"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface contractAttributes {
    id: number;
    name: string;
    type: string;
    department_id: number;
    organization_id: string;
    status: string;
    created_by: number;
    updated_by: number;
}

export class EmpContractCategory
    extends Model<contractAttributes, never>
    implements contractAttributes {
    id!: number;
    name!: string;
    type!: string;
    department_id!: number;
    organization_id!: string;
    status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    DELETED = "deleted"
}

export enum type {
    GENERAL = "general",
    DEPT = "department"
}

EmpContractCategory.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        type: {
            type: DataTypes.ENUM,
            values: Object.values(type)
        },
        name: {
            type: DataTypes.STRING
        },
        department_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        organization_id: {
            type: DataTypes.STRING,
            allowNull: true
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
            defaultValue: status.ACTIVE,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_contract_category",
        modelName: "EmpContractCategory",
    },
);

// Define hooks for HRMC model
EmpContractCategory.addHook("afterUpdate", async (empContractCategory: any) => {
    await addActivity("EmpContractCategory", "updated", empContractCategory);
});

EmpContractCategory.addHook("afterCreate", async (empContractCategory: EmpContractCategory) => {
    await addActivity("EmpContractCategory", "created", empContractCategory);
});

EmpContractCategory.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});


