"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { BannerConfig } from "./BannerConfig";

interface bannerNotificationAttributes {
    id: number;
    title: string;
    description: string;
    extra_information: string | null;
    banner_config_id: number;
    created_by: number;
    read_notification: boolean;
    updated_by: number | null;
    createdAt: Date;
    updatedAt: Date;
}

export class BannerNotification
    extends Model<bannerNotificationAttributes, never>
    implements bannerNotificationAttributes {
    id!: number;
    title!: string;
    description!: string;
    extra_information!: string | null;
    banner_config_id!: number;
    created_by!: number;
    read_notification!: boolean;
    updated_by!: number | null;
    createdAt!: Date;
    updatedAt!: Date;
}

BannerNotification.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        title: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        description: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
        },
        extra_information: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        banner_config_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        read_notification: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },
        updated_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_banner_notifications",
        modelName: "BannerNotification",
    }
);

BannerNotification.belongsTo(BannerConfig, { foreignKey: "banner_config_id", });
BannerConfig.hasMany(BannerNotification, { foreignKey: "banner_config_id" });
