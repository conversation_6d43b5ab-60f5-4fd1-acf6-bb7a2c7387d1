"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface forecastAttributes {
    id: number;
    forecast_year: number;
    forecast_due_date: Date;
    branch_id: number;
    forecast_status: string;
    created_by: number;
    updated_by: number;
}

export enum forecast_status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DELETED = 'deleted',
    ASSIGNED = 'assigned',
    SUBMITTED = 'submitted',
    APPROVED = 'approved',
    REVOKED = 'revoked'
}

export class Forecast
    extends Model<forecastAttributes, never>
    implements forecastAttributes {
    id!: number;
    forecast_year!: number;
    forecast_due_date!: Date;
    branch_id!: number
    forecast_status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

Forecast.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        forecast_year: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        branch_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        forecast_status: {
            type: DataTypes.ENUM,
            values: Object.values(forecast_status),
            defaultValue: forecast_status.ACTIVE,
        },
        forecast_due_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_forecast",
        modelName: "Forecast",
    },
);



// Define hooks for Card model
Forecast.addHook("afterUpdate", async (forecast: any) => {
    await addActivity("Forecast", "updated", forecast);
});

Forecast.addHook("afterCreate", async (forecast: Forecast) => {
    await addActivity("Forecast", "created", forecast);
});

Forecast.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});
