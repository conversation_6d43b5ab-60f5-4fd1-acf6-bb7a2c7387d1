"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { fixed_types } from "./UserMeta";

interface userEmploymentContractAttributes {
    id: number;
    user_id: number;
    contract_with_sign: string;
    is_confirm_sign: boolean;
    contract_status: string;
    general_template: number;
    department_template: number;
    additional_template: string;
    tips_grade: string;
    expire_date: Date;
    start_date: Date;
    other: string;
    expire_duration: string;
    fixed_types: string;
    wages_hours: number;
    leave_policy_id: number;
    probation_length: number;
    working_hours: number;
    duration_type: string;
    wage_type: string;
    contract_remark: string; //remark
    contract_name: string
    leave_type_id: number;
    leave_days: number;
    leave_remark: string;
    leave_duration_type: string;
    contract_type: number;
    place_of_work: string;
    working_hour_per_day: number;
    max_limit_per_week: number;
    contract_name_id: number;
    has_holiday_entitlement: boolean;
    holiday_entitlement_remark: string;
    created_by: number;
    updated_by: number;
}

export enum contract_status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DELETED = "deleted"
}
export enum durationType {
    WEEKLY = "week",
    MONTHLY = "month"
}

export enum wageType {
    HOURS = "hours",
    FIXED = "fixed"
}
export enum leave_duration_type {
    HOURS = "Hours",
    DAYS = "Days",
}

export enum leave_calculation_type {
    FIXED = "fixed",
    POLICY = "policy",
    FORMULA = "formula"
}



export class UserEmploymentContract
    extends Model<userEmploymentContractAttributes, never>
    implements userEmploymentContractAttributes {
    id!: number;
    user_id!: number;
    contract_with_sign!: string;
    is_confirm_sign!: boolean;
    contract_status!: string;
    general_template!: number;
    department_template!: number;
    additional_template!: string;
    tips_grade!: string;
    expire_date!: Date;
    start_date!: Date;
    other!: string;
    expire_duration!: string;
    fixed_types!: string;
    wages_hours!: number;
    leave_policy_id!: number;
    probation_length!: number;
    working_hours!: number;
    duration_type!: string;
    wage_type!: string;
    contract_remark!: string; //remark
    contract_name!: string
    leave_type_id!: number;
    leave_days!: number;
    leave_remark!: string;
    leave_duration_type!: string;
    contract_type!: number;
    place_of_work!: string;
    working_hour_per_day!: number;
    max_limit_per_week!: number;
    contract_name_id!: number;
    has_holiday_entitlement!: boolean;
    holiday_entitlement_remark!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

UserEmploymentContract.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        user_id: {
            type: DataTypes.INTEGER
        },
        is_confirm_sign: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        contract_with_sign: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        contract_status: {
            type: DataTypes.ENUM,
            values: Object.values(contract_status),
            allowNull: true,
        },
        general_template: {
            type: DataTypes.INTEGER
        },
        department_template: {
            type: DataTypes.INTEGER
        },
        additional_template: {
            type: DataTypes.TEXT('long')
        },
        tips_grade: {
            type: DataTypes.STRING,
            allowNull: true
        },
        expire_date: {
            type: DataTypes.DATE
        },
        start_date: {
            type: DataTypes.DATE
        },
        other: {
            type: DataTypes.TEXT('long')
        },
        expire_duration: {
            type: DataTypes.STRING
        },
        fixed_types: {
            type: DataTypes.ENUM,
            values: Object.values(fixed_types),
            allowNull: true
        },
        wages_hours: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        leave_policy_id: {
            type: DataTypes.INTEGER
        },
        probation_length: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        working_hours: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        duration_type: {
            type: DataTypes.ENUM,
            values: Object.values(durationType),
            allowNull: true
        },
        wage_type: {
            type: DataTypes.ENUM,
            values: Object.values(wageType),
            allowNull: true
        },
        contract_remark: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        contract_name: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        leave_type_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        leave_days: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        leave_remark: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        leave_duration_type: {
            type: DataTypes.ENUM,
            values: Object.values(leave_duration_type),
            defaultValue: leave_duration_type.DAYS
        },
        contract_type: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        place_of_work: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        working_hour_per_day: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        max_limit_per_week: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        contract_name_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        has_holiday_entitlement: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        holiday_entitlement_remark: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_user_employment_contract",
        modelName: "UserEmploymentContract",
    },
);

// UserEmploymentContract.removeAttribute("id");
// UserEmploymentContract.belongsTo(User, { foreignKey: "user_id", as: "user_employment_contract" });
// User.hasOne(UserEmploymentContract, {
//   foreignKey: "user_id",
//   as: "user_contract",
// });


UserEmploymentContract.addHook("afterUpdate", async (userEmploymentContract: any) => {
    await addActivity("User Employment Contract", "updated", userEmploymentContract);
});

UserEmploymentContract.addHook("afterCreate", async (userEmploymentContract: UserEmploymentContract) => {
    await addActivity("User Employment Contract", "created", userEmploymentContract);
});

UserEmploymentContract.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

