import { Router } from "express";
import cardController from "../../controller/card.controller";
import cardValidator from "../../validators/card.validator";
const router: Router = Router();

// add card 
router.post("/add-card",cardValidator.addCard() ,cardController.addCard);

// update card
router.put("/update-card/:card_id",cardValidator.updateCard() ,cardController.updateCard);

// get card
router.get("/get-card-list" ,cardController.getAllCard);

// get card
router.get("/get-one-card/:card_id" ,cardController.getCardById);

// delete  card
router.delete("/delete-card/:card_id" ,cardController.deleteCard);

// get card
router.get("/get-card-by-branch/:branch_id" ,cardController.getCardByBranch);

// update card order
router.put("/update-card-order/:branch_id" ,cardController.updateCardOrder);


export default router;
