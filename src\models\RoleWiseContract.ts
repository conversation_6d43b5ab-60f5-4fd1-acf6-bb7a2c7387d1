"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { Role } from "./Role";

interface roleWiseContractAttributes {
    role_id: number;
    employee_duties: string;
    employee_content: string;
    status: string;
    created_by: number;
    updated_by: number;
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
}

export class RoleWiseContract
    extends Model<roleWiseContractAttributes, never>
    implements roleWiseContractAttributes {
    role_id!: number;
    employee_duties!: string;
    employee_content!: string;
    status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

RoleWiseContract.init(
    {
        role_id: {
            type: DataTypes.INTEGER
        },
        employee_duties: {
            type: DataTypes.TEXT,
            defaultValue: true
        },
        employee_content: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
            allowNull: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_role_wise_contract",
        modelName: "RoleWiseContract",
    },
);

RoleWiseContract.removeAttribute("id");
RoleWiseContract.belongsTo(Role, { foreignKey: "role_id", as: "role_wise" });
Role.hasOne(RoleWiseContract, {
    foreignKey: "role_id",
    as: "role_wise_content",
});


RoleWiseContract.addHook("afterUpdate", async (roleWiseContract: any) => {
    await addActivity("User Employment Contract", "updated", roleWiseContract);
});

RoleWiseContract.addHook("afterCreate", async (roleWiseContract: RoleWiseContract) => {
    await addActivity("User Employment Contract", "created", roleWiseContract);
});

RoleWiseContract.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

