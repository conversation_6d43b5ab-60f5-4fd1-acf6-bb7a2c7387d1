"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { Dashboard } from "./Dashboard";


interface dashboardModelAttributes {
    id: number;
    model_title: string;
    model_status: string;
    model_type: string;
    xaxis_list: string;
    yaxis_list: string;
    xaxis_value: string;
    yaxis_value: string;
    dashboard_id: number;
    model_order: number;
    created_by: number;
    updated_by: number;
}

export enum model_status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DELETED = 'deleted'
}

export class DashboardModel
    extends Model<dashboardModelAttributes, never>
    implements dashboardModelAttributes {
    id!: number;
    model_title!: string;
    model_status!: string;
    model_type!: string;
    xaxis_list!: string;
    yaxis_list!: string;
    xaxis_value!: string;
    yaxis_value!: string;
    dashboard_id!: number;
    model_order!: number;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

DashboardModel.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        model_title: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        model_status: {
            type: DataTypes.ENUM,
            values: Object.values(model_status),
            defaultValue: model_status.ACTIVE,
        },
        xaxis_list: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        yaxis_list: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        xaxis_value: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        yaxis_value: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        model_type: {
            type: DataTypes.STRING(50),
            allowNull: true
        },
        dashboard_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        model_order: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_dashboard_models",
        modelName: "DashboardModel",
    },
);

Dashboard.hasMany(DashboardModel, { foreignKey: 'dashboard_id', as: 'dashboard_models' });
DashboardModel.belongsTo(Dashboard, { foreignKey: 'dashboard_id' });


// Define hooks for Card model
DashboardModel.addHook("afterUpdate", async (dashboardModel: any) => {
    await addActivity("DashboardModel", "updated", dashboardModel);
});

DashboardModel.addHook("afterCreate", async (dashboardModel: DashboardModel) => {
    await addActivity("DashboardModel", "created", dashboardModel);
});

DashboardModel.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

