"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface categoryAttributes {
  id: number;
  category_name: string;
  category_description: string;
  category_status: string;
  dashboard_view: boolean;
  created_by: number;
  updated_by: number;
}

export enum category_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class Category
  extends Model<categoryAttributes, never>
  implements categoryAttributes {
  id!: number;
  category_name!: string;
  category_description!: string;
  category_status!: string;
  dashboard_view!: boolean;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Category.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    category_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    dashboard_view: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    category_description: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    category_status: {
      type: DataTypes.ENUM,
      values: Object.values(category_status),
      defaultValue: category_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_category",
    modelName: "Category",
  },
);

// Define hooks for Category model
Category.addHook("afterUpdate", async (category: any) => {
  await addActivity("Category", "updated", category);
});

Category.addHook("afterCreate", async (category: Category) => {
  await addActivity("Category", "created", category);
});

Category.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

