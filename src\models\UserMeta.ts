"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

export enum fixed_types {
    YEARLY = "annum",
    QUARTLY = "quarterly",
    MONTHLY = "month",
    WEEKLY = "week",
    DAILY = "day",
}
export enum durationType {
    WEEKLY = "week",
    MONTHLY = "month"
}

export enum wageType {
    HOURS = "hours",
    FIXED = "fixed"
}
export enum leave_duration_type {
    HOURS = "Hours",
    DAYS = "Days",
}
export enum leave_calculation_type {
    FIXED = "fixed",
    POLICY = "policy",
    FORMULA = "formula"
}



interface userMetaAttributes {
    user_id: number;
    general_template: number;
    department_template: number;
    additional_template: string;
    tips_grade: string;
    start_date: Date;
    expire_date: Date;
    other: string;
    expire_duration: string;
    fixed_types: string;
    wages_hours: number;
    leave_policy_id: number;
    isDraft: boolean;
    probation_length: number;
    working_hours: number;
    duration_type: string;
    wage_type: string;
    contract_remark: string; //remark
    contract_name: string;
    leave_type_id: number;
    leave_days: number;
    leave_remark: string;
    leave_duration_type: string;
    contract_type: number;
    place_of_work: string;
    working_hour_per_day: number;
    max_limit_per_week: number;
    contract_name_id: number;
    has_holiday_entitlement: boolean;
    holiday_entitlement_remark: string;
    created_by: number;
    updated_by: number;
}

export class UserMeta
    extends Model<userMetaAttributes, never>
    implements userMetaAttributes {
    user_id!: number;
    general_template!: number;
    department_template!: number;
    additional_template!: string;
    tips_grade!: string;
    start_date!: Date;
    expire_date!: Date;
    other!: string;
    expire_duration!: string;
    fixed_types!: string;
    wages_hours!: number;
    leave_policy_id!: number;
    isDraft!: boolean;
    probation_length!: number;
    working_hours!: number;
    duration_type!: string;
    wage_type!: string;
    contract_remark!: string; //remark
    contract_name!: string;
    leave_type_id!: number;
    leave_days!: number;
    leave_remark!: string;
    leave_duration_type!: string;
    contract_type!: number;
    place_of_work!: string;
    working_hour_per_day!: number;
    max_limit_per_week!: number;
    contract_name_id!: number;
    has_holiday_entitlement!: boolean;
    holiday_entitlement_remark!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

UserMeta.init(
    {
        user_id: {
            type: DataTypes.INTEGER
        },
        general_template: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        department_template: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        additional_template: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        tips_grade: {
            type: DataTypes.STRING,
            allowNull: true
        },
        start_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        expire_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        other: {
            type: DataTypes.TEXT('long')
        },
        expire_duration: {
            type: DataTypes.STRING
        },
        fixed_types: {
            type: DataTypes.ENUM,
            values: Object.values(fixed_types),
            allowNull: true
        },
        wages_hours: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        leave_policy_id: {
            type: DataTypes.INTEGER
        },
        isDraft: {
            type: DataTypes.BOOLEAN,
            defaultValue: 0
        },
        probation_length: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        working_hours: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        duration_type: {
            type: DataTypes.ENUM,
            values: Object.values(durationType),
            allowNull: true
        },
        wage_type: {
            type: DataTypes.ENUM,
            values: Object.values(wageType),
            allowNull: true
        },
        contract_remark: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        contract_name: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        leave_type_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        leave_days: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        leave_remark: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        leave_duration_type: {
            type: DataTypes.ENUM,
            values: Object.values(leave_duration_type),
            defaultValue: leave_duration_type.DAYS
        },
        contract_type: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        place_of_work: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        working_hour_per_day: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        max_limit_per_week: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        contract_name_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        has_holiday_entitlement: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        holiday_entitlement_remark: {
            type: DataTypes.TEXT('long'),
            allowNull: true
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_user_meta",
        modelName: "UserMeta",
    },
);

UserMeta.removeAttribute("id");

// User.hasOne(UserMeta, {
//     foreignKey: "user_id",
//     as: "user_meta",
// });
// UserMeta.belongsTo(User, { foreignKey: "user_id", as: "user_meta" });
// UserMeta.belongsTo(ContractTypeModel, { foreignKey: "contract_type", as: "user_contract_type_list" });
// ContractTypeModel.hasMany(UserMeta, {
//   foreignKey: "contract_type",
//   as: "contract_type_list",
// });

UserMeta.addHook("afterUpdate", async (userMeta: any) => {
    await addActivity("User Meta", "updated", userMeta);
});

UserMeta.addHook("afterCreate", async (userMeta: UserMeta) => {
    await addActivity("User Meta", "created", userMeta);
});

UserMeta.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

