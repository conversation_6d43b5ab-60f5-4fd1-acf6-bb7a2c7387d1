"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface paymentTypeRemarkAttributes {
  detail_id: number;
  payment_type_id: number;
  payment_type_remark: string;
  created_by: number;
  updated_by: number;
}

export class PaymentTypeRemark
  extends Model<paymentTypeRemarkAttributes, never>
  implements paymentTypeRemarkAttributes {
  detail_id!: number;
  payment_type_id!: number;
  payment_type_remark!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PaymentTypeRemark.init(
  {
    detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_remark: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },

  },
  {
    sequelize: sequelize,
    tableName: "nv_payment_type_remarks",
    modelName: "PaymentTypeRemark",
    timestamps: false,
  },
);

PaymentTypeRemark.removeAttribute("id");

// Define hooks for leave type model
PaymentTypeRemark.addHook("afterUpdate", async (PaymentTypeRemark: any) => {
  await addActivity("PaymentTypeRemark", "updated", PaymentTypeRemark);
});

PaymentTypeRemark.addHook("afterCreate", async (paymentTypeRemark: PaymentTypeRemark) => {
  await addActivity("PaymentTypeRemark", "created", paymentTypeRemark);
});

PaymentTypeRemark.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});


