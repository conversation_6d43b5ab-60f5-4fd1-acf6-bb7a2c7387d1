"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { Playlist } from "./Playlist";
import { Department } from "./Department";
import { addActivity } from "../helper/queue.service";

interface playlistDepartmentAttributes {
  playlist_id: number;
  department_id: number;
  playlist_department_status: string;
  created_by: number;
  updated_by: number;
}

export enum playlist_department_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class PlaylistDepartment
  extends Model<playlistDepartmentAttributes, never>
  implements playlistDepartmentAttributes {
  playlist_id!: number;
  department_id!: number;
  playlist_department_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PlaylistDepartment.init(
  {
    playlist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    playlist_department_status: {
      type: DataTypes.ENUM,
      values: Object.values(playlist_department_status),
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_playlist_department",
    modelName: "PlaylistDepartment",
  },
);

PlaylistDepartment.removeAttribute("id");
Department.hasMany(PlaylistDepartment, {
  foreignKey: "department_id",
  as: "department",
});
PlaylistDepartment.belongsTo(Department, {
  foreignKey: "department_id",
  as: "department",
});
Playlist.hasMany(PlaylistDepartment, { foreignKey: "playlist_id" });
PlaylistDepartment.belongsTo(Playlist, { foreignKey: "playlist_id" });

PlaylistDepartment.addHook("afterUpdate", async (playlistDepartment: any) => {
  await addActivity("PlaylistDepartment", "updated", playlistDepartment);
});

PlaylistDepartment.addHook(
  "afterCreate",
  async (playlistDepartment: PlaylistDepartment) => {
    await addActivity("PlaylistDepartment", "created", playlistDepartment);
  },
);

PlaylistDepartment.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

