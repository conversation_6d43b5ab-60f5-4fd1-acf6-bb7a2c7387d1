"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface changeRequestAttributes {
  id: number;
  change_request_subject: string;
  user_id: number;
  old_data: string;
  new_data: string;
  change_request_files: string;
  change_request_status: string;
  created_by: number;
  updated_by: number;
}


export enum change_request_status {
  PENDING = 'pending',
  REOPENED = 'reopened',
  REJECTED = 'rejected',
  APPROVED = 'approved',
  CANCELLED = "cancelled",
  DELETED = 'deleted',
  CLOSED = 'closed'
}

export class ChangeRequest
  extends Model<changeRequestAttributes, never>
  implements changeRequestAttributes {
  id!: number;
  change_request_subject!: string;
  user_id!: number;
  old_data!: string;
  new_data!: string;
  change_request_files!: string;
  change_request_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

ChangeRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    change_request_subject: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    old_data: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    new_data: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    change_request_files: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    change_request_status: {
      type: DataTypes.ENUM,
      values: Object.values(change_request_status),
      defaultValue: change_request_status.PENDING,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_change_request",
    modelName: "ChangeRequest",
  },
);

// ChangeRequest.belongsTo(User, {
//   foreignKey: "user_id",
//   as: "change_request_user",
// });
// User.hasMany(ChangeRequest, {
//   foreignKey: "user_id",
//   as: "change_request_user_detail",
// });



ChangeRequest.addHook("afterUpdate", async (changeRequest: any) => {
  await addActivity("Change Request", "updated", changeRequest);
});

ChangeRequest.addHook("afterCreate", async (changeRequest: ChangeRequest) => {
  await addActivity("Change Request", "created", changeRequest);
});

ChangeRequest.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
