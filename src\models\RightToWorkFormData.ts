"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface rightToWorkFormDataAttributes {
  id: number;
  type: string;
  field_type: string;
  header: string;
  footer: string;
  title: string;
  sub_title: string;
  created_by: number;
  updated_by: number;
}

export class RightToWorkFormData
  extends Model<rightToWorkFormDataAttributes, never>
  implements rightToWorkFormDataAttributes {
  id!: number;
  type!: string;
  field_type!: string;
  header!: string;
  footer!: string;
  title!: string;
  sub_title!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}


export enum type {
  STEP1_LISTA = "step1_listA",
  STEP2_LISTB_GROUP1 = "step1_listB_group1",
  STEP2_LISTB_GROUP2 = "step1_listB_group2",
  STEP2_CHECK = "step2_check",
  STEP3_COPY = "step3_copy",
  KNOW_TYPE_OF_EXCUSE = "know_type_of_excuse"
}


export enum field_type {
  CHECKBOX = "checkbox",
  OPTION = "option"

}

RightToWorkFormData.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    type: {
      type: DataTypes.ENUM,
      values: Object.values(type),
      allowNull: true
    },
    field_type: {
      type: DataTypes.ENUM,
      values: Object.values(field_type),
      allowNull: true
    },
    header: {
      type: DataTypes.TEXT,
    },
    footer: {
      type: DataTypes.TEXT,
    },
    title: {
      type: DataTypes.STRING,
    },
    sub_title: {
      type: DataTypes.STRING,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_right_work_form_data",
    modelName: "RightToWorkFormData",
  },
);



RightToWorkFormData.addHook(
  "afterUpdate",
  async (rightToWorkFormData: any) => {
    await addActivity(
      "RightToWorkFormData",
      "updated",
      rightToWorkFormData,
    );
  },
);

RightToWorkFormData.addHook(
  "afterCreate",
  async (rightToWorkFormData: RightToWorkFormData) => {
    await addActivity(
      "RightToWorkFormData",
      "created",
      rightToWorkFormData,
    );
  },
);

RightToWorkFormData.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
