"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";



interface policySettingAttributes {
    id: number;
    policy_type: string;
    allow_past_date_leave: boolean;
    apply_leave_upto_limit: boolean;
    leave_upto_limit: number;
    policy_request_require_approval: boolean;
    display_user_list_in_calender: boolean
    organization_id: string
}


export class PolicySetting
    extends Model<policySettingAttributes, never>
    implements policySettingAttributes {
    id!: number;
    policy_type!: string;
    allow_past_date_leave!: boolean;
    apply_leave_upto_limit!: boolean;
    leave_upto_limit!: number;
    policy_request_require_approval!: boolean;
    display_user_list_in_calender!: boolean;
    organization_id!: string

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

export enum policy_type {
    HOLIDAY = "holiday",
    LEAVE = "leave",
}

PolicySetting.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        policy_type: {
            type: DataTypes.ENUM,
            values: Object.values(policy_type),
            allowNull: true,
        },
        allow_past_date_leave: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        apply_leave_upto_limit: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        leave_upto_limit: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        policy_request_require_approval: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        display_user_list_in_calender: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        organization_id: {
            type: DataTypes.STRING,
            allowNull: true
        }
    },
    {
        sequelize: sequelize,
        tableName: "nv_policy_setting",
        modelName: "PolicySetting",
    },
);



PolicySetting.addHook("afterUpdate", async (PolicySetting: any) => {
    await addActivity("PolicySetting", "updated", PolicySetting);
});

PolicySetting.addHook("afterCreate", async (policySetting: PolicySetting) => {
    await addActivity("PolicySetting", "created", policySetting);
});

PolicySetting.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

