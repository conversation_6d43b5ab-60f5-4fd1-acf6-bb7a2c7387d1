import { Segments, <PERSON><PERSON>, celebrate } from "celebrate";

export default {
    createModule: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                module: Joi.string().required(),
                module_name: Joi.string().required(),
            }),
        }),
    updateModule: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                module: Joi.string().allow(null, ""),
                module_name: Joi.string().allow(null, ""),
            }),
        }),
};
