"use strict";
const { QueryTypes } = require("sequelize");

module.exports = {
  async up(queryInterface, Sequelize) {
    const getReportFilterData = await queryInterface.sequelize.query(
      `SELECT * FROM nv_contract_name`,
      { type: QueryTypes.SELECT },
    );

    if (getReportFilterData.length === 0) {
      const insertData = [
        {
          contract_name: "Full Time",
          created_by: 1,
          updated_by: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          contract_name: "Part Time",
          created_by: 1,
          updated_by: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          contract_name: "Flexible",
          created_by: 1,
          updated_by: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        }];
      await queryInterface.bulkInsert("nv_contract_name", insertData, {});
    }
  },
};