import { payment_type_category_field_status as paymentTypeCategoryFieldStatus, PaymentTypeCategoryField } from "../models/PaymentTypeCategoryField";
import { payment_type_category_value_status, PaymentTypeCategoryValue, payment_type_category_value_status as paymentTypeCategoryValueStatus } from "../models/PaymentTypeCategoryValue";
import { PaymentTypeCategory, payment_type_category_status as paymentTypeCategoryStatus, payment_type_category_pattern as paymentTypeCategoryPattern } from "../models/PaymentTypeCategory";
import { PaymentTypeCategoryBranch, payment_type_category_branch_status as paymentTypeCategoryBranchStatus, category_branch_type as categoryBranchType } from "../models/PaymentTypeCategoryBranch";
import { PaymentType, payment_type_status as paymentTypeStatus, payment_type_usage as paymentTypeUsage } from "../models/PaymentType";
import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { Branch, branch_status } from "../models/Branch";
import { sequelize } from "../models";
import { dsr_item_status, DsrItem } from "../models/DsrItem";
import { wsr_item_status, WsrItem } from "../models/WsrItem";



const createPaymentType = async (req: Request, res: Response) => {
    try {
        const { payment_type_title, payment_type_status, payment_type_usage, has_weekly_use, has_include_amount = false, has_field_currency = true }: any = req.body
        const checkSameTitleExist = await PaymentType.findOne({ where: { organization_id: req.user.organization_id, payment_type_title, payment_type_status: { [Op.not]: paymentTypeStatus.DELETED } }, raw: true })
        if (checkSameTitleExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("CANNOT_CREATE_SAME_PAYMENT_TYPE")
            })
        }

        const findPaymentTypeOrder = await PaymentType.findOne({
            order: [['payment_type_order', 'DESC']],
            attributes: ['payment_type_order'],
            where: { organization_id: req.user.organization_id, payment_type_status: { [Op.not]: paymentTypeStatus.DELETED } }
        })
        const paymentTypeObj: any = {
            payment_type_title,
            payment_type_status,
            payment_type_usage,
            has_include_amount,
            has_field_currency,
            organization_id: req.user.organization_id,
            created_by: req.user.id,
            updated_by: req.user.id
        }
        if (payment_type_usage != paymentTypeUsage.EXPENSE) {
            paymentTypeObj.has_weekly_use = has_weekly_use
        }
        paymentTypeObj.payment_type_order = findPaymentTypeOrder ? findPaymentTypeOrder.payment_type_order + 1 : 1
        const createPaymentType = await PaymentType.create(paymentTypeObj)
        if (createPaymentType) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("PAYMENT_TYPE_CREATED")
            });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_PAYMENT_CREATED")
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const updatePaymentType = async (req: Request, res: Response) => {
    try {
        const { payment_type_title, payment_type_status, payment_type_usage, has_weekly_use, has_include_amount, has_field_currency }: any = req.body
        const { payment_type_id } = req.params
        const findPaymentType = await PaymentType.findOne({ where: { id: payment_type_id, organization_id: req.user.organization_id, payment_type_status: { [Op.not]: paymentTypeStatus.DELETED } }, raw: true })
        if (!findPaymentType) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PAYMENT_TYPE_NOT_FOUND")
            })
        }
        const checkPaymentTypeTitleExist = await PaymentType.findOne({ where: { payment_type_title, organization_id: req.user.organization_id, payment_type_status: { [Op.not]: paymentTypeStatus.DELETED } }, raw: true })
        if (checkPaymentTypeTitleExist && checkPaymentTypeTitleExist.id != findPaymentType.id) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("CANNOT_CREATE_SAME_PAYMENT_TYPE")
            })
        }
        const paymentTypeObj: any = {
            payment_type_title,
            payment_type_status,
            payment_type_usage,
            has_include_amount,
            has_field_currency,
            updated_by: req.user.id
        }
        if (payment_type_usage != paymentTypeUsage.EXPENSE) {

            if (findPaymentType.has_weekly_use != has_weekly_use) {

                const findDsrItem = await DsrItem.findAll({
                    where: {
                        payment_type_category_id: {
                            [Op.in]: sequelize.literal(`(SELECT nv_payment_type_category_branch.payment_type_category_id  
                            FROM nv_payment_type_category_branch 
                            WHERE nv_payment_type_category_branch.payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}' AND 
                            nv_payment_type_category_branch.payment_type_category_id IN(SELECT id FROM nv_payment_type_category WHERE payment_type_id = ${findPaymentType.id} AND payment_type_category_status = '${paymentTypeCategoryStatus.ACTIVE}')
                    )`)  // Make sure subquery is wrapped inside parentheses
                        }, dsr_item_status: dsr_item_status.ACTIVE
                    }
                })
                const findWsrItem = await WsrItem.findAll({
                    where: {
                        payment_type_category_id: {
                            [Op.in]: sequelize.literal(`(SELECT nv_payment_type_category_branch.payment_type_category_id  
                            FROM nv_payment_type_category_branch 
                            WHERE nv_payment_type_category_branch.payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}' AND 
                            nv_payment_type_category_branch.payment_type_category_id IN(SELECT id FROM nv_payment_type_category WHERE payment_type_id = ${findPaymentType.id} AND payment_type_category_status = '${paymentTypeCategoryStatus.ACTIVE}')
                    )`)  // Make sure subquery is wrapped inside parentheses
                        }, wsr_item_status: wsr_item_status.ACTIVE
                    }
                })

                if (findDsrItem.length > 0 || findWsrItem.length > 0) {
                    return res.status(StatusCodes.BAD_REQUEST).json({
                        status: false,
                        message: res.__("FAIL_PAYMENT_ALREADY_USED")
                    });
                }
            }
            paymentTypeObj.has_weekly_use = has_weekly_use
        }
        const updatePaymentType = await PaymentType.update(paymentTypeObj, { where: { id: findPaymentType.id } })
        if (updatePaymentType.length > 0) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("PAYMENT_TYPE_UPDATED")
            });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_PAYMENT_UPDATED")
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getPaymentTypeById = async (req: Request, res: Response) => {
    try {
        const { payment_type_id } = req.params
        const findPaymentType = await PaymentType.findOne({ attributes: ['id', 'payment_type_title', 'payment_type_status', 'payment_type_usage', 'has_weekly_use', 'has_include_amount', 'has_field_currency'], where: { id: payment_type_id, organization_id: req.user.organization_id, payment_type_status: { [Op.not]: paymentTypeStatus.DELETED } }, raw: true })
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: findPaymentType || {}
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getPaymentTypeList = async (req: Request, res: Response) => {
    try {
        const { payment_type_status, search, page, size }: any = req.query
        const { limit, offset } = getPagination(Number(page), Number(size));
        const paymentTypeListObj: any = { attributes: ['id', 'payment_type_title', 'payment_type_status', 'payment_type_usage', 'has_weekly_use', 'has_include_amount', 'has_field_currency'] }
        const whereObj: any = { organization_id: req.user.organization_id, payment_type_status: { [Op.not]: paymentTypeStatus.DELETED } }
        if (search) {
            whereObj.payment_type_title = { [Op.like]: `%${search}%` }
        }
        if (payment_type_status) {
            whereObj.payment_type_status = payment_type_status
        }
        if (page && size) {
            paymentTypeListObj.limit = Number(limit);
            paymentTypeListObj.offset = Number(offset);
        }
        paymentTypeListObj.where = whereObj
        paymentTypeListObj.order = [['payment_type_order', 'ASC']]
        const { rows: getPaymentList, count } = await PaymentType.findAndCountAll(paymentTypeListObj)
        const { total_pages } = getPaginatedItems(
            size,
            page,
            count || 0,
        );
        if (getPaymentList.length > 0) {
            res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: getPaymentList,
                count: count,
                page: parseInt(page),
                size: parseInt(size),
                total_pages,
            });
        } else {
            res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: [],
                count: 0,
                page: 0,
                size: 0,
                total_pages: 0,
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

/**
 *  update PaymentType Order
 * @param req
 * @param res
 * @returns
 */

const updatePaymentTypeOrder = async (req: any, res: any) => {
    try {
        const { payment_type_id, order } = req.body;

        const findPaymentType = await PaymentType.findOne({
            where: {
                organization_id: req.user.organization_id,
                id: payment_type_id,
                payment_type_status: { [Op.not]: paymentTypeStatus.DELETED },
            },
        });
        if (!findPaymentType) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("PAYMENT_TYPE_NOT_FOUND") });
        }

        const paymentTypeObj: any = {
            where: {
                organization_id: req.user.organization_id,
                payment_type_status: { [Op.not]: paymentTypeStatus.DELETED },
            },
            attributes: [
                'id', 'payment_type_order'
            ],
            order: [['payment_type_order', 'ASC']]
        }


        const categories = await PaymentType.findAll(paymentTypeObj);
        const oldPosition = findPaymentType.payment_type_order;
        const newPosition = order
        categories.forEach(async item => {
            let new_order = item.payment_type_order
            if (item.id !== payment_type_id) {

                // If the item's position is between the old position and new position
                if (oldPosition < newPosition && item.payment_type_order > oldPosition && item.payment_type_order <= newPosition) {
                    new_order = item.payment_type_order - 1; // Move up
                }
                if (oldPosition > newPosition && item.payment_type_order < oldPosition && item.payment_type_order >= newPosition) {
                    new_order = item.payment_type_order + 1; // Move down
                }


            } else { new_order = order }
            await PaymentType.setHeaders(req).update(
                {
                    payment_type_order: new_order,
                    updated_by: req.user.id
                },
                {
                    where: {
                        id: item.id
                    },
                },
            );
        });

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("PAYMENT_TYPE_ORDER_UPDATE"),
        });

    } catch (e: any) {
        console.log('Exception', e);
        return res.send({ status: false, message: e.message });
    }
};


const createPaymentTypeCategory = async (req: Request, res: Response) => {
    try {
        const { payment_type_category_title, payment_type_category_status, payment_type_category_pattern, payment_type_category_data_type, payment_type_category_fields = [], payment_type_category_remarks }: any = req.body
        const { payment_type_id } = req.params
        const findPaymentType = await PaymentType.findOne({ where: { id: payment_type_id, payment_type_status: paymentTypeStatus.ACTIVE, organization_id: req.user.organization_id }, raw: true })
        if (!findPaymentType) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PAYMENT_TYPE_NOT_FOUND")
            })
        }
        const findPaymentTypeCategory = await PaymentTypeCategory.findOne({ where: { payment_type_id, payment_type_category_title, payment_type_category_status: { [Op.not]: paymentTypeCategoryStatus.DELETED } }, raw: true })
        if (findPaymentTypeCategory) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PAYMENT_CATEGORY_EXIST_PAYMENT_TYPE")
            })
        }
        const findPaymentTypeCategoryOrder = await PaymentTypeCategory.findOne({
            order: [['payment_type_category_order', 'DESC']],
            attributes: ['payment_type_category_order'],
            raw: true
        })
        const createPaymentTypeCategoryObj: any = {
            payment_type_id: findPaymentType.id,
            payment_type_category_title,
            payment_type_category_status,
            payment_type_category_pattern,
            payment_type_category_remarks,
            created_by: req.user.id,
            updated_by: req.user.id
        }
        createPaymentTypeCategoryObj.payment_type_category_order = findPaymentTypeCategoryOrder ? findPaymentTypeCategoryOrder.payment_type_category_order + 1 : 1
        if (payment_type_category_pattern == paymentTypeCategoryPattern.SINGLE) {
            createPaymentTypeCategoryObj.payment_type_category_data_type = payment_type_category_data_type
        }
        const createPaymentTypeCategory = await PaymentTypeCategory.create(createPaymentTypeCategoryObj)
        if (createPaymentTypeCategory) {
            if (createPaymentTypeCategory.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                for (const fields of payment_type_category_fields) {
                    const fieldObj: any = {
                        payment_type_category_id: createPaymentTypeCategory.id,
                        field_name: fields?.field_name,
                        field_type: fields?.field_type,
                        field_limit: fields?.field_limit,
                        payment_type_category_field_status: paymentTypeCategoryFieldStatus.ACTIVE,
                        created_by: req.user.id,
                        updated_by: req.user.id
                    }
                    await PaymentTypeCategoryField.create(fieldObj)
                }
            }
            const getBranchList = await Branch.findAll({ where: { organization_id: req.user.organization_id, branch_status: branch_status.ACTIVE }, raw: true })
            if (getBranchList.length > 0) {
                const branch_ids = getBranchList.map((branch) => { return branch.id })
                for (const branch_id of branch_ids) {
                    await PaymentTypeCategoryBranch.create({
                        payment_type_category_id: createPaymentTypeCategory.id, branch_id, payment_type_category_branch_status: createPaymentTypeCategory.payment_type_category_status, category_branch_type: categoryBranchType.SEPARATE, created_by: req.user.id, updated_by: req.user.id
                    } as any)
                }
            }
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("PAYMENT_CATEGORY_CREATED")
            })
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_PAYMENT_CATEGORY_CREATED")
            })
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const updatePaymentTypeCategory = async (req: Request, res: Response) => {
    try {
        const { payment_type_category_title, payment_type_category_status, payment_type_category_data_type, payment_type_category_fields = [], payment_type_category_remarks }: any = req.body
        const { payment_type_category_id } = req.params

        const findPaymentType = await PaymentTypeCategory.findOne({ where: { id: payment_type_category_id, payment_type_category_status: { [Op.not]: paymentTypeCategoryStatus.DELETED } }, raw: true })
        if (!findPaymentType) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PAYMENT_CATEGORY_TYPE_NOT_FOUND")
            })
        }
        const findPaymentTypeCategory = await PaymentTypeCategory.findOne({ attributes: ['id'], where: { payment_type_id: findPaymentType.payment_type_id, payment_type_category_title, payment_type_category_status: { [Op.not]: paymentTypeCategoryStatus.DELETED } }, raw: true })

        if (findPaymentTypeCategory && findPaymentTypeCategory.id != findPaymentType.id) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PAYMENT_CATEGORY_EXIST_PAYMENT_TYPE")
            })
        }
        const updatePaymentTypeCategoryObj: any = {
            payment_type_category_title,
            payment_type_category_status,
            payment_type_category_remarks,
            created_by: req.user.id,
            updated_by: req.user.id
        }
        if (findPaymentType.payment_type_category_pattern == paymentTypeCategoryPattern.SINGLE) {
            updatePaymentTypeCategoryObj.payment_type_category_data_type = payment_type_category_data_type
        }
        const updatePaymentTypeCategory = await PaymentTypeCategory.update(updatePaymentTypeCategoryObj, { where: { id: findPaymentType.id } })
        if (updatePaymentTypeCategory.length > 0) {
            if (findPaymentType.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                const availableFieldIds = []

                for (const fields of payment_type_category_fields) {

                    const fieldObj: any = {
                        payment_type_category_id: findPaymentType.id,
                        field_name: fields?.field_name,
                        field_type: fields?.field_type,
                        field_limit: fields?.field_limit,
                        updated_by: req.user.id
                    }
                    if (fields?.field_id) {
                        const findExist = await PaymentTypeCategoryField.findOne({ where: { field_name: fields?.field_name, payment_type_category_id: findPaymentType.id, id: { [Op.not]: fields.field_id } } })
                        if (findExist && findExist.payment_type_category_field_status == paymentTypeCategoryFieldStatus.ACTIVE) {
                            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                                status: false,
                                message: res.__("FIELD_EXIST_IN_CATEGORY")
                            })
                        } else {
                            availableFieldIds.push(fields.field_id)
                            fieldObj.payment_type_category_field_status = paymentTypeCategoryFieldStatus.ACTIVE
                            await PaymentTypeCategoryField.update(fieldObj, { where: { id: fields?.field_id } })
                        }
                    } else {
                        fieldObj.created_by = req.user.id
                        fieldObj.payment_type_category_field_status = paymentTypeCategoryFieldStatus.ACTIVE
                        const findExist = await PaymentTypeCategoryField.findOne({ where: { field_name: fields?.field_name, payment_type_category_id: findPaymentType.id, payment_type_category_field_status: paymentTypeCategoryFieldStatus.ACTIVE } })
                        if (findExist) {
                            if (findExist.payment_type_category_field_status == paymentTypeCategoryFieldStatus.ACTIVE) {
                                return res.status(StatusCodes.EXPECTATION_FAILED).json({
                                    status: false,
                                    message: res.__("FIELD_EXIST_IN_CATEGORY")
                                })
                            } else {
                                availableFieldIds.push(findExist.id)
                                await PaymentTypeCategoryField.update(fieldObj, { where: { id: findExist.id } })
                            }
                        } else {
                            const paymentCategoryFields = await PaymentTypeCategoryField.create(fieldObj)
                            availableFieldIds.push(paymentCategoryFields.id)
                        }
                    }
                }
                await PaymentTypeCategoryField.update({
                    payment_type_category_field_status: paymentTypeCategoryFieldStatus.INACTIVE,
                    updated_by: req.user.id
                }, { where: { id: { [Op.notIn]: availableFieldIds }, payment_type_category_field_status: paymentTypeCategoryFieldStatus.ACTIVE, payment_type_category_id } })
            }
            await PaymentTypeCategoryBranch.update({ payment_type_category_branch_status: updatePaymentTypeCategoryObj.payment_type_category_status }, { where: { payment_type_category_id: findPaymentType.id } })
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("PAYMENT_TYPE_CATEGORY_UPDATED")
            })
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_PAYMENT_TYPE_CATEGORY_UPDATED")
            })
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

/**
 *  update Payment Type category Order
 * @param req
 * @param res
 * @returns
 */

const updatePaymentTypeCategoryOrder = async (req: any, res: any) => {
    try {
        const { payment_type_category_id, order } = req.body;

        const findPaymentType = await PaymentTypeCategory.findOne({
            where: {
                id: payment_type_category_id,
                payment_type_category_status: { [Op.not]: paymentTypeCategoryStatus.DELETED },
            },
        });
        if (!findPaymentType) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("PAYMENT_CATEGORY_TYPE_NOT_FOUND") });
        }

        const paymentTypeCategoryObj: any = {
            where: {
                payment_type_category_status: { [Op.not]: paymentTypeCategoryStatus.DELETED },
            },
            attributes: [
                'id', 'payment_type_category_order'
            ],
            order: [['payment_type_category_order', 'ASC']]
        }


        const categories = await PaymentTypeCategory.findAll(paymentTypeCategoryObj);
        const oldPosition = findPaymentType.payment_type_category_order;
        const newPosition = order
        categories.forEach(async item => {
            let new_order = item.payment_type_category_order
            if (item.id !== payment_type_category_id) {

                // If the item's position is between the old position and new position
                if (oldPosition < newPosition && item.payment_type_category_order > oldPosition && item.payment_type_category_order <= newPosition) {
                    new_order = item.payment_type_category_order - 1; // Move up
                }
                if (oldPosition > newPosition && item.payment_type_category_order < oldPosition && item.payment_type_category_order >= newPosition) {
                    new_order = item.payment_type_category_order + 1; // Move down
                }


            } else { new_order = order }
            await PaymentTypeCategory.setHeaders(req).update(
                {
                    payment_type_category_order: new_order,
                    updated_by: req.user.id
                },
                {
                    where: {
                        id: item.id
                    },
                },
            );
        });

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("PAYMENT_TYPE_CATEGORY_ORDER_UPDATE"),
        });

    } catch (e: any) {
        console.log('Exception', e);
        return res.send({ status: false, message: e.message });
    }
};

const getAllPaymentTypeWithChild = async (req: Request, res: Response) => {
    try {
        const { payment_type_status, search, page, size, only_active = false }: any = req.query
        const { limit, offset } = getPagination(Number(page), Number(size));
        const paymentTypeListObj: any = {
            attributes: ['id', 'payment_type_title', 'payment_type_status', 'payment_type_usage', 'has_weekly_use', 'has_include_amount', 'has_field_currency'],
            include: [{
                model: PaymentTypeCategory,
                as: "payment_type_category",
                attributes: ['id', 'payment_type_category_remarks', ['payment_type_category_title', 'payment_type_title'], ['payment_type_category_status', 'payment_type_status'], 'payment_type_category_pattern', 'payment_type_category_order', 'payment_type_category_data_type'],
                where: only_active ? { payment_type_category_status: paymentTypeCategoryStatus.ACTIVE } : { payment_type_category_status: { [Op.not]: paymentTypeCategoryStatus.DELETED } },
                required: false,
                include: [{
                    model: PaymentTypeCategoryField,
                    as: "payment_type_category_field",
                    attributes: ['id', 'field_name', 'field_type', 'field_limit', 'payment_type_category_field_status'],
                    where: { payment_type_category_field_status: paymentTypeCategoryFieldStatus.ACTIVE },
                    required: false,
                }]
            }],
            order: [
                ['payment_type_order', 'ASC'],
                [{ model: PaymentTypeCategory, as: 'payment_type_category' }, 'payment_type_category_order', 'ASC']
            ],
        }
        const whereObj: any = { organization_id: req.user.organization_id, payment_type_status: { [Op.not]: paymentTypeStatus.DELETED } }
        if (search) {
            whereObj.payment_type_title = { [Op.like]: `%${search}%` }
        }
        if (payment_type_status) {
            whereObj.payment_type_status = payment_type_status
        }
        if (page && size) {
            paymentTypeListObj.limit = Number(limit);
            paymentTypeListObj.offset = Number(offset);
        }
        if (only_active) {
            whereObj.payment_type_status = paymentTypeStatus.ACTIVE
        }
        paymentTypeListObj.where = whereObj
        const getPaymentList = await PaymentType.findAll(paymentTypeListObj)
        const count = await PaymentType.count({ where: whereObj })
        const { total_pages } = getPaginatedItems(
            size,
            page,
            count || 0,
        );
        if (getPaymentList.length > 0) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: getPaymentList,
                count: count,
                page: parseInt(page),
                size: parseInt(size),
                total_pages,
            });
        } else {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: [],
                count: 0,
                page: 0,
                size: 0,
                total_pages: 0,
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const deletePaymentType = async (req: Request, res: Response) => {
    try {
        const { payment_type_id } = req.params
        const getPaymentType = await PaymentType.findOne({ where: { id: payment_type_id, organization_id: req.user.organization_id, payment_type_status: { [Op.not]: paymentTypeStatus.DELETED } }, raw: true })
        if (getPaymentType) {
            const deletePaymentType = await PaymentType.update({ payment_type_status: paymentTypeStatus.DELETED, updated_by: req.user.id }, { where: { id: getPaymentType.id } })
            if (deletePaymentType.length > 0) {
                const findPaymentTypeCategory = await PaymentTypeCategory.findAll({ where: { payment_type_id: getPaymentType.id, payment_type_category_status: { [Op.not]: paymentTypeCategoryStatus.DELETED } }, raw: true })
                if (findPaymentTypeCategory.length > 0) {
                    const paymentCategoryIds = findPaymentTypeCategory.map((category) => { return category.id })
                    if (paymentCategoryIds.length > 0) {
                        const findDsrItem = await DsrItem.findAll({
                            where: {
                                payment_type_category_id: { [Op.in]: paymentCategoryIds }, dsr_item_status: dsr_item_status.ACTIVE
                            }
                        })
                        if (findDsrItem.length > 0) {
                            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                                status: false,
                                message: res.__("CAN'T_REMOVE_CATEGORY_VALUE"),
                            });
                        }
                    }
                    const deletePaymentTypeCategory = await PaymentTypeCategory.update({ payment_type_category_status: paymentTypeCategoryStatus.DELETED, updated_by: req.user.id }, { where: { id: { [Op.in]: paymentCategoryIds } } })
                    if (deletePaymentTypeCategory.length > 0) {
                        await PaymentTypeCategoryField.update({ payment_type_category_field_status: paymentTypeCategoryFieldStatus.INACTIVE }, { where: { payment_type_category_id: { [Op.in]: paymentCategoryIds } } })
                        await PaymentTypeCategoryBranch.update({ payment_type_category_branch_status: paymentTypeCategoryFieldStatus.INACTIVE }, { where: { payment_type_category_id: { [Op.in]: paymentCategoryIds } } })
                    }
                }
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("PAYMENT_TYPE_DELETED_SUCCESSFULLY"),
                });
            } else {
                return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("FAIL_TO_DELETE_PAYMENT_TYPE"),
                });
            }
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_TO_DELETE_PAYMENT_TYPE"),
            });
        }

    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}


const deletePaymentTypeCategory = async (req: Request, res: Response) => {
    try {
        const { payment_category_type_id } = req.params
        const getPaymentTypeCategory = await PaymentTypeCategory.findOne({ where: { id: payment_category_type_id, payment_type_category_status: { [Op.not]: paymentTypeCategoryStatus.DELETED } }, raw: true })
        if (getPaymentTypeCategory) {
            const findDsrItem = await DsrItem.findAll({
                where: {
                    payment_type_category_id: getPaymentTypeCategory.id, dsr_item_status: dsr_item_status.ACTIVE
                }
            })
            if (findDsrItem.length > 0) {
                return res.status(StatusCodes.EXPECTATION_FAILED).json({
                    status: false,
                    message: res.__("CAN'T_REMOVE_CATEGORY_VALUE"),
                });
            }
            const deletePaymentTypeCategory = await PaymentTypeCategory.update({ payment_type_category_status: paymentTypeCategoryStatus.DELETED, updated_by: req.user.id }, { where: { id: getPaymentTypeCategory.id } })
            if (deletePaymentTypeCategory.length > 0) {
                await PaymentTypeCategoryField.update({ payment_type_category_field_status: paymentTypeCategoryFieldStatus.INACTIVE }, { where: { payment_type_category_id: getPaymentTypeCategory.id } })
                await PaymentTypeCategoryBranch.update({ payment_type_category_branch_status: paymentTypeCategoryFieldStatus.INACTIVE }, { where: { payment_type_category_id: getPaymentTypeCategory.id } })
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("PAYMENT_TYPE_CATEGORY_DELETED_SUCCESSFULLY"),
                });
            } else {
                return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("FAIL_TO_DELETE_PAYMENT_TYPE_CATEGORY"),
                });
            }
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_TO_DELETE_PAYMENT_TYPE_CATEGORY"),
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}


const addValueInCategoryBranch = async (req: Request, res: Response) => {
    try {
        const { branch_id } = req.params
        const { payment_type_category_id, payment_type_category_value = [] } = req.body

        const findBranch = await Branch.findOne({ attributes: ['id'], where: { id: branch_id, organization_id: req.user.organization_id, branch_status: branch_status.ACTIVE }, raw: true })
        if (!findBranch) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("BRANCH_NOT_FOUND")
            })
        }
        const findPaymentTypeCategory = await PaymentTypeCategory.findOne({ attributes: ['id'], where: { id: payment_type_category_id, payment_type_category_status: paymentTypeCategoryStatus.ACTIVE }, raw: true })

        if (!findPaymentTypeCategory) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PAYMENT_TYPE_NOT_FOUND")
            })
        }

        const findParentCategoryExist = await PaymentTypeCategoryBranch.findOne({ attributes: ['id'], where: { branch_id: branch_id, payment_type_category_id: payment_type_category_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE }, raw: true })

        if (!findParentCategoryExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PAYMENT_TYPE_CATEGORY_NOT_IN_BRANCH")
            })
        }

        const findPaymentTypeCategoryBranchOrder = await PaymentTypeCategoryBranch.findOne({
            where: { payment_type_category_id: payment_type_category_id, branch_id: branch_id },
            order: [['payment_type_category_branch_order', 'DESC']],
            attributes: ['payment_type_category_branch_order'],
            raw: true
        })
        const order = findPaymentTypeCategoryBranchOrder ? findPaymentTypeCategoryBranchOrder.payment_type_category_branch_order + 1 : 1

        const findParentCategory = await PaymentTypeCategoryBranch.findOne({ where: { payment_type_category_id, branch_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE, parent_id: { [Op.eq]: null } } as any })
        if (!findParentCategory) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PAYMENT_TYPE_CATEGORY_NOT_IN_BRANCH")
            })
        }
        const addValueCategoryType = await PaymentTypeCategoryBranch.create({ payment_type_category_id, branch_id: findBranch.id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE, parent_id: findParentCategory.id, payment_type_category_branch_order: order, created_by: req.user.id, updated_by: req.user.id } as any)

        if (addValueCategoryType) {
            if (payment_type_category_value.length > 0) {
                for (const category_value of payment_type_category_value) {
                    const createObj: any = {
                        payment_type_category_field_id: category_value.payment_type_category_field_id,
                        payment_type_category_branch_id: addValueCategoryType.id,
                        field_value: category_value.field_value,
                        payment_type_category_value_status: paymentTypeCategoryValueStatus.ACTIVE,
                        created_by: req.user.id, updated_by: req.user.id
                    }
                    await PaymentTypeCategoryValue.create(createObj)
                }
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("VALUE_ADDED_SUCCESSFULLY")
                })
            } else {
                return res.status(StatusCodes.EXPECTATION_FAILED).json({
                    status: false,
                    message: res.__("PAYMENT_VALUE_ARRAY_CAN")
                })
            }
        } else {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_TO_ADD_VALUE_IN_CATEGORY")
            })
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const updateValueInCategoryBranch = async (req: Request, res: Response) => {
    try {
        const { payment_type_category_value = [] } = req.body

        if (payment_type_category_value.length > 0) {
            for (const category_value of payment_type_category_value) {
                if (category_value?.category_value_id) {
                    await PaymentTypeCategoryValue.update({ field_value: category_value.field_value, payment_type_category_value_status: paymentTypeCategoryValueStatus.ACTIVE, updated_by: req.user.id }, { where: { id: category_value.category_value_id } })
                } else {
                    const createObj: any = {
                        payment_type_category_field_id: category_value.payment_type_category_field_id,
                        payment_type_category_branch_id: category_value.payment_type_category_branch_id,
                        field_value: category_value.field_value,
                        payment_type_category_value_status: paymentTypeCategoryValueStatus.ACTIVE,
                        created_by: req.user.id, updated_by: req.user.id
                    }
                    await PaymentTypeCategoryValue.create(createObj)
                }
            }
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("VALUE_UPDATE_SUCCESSFULLY")
            })
        } else {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_TO_UPDATE_VALUE_IN_CATEGORY")
            })
        }

    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const getValueInCategoryBranchById = async (req: Request, res: Response) => {
    try {
        const { payment_type_category_branch_id } = req.params

        const findCategoryBranchList: any = await PaymentTypeCategoryBranch.findOne({ where: { id: payment_type_category_branch_id }, raw: true })
        let findFieldList: any = await PaymentTypeCategoryField.findAll({ attributes: [['id', 'payment_type_category_field_id'], 'field_name'], where: { payment_type_category_id: findCategoryBranchList.payment_type_category_id, payment_type_category_field_status: paymentTypeCategoryFieldStatus.ACTIVE }, raw: true })
        if (findFieldList.length > 0) {
            findFieldList = JSON.parse(JSON.stringify(findFieldList))
            for (const fieldList of findFieldList) {
                const findFieldValue: any = await PaymentTypeCategoryValue.findOne({
                    attributes: [['id', 'category_value_id'], 'field_value'],
                    where: { payment_type_category_field_id: fieldList.payment_type_category_field_id, payment_type_category_value_status: paymentTypeCategoryValueStatus.ACTIVE, payment_type_category_branch_id: payment_type_category_branch_id },
                    raw: true, nest: true
                })
                fieldList.category_value_id = findFieldValue && findFieldValue?.category_value_id ? findFieldValue?.category_value_id : null
                fieldList.field_value = findFieldValue && findFieldValue?.field_value ? findFieldValue?.field_value : null
                fieldList.payment_type_category_branch_id = findCategoryBranchList.id
            }
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: findFieldList.length > 0 ? findFieldList : [],
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}


const getAllCategoryBasedOnBranch = async (req: Request, res: Response) => {
    try {
        const { branch_id } = req.params

        let getCategoryBranch: any = await PaymentType.findAll({
            include: [
                {
                    model: PaymentTypeCategory,
                    as: "payment_type_category",
                    attributes: [['id', 'cateogory_id'], 'payment_type_category_remarks', 'payment_type_category_title', 'payment_type_category_status', 'payment_type_category_pattern', 'payment_type_category_order', [sequelize.literal(`(SELECT has_default_active FROM nv_payment_type_category_branch WHERE payment_type_category.id = nv_payment_type_category_branch.payment_type_category_id and branch_id = ${branch_id} and parent_id is null)`), 'has_default_active'],
                    [sequelize.literal(`(SELECT id FROM nv_payment_type_category_branch WHERE payment_type_category.id = nv_payment_type_category_branch.payment_type_category_id and branch_id = ${branch_id} and parent_id is null)`), 'id']],
                    where: {
                        payment_type_category_status: paymentTypeCategoryStatus.ACTIVE, id: {
                            [Op.in]: sequelize.literal(`(
                            SELECT payment_type_category_id 
                            FROM nv_payment_type_category_branch 
                            WHERE branch_id = ${branch_id} 
                            AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                        )`)  // Make sure subquery is wrapped inside parentheses
                        }
                    },
                    required: false,
                    include: [
                        {
                            model: PaymentTypeCategoryField,
                            as: "payment_type_category_field",
                            attributes: ['id', 'field_name', 'field_type', 'field_limit'],
                            where: { payment_type_category_field_status: paymentTypeCategoryFieldStatus.ACTIVE },
                            required: false
                        }

                    ]
                }
            ],
            where: {
                payment_type_status: paymentTypeStatus.ACTIVE,
                organization_id: req.user.organization_id
            }
        });
        if (getCategoryBranch.length > 0) {
            getCategoryBranch = JSON.parse(JSON.stringify(getCategoryBranch));

            for (const category of getCategoryBranch) {
                let categoryHasDefaultActive = false; // Reset this for each category

                if (category?.payment_type_category?.length > 0) {
                    for (const categoryBranch of category.payment_type_category) {

                        // Check if this branch itself has `has_default_active`
                        if (categoryBranch.has_default_active == true) {
                            categoryHasDefaultActive = true;
                        }

                        // Find the parent branch for the current categoryBranch.id
                        const parentBranch = await PaymentTypeCategoryBranch.findOne({
                            where: {
                                branch_id: branch_id,
                                payment_type_category_id: categoryBranch.cateogory_id,
                                parent_id: null,
                                payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                            } as any,
                            attributes: ['id'],
                            raw: true,
                            nest: true
                        });

                        const findCategoryBranch = await PaymentTypeCategoryBranch.findAll({
                            attributes: [['id', 'payment_type_category_branch_id'], 'has_default_active',
                            [sequelize.literal(`
                                (SELECT field_value 
                                FROM nv_payment_type_category_value 
                                INNER JOIN nv_payment_type_category_field ON nv_payment_type_category_value.payment_type_category_field_id = nv_payment_type_category_field.id
                                WHERE payment_type_category_branch_id = PaymentTypeCategoryBranch.id
                                And nv_payment_type_category_value.payment_type_category_value_status = '${payment_type_category_value_status.ACTIVE}' And nv_payment_type_category_field.payment_type_category_field_status = '${paymentTypeCategoryFieldStatus.ACTIVE}'
                                ORDER BY nv_payment_type_category_value.createdAt ASC 
                                    LIMIT 1)`), 'first_field_value']],
                            where: {
                                branch_id,
                                parent_id: parentBranch?.id,
                                payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                            },
                            order: [['payment_type_category_branch_order', 'ASC']],
                            raw: true,
                            nest: true
                        });

                        if (findCategoryBranch.length > 0) {
                            categoryBranch.categoryBranchValue = findCategoryBranch;

                            // Check if any of the child branches have `has_default_active`
                            const branchHasDefaultActive = findCategoryBranch.some(branch => branch.has_default_active == true);

                            // If any branch has `has_default_active`, mark the category as active
                            if (branchHasDefaultActive) {
                                categoryHasDefaultActive = true;
                            }
                        } else {
                            categoryBranch.categoryBranchValue = [];
                        }
                    }

                    // Set `category.has_default_active` based on the result of checks
                    category.has_default_active = categoryHasDefaultActive;
                }
            }
        }

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: getCategoryBranch.length > 0 ? getCategoryBranch : [],
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}


const updateStatusPaymentTypeCategory = async (req: Request, res: Response) => {
    try {
        const { checked_category_ids = [], branch_id } = req.body
        if (checked_category_ids.length > 0) {
            const findCheckedCategoryExist = await PaymentTypeCategoryBranch.update({ has_default_active: true }, { where: { id: { [Op.in]: checked_category_ids }, branch_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE } })
            if (findCheckedCategoryExist.length > 0) {
                await PaymentTypeCategoryBranch.update({ has_default_active: false }, { where: { id: { [Op.notIn]: checked_category_ids }, branch_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE } })

                await PaymentTypeCategoryBranch.update(
                    { has_default_active: false },
                    {
                        where: {
                            id: {
                                [Op.in]: sequelize.literal(`(
                                    SELECT * FROM (
                                        SELECT ptcb.id 
                                        FROM nv_payment_type_category_branch AS ptcb
                                        LEFT JOIN nv_payment_type_category AS sub_ptc ON sub_ptc.id = ptcb.payment_type_category_id
                                        WHERE ptcb.branch_id = ${branch_id}
                                        AND ptcb.payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                        AND ptcb.has_default_active = 1
                                        AND ptcb.parent_id IS NULL
                                        AND (
                                            SELECT COUNT(*)
                                            FROM nv_payment_type_category_branch AS sub_ptcb
                                            WHERE sub_ptcb.branch_id = ptcb.branch_id
                                                AND sub_ptcb.payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                                AND sub_ptcb.has_default_active = 1
                                                AND sub_ptcb.parent_id = ptcb.id
                                        ) = 0
                                        AND sub_ptc.payment_type_category_pattern = '${paymentTypeCategoryPattern.MULTIPLE}'
                                    ) AS derived_table
                                )`)
                            }
                        } as any
                    }
                );

                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("DEFAULT_ACTIVE_UPDATE")
                });
            } else {
                return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("FAIL_DEFAULT_ACTIVE_UPDATE")
                });
            }
        } else {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("CATEGORY_ID_ARRAY_REQUIRED")
            });

        }


    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const updatePaymentTypeValueOrder = async (req: any, res: any) => {
    try {
        const { payment_type_category_branch_id, order } = req.body;

        const findPaymentTypeCategoryBranch = await PaymentTypeCategoryBranch.findOne({
            where: {
                id: payment_type_category_branch_id,
            }, raw: true
        });
        if (!findPaymentTypeCategoryBranch) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("PAYMENT_TYPE_VALUE_NOT_FOUND") });
        }

        const paymentTypeObj: any = {
            attributes: [
                'id', 'payment_type_category_branch_order'
            ],
            order: [['payment_type_category_branch_order', 'ASC']]
        }


        const categories = await PaymentTypeCategoryBranch.findAll(paymentTypeObj);
        const oldPosition = findPaymentTypeCategoryBranch.payment_type_category_branch_order;
        const newPosition = order
        categories.forEach(async item => {
            let new_order = item.payment_type_category_branch_order
            if (item.id !== payment_type_category_branch_id) {

                // If the item's position is between the old position and new position
                if (oldPosition < newPosition && item.payment_type_category_branch_order > oldPosition && item.payment_type_category_branch_order <= newPosition) {
                    new_order = item.payment_type_category_branch_order - 1; // Move up
                }
                if (oldPosition > newPosition && item.payment_type_category_branch_order < oldPosition && item.payment_type_category_branch_order >= newPosition) {
                    new_order = item.payment_type_category_branch_order + 1; // Move down
                }


            } else { new_order = order }
            await PaymentTypeCategoryBranch.setHeaders(req).update(
                {
                    payment_type_category_branch_order: new_order,
                    updated_by: req.user.id
                },
                {
                    where: {
                        id: item.id
                    },
                },
            );
        });

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("PAYMENT_TYPE_VALUE_ORDER_UPDATE"),
        });

    } catch (e: any) {
        console.log('Exception', e);
        return res.send({ status: false, message: e.message });
    }
}

const deletePaymentTypeCategoryValue = async (req: any, res: any) => {
    try {
        const { payment_type_category_branch_id, } = req.params

        const findPaymentTypeCategory = await PaymentTypeCategoryBranch.findOne({ where: { id: payment_type_category_branch_id }, raw: true })
        if (findPaymentTypeCategory) {
            const findDsrItem = await DsrItem.findAll({
                attributes: ['id'],
                where: {
                    reference_id: findPaymentTypeCategory.id, dsr_item_status: dsr_item_status.ACTIVE
                }, raw: true
            })
            if (findDsrItem.length > 0) {
                return res.status(StatusCodes.EXPECTATION_FAILED).json({
                    status: false,
                    message: res.__("CAN'T_REMOVE_CATEGORY_VALUE"),
                });
            }
            await PaymentTypeCategoryBranch.update({ payment_type_category_branch_status: paymentTypeCategoryBranchStatus.INACTIVE }, { where: { id: findPaymentTypeCategory.id } })
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("CATEGORY_REMOVED_SUCCESSFULLY"),
            });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_TO_REMOVE_CATEGORY"),
            });
        }
    } catch (e: any) {
        console.log('Exception', e);
        return res.send({ status: false, message: e.message });
    }
}

export default {
    createPaymentType,
    updatePaymentType,
    getPaymentTypeById,
    getPaymentTypeList,
    updatePaymentTypeOrder,
    createPaymentTypeCategory,
    updatePaymentTypeCategory,
    updatePaymentTypeCategoryOrder,
    getAllPaymentTypeWithChild,
    deletePaymentType,
    deletePaymentTypeCategory,
    addValueInCategoryBranch,
    updateValueInCategoryBranch,
    getValueInCategoryBranchById,
    getAllCategoryBasedOnBranch,
    updateStatusPaymentTypeCategory,
    updatePaymentTypeValueOrder,
    deletePaymentTypeCategoryValue
}