import { StatusCodes } from "http-status-codes";
import { deleteFiles, propagateBranchesAndDepartmentsToAllParents, updateBranchesAndDepartmentsInAllParents, findAllChildren, fetchCategoriesRecursively, calculateTotalSize, createNotification, getSuperAdminUserId, checkUserStorageLimit, checkStorageSize } from "../helper/common";
import { NOTIFICATION_TYPE, NOTIFICATIONCONSTANT, ROLE_CONSTANT, REDIRECTION_TYPE, FILE_UPLOAD_CONSTANT } from "../helper/constant";
import { DocumentCategory, category_status as categoryStatus, category_use as categoryUse, category_type as categoryType } from "../models/DocumentCategory";
import { Role, role_status } from "../models/Role";
import { User, user_status } from "../models/User";
import { Department, department_status } from "../models/Department";
import { Op, QueryTypes } from "sequelize";
import { Branch, branch_status } from "../models/Branch";
import documentCategoryValidator from "../validators/documentCategory.validator";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { sequelize } from "../models";
import {
  document_category_item_status,
  DocumentCategoryItem,
} from "../models/DocumentCategoryItem";
import {
  document_category_branch_status,
  DocumentCategoryBranch,
} from "../models/DocumentCategoryBranch";
import {
  document_category_department_status,
  DocumentCategoryDepartment,
} from "../models/DocumentCategoryDepartment";
import {
  document_category_item_track_status,
  DocumentCategoryItemTrack,
} from "../models/DocumentCategoryItemTrack";
import { Item } from "../models/Item";
import {
  HealthSafetyCategoryItem,
  status,
} from "../models/HealthSafetyCategoryItem";
import { check_list_status, UserCheckList } from "../models/UserCheckList";
import { UserBranch } from "../models/UserBranch";
import { moveFileInBucket } from "../helper/upload.service";

/**
 *  Create category
 * @param req
 * @param res
 * @returns
 */

const createCategory = async (req: any, res: any) => {
  try {
    const {
      category_name,
      category_description,
      category_status,
      dashboard_view = false,
      category_use,
      parent_id,
      branch_ids,
      department_ids,
      is_notify,
      notification_content,
      has_video_control,
      has_agreement_required,
      is_external_link,
      external_links = [],
      category_type,
    } = req.body;

    const checkStorage: any = await checkStorageSize(req, res);
    if (checkStorage.status == false) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: checkStorage.message,
      });
    }

    const { error } = await documentCategoryValidator.createCategory.validate(
      req.body,
    );
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }

    if (req.files) {
      if (
        category_type == categoryType.FILE &&
        !req.files.item_list &&
        external_links.length == 0
      ) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("ITEM_UPLOAD_REQUIRED_FOR_FILE_TYPE"),
        });
      }
      if (category_type == categoryType.FOLDER && req.files.item_list) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("ITEM_UPLOAD_NOT_ALLOWED_IN_FOLDER"),
        });
      }
    }

    // Fetch user details
    const user = await User.findOne({ attributes: ['web_user_active_role_id'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true });
    if (!user) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("ERROR_USER_NOT_FOUND"),
      });
    }

    // Fetch user role
    const userRole: any = await Role.findOne({
      attributes: ['role_name'],
      where: {
        id: user.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
      raw: true,
    });

    if (
      !userRole ||
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(userRole.role_name)
    ) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    if (category_type == categoryType.FILE && !parent_id) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("PARENT_REQUIRED_FOR_CATEGORY_ITEM"),
      });
    }

    // Check if the category already exists
    const existingCategory = await DocumentCategory.findOne({
      where: {
        category_name,
        organization_id: req.user.organization_id,
        category_status: { [Op.not]: categoryStatus.DELETED },
      },
      raw: true,
    });
    if (existingCategory) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("CATEGORY_EXIST"),
      });
    }

    let currentLevel = 1;
    if (parent_id) {
      let parentCategory = await DocumentCategory.findOne({ where: { id: parent_id, organization_id: req.user.organization_id }, raw: true });
      if (!parentCategory) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("PARENT_CATEGORY_NOT_FOUND"),
        });
      }

      // Traverse up the parent chain to determine the level
      while (parentCategory?.parent_id) {
        currentLevel++;
        parentCategory = await DocumentCategory.findOne({ where: { id: parentCategory.parent_id, organization_id: req.user.organization_id }, raw: true });
      }

      // Check if adding a folder would exceed the maximum level
      if (
        category_type === categoryType.FOLDER &&
        currentLevel >= global.config.CATEGORY_CREATE_LIMIT - 1
      ) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("CANNOT_CREATE_FOLDER_BEYOND_10_LEVELS"),
        });
      }

      // Check if adding a file would exceed the maximum level
      if (
        category_type === categoryType.FILE &&
        currentLevel >= global.config.CATEGORY_CREATE_LIMIT
      ) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("CANNOT_ADD_FILE_BEYOND_10_LEVELS"),
        });
      }
    }

    // Training category specific validation
    if (category_use === categoryUse.TRAINING && parent_id) {
      const parentCategory = await DocumentCategory.findOne({ where: { id: parent_id, category_use: categoryUse.TRAINING, organization_id: req.user.organization_id }, raw: true });
      if (parentCategory && parentCategory.parent_id && category_type == categoryType.FOLDER) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("CAN'T_ADD_FOLDER_MORE_THAN_ONE_IN_TRANING"),
        });
      }
    }

    // Validate branches
    const validBranches = await Branch.findAll({
      where: {
        id: { [Op.in]: branch_ids },
        branch_status: branch_status.ACTIVE,
        organization_id: req.user.organization_id
      }, raw: true
    });

    if (validBranches.length !== branch_ids.length) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("BRANCH_NOT_FOUND"),
      });
    }

    // Validate departments
    const validDepartments = await Department.findAll({
      where: {
        id: { [Op.in]: department_ids },
        department_status: department_status.ACTIVE,
        organization_id: req.user.organization_id
      }, raw: true
    });

    if (validDepartments.length !== department_ids.length) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("DEPARTMENT_NOT_FOUND"),
      });
    }

    let getFileId: any;
    let itemListId: any = [];
    if (req.files) {
      if (req.files.category_image && req.files.category_image.length > 0) {
        const categoryImage = req.files.category_image[0];
        getFileId = categoryImage.item_id;

        // Move file to proper location if needed
        if (categoryImage.isMovable) {
          await moveFileInBucket(
            categoryImage.bucket,
            categoryImage.path,
            FILE_UPLOAD_CONSTANT.DOCUMENT_CATEGORY_FILES.destinationPath(
              req.user.organization_id,
              categoryImage.filename,
            ),
            categoryImage.item_id,
            true,
          );
        }
      }

      if (req.files.item_list && req.files.item_list.length > 0) {
        itemListId = req.files.item_list;

        // Process each item in item_list
        for (const item of req.files.item_list) {
          if (item.isMovable) {
            await moveFileInBucket(
              item.bucket,
              item.path,
              FILE_UPLOAD_CONSTANT.DOCUMENT_CATEGORY_FILES.destinationPath(
                req.user.organization_id,
                item.filename,
              ),
              item.item_id,
              true,
            );
          }
        }
      }
    }

    const createObj: any = {
      category_name,
      category_image: getFileId ? getFileId : null,
      category_description,
      category_use,
      category_status,
      dashboard_view,
      category_type: category_type,
      parent_id,
      has_video_control,
      has_agreement_required,
      is_external_link,
      organization_id: req.user.organization_id,
      created_by: req.user.id,
      updated_by: req.user.id,
    };
    if (parent_id) {
      const findParentCategory = await DocumentCategory.findOne({ where: { parent_id: parent_id, organization_id: req.user.organization_id }, order: [['category_order', 'DESC']], raw: true })
      createObj.category_order = findParentCategory ? findParentCategory.category_order + 1 : 1
    } else {
      const findParentCategory = await DocumentCategory.findOne({
        where: { parent_id: { [Op.eq]: null } as any, organization_id: req.user.organization_id },
        order: [['category_order', 'DESC']],
        attributes: ['category_order'], // Only select category_order for efficiency
        raw: true
      });
      createObj.category_order = findParentCategory
        ? findParentCategory.category_order + 1
        : 1;
    }

    // Create new category
    const newCategory =
      await DocumentCategory.setHeaders(req).create(createObj);

    if (newCategory) {
      if (itemListId.length > 0) {
        // Fetch the maximum order count for the current category
        const lastCategoryItems = await DocumentCategoryItem.findAll({
          where: { category_id: newCategory.id },
          order: [["order", "DESC"]],
          raw: true,
        });

        // Determine the next order count
        let nextOrderCount =
          lastCategoryItems.length > 0 ? lastCategoryItems[0].order + 1 : 1;

        for (const itemId of itemListId) {
          await DocumentCategoryItem.create({
            category_id: newCategory.id,
            item_id: itemId.item_id,
            document_category_item_type: itemId.type,
            document_category_item_status: newCategory.category_status,
            created_by: req.user.id,
            updated_by: req.user.id,
            order: nextOrderCount,
          } as any);

          // Increment order count for the next item
          nextOrderCount++;
        }
      }
      if (external_links.length > 0) {
        // Fetch the maximum order count for the current category
        const lastCategoryItems = await DocumentCategoryItem.findAll({
          where: { category_id: newCategory.id },
          order: [["order", "DESC"]],
          raw: true,
        });

        // Determine the next order count
        let nextOrderCount =
          lastCategoryItems.length > 0 ? lastCategoryItems[0].order + 1 : 1;

        for (const link of external_links) {
          await DocumentCategoryItem.create({
            category_id: newCategory.id,
            document_category_item_status: newCategory.category_status,
            document_category_item_link: link.document_category_item_link, // Link
            document_category_item_type: link.document_category_item_type, // Type (pdf, image, etc.)
            created_by: req.user.id,
            updated_by: req.user.id,
            order: nextOrderCount,
          } as any);

          // Increment order count for the next item
          nextOrderCount++;
        }
      }
      await propagateBranchesAndDepartmentsToAllParents(
        newCategory.id,
        branch_ids,
        department_ids,
        req,
      );
    }

    /** if notify true then send notification to user */
    if (is_notify == "true") {
      const findUserDetail = await User.findAll({
        attributes: ['id', 'appToken', 'webAppToken'],
        where: {
          branch_id: { [Op.in]: branch_ids },
          department_id: { [Op.in]: department_ids },
          organization_id: req.user.organization_id
        }, raw: true, nest: true
      })
      await createNotification(findUserDetail, req, NOTIFICATION_TYPE.INDIVIDUAL, notification_content, NOTIFICATIONCONSTANT.CATEGORY_ADDED.heading, REDIRECTION_TYPE.CATEGORY, newCategory.id, { category_id: newCategory.id, category_type: newCategory.category_type, parent_id: Number(newCategory.parent_id) })
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_CATEGORY_CREATED"),
    });
  } catch (error) {
    console.error("Error creating category:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
    });
  }
};

/**
 *  Update category
 * @param req
 * @param res
 * @returns
 */

const updateCategory = async (req: any, res: any) => {
  try {
    const {
      category_name,
      category_description,
      category_status,
      dashboard_view = false,
      branch_ids,
      department_ids,
      is_notify,
      notification_content,
      has_video_control,
      has_agreement_required,
      is_external_link,
      external_links = [],
      existing_item = [],
    } = req.body;
    const { category_id } = req.params;

    // Fetch user details
    const user = await User.findOne({ attributes: ['id', 'web_user_active_role_id'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true });
    if (!user) {
      await deleteFiles([req.file]);
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("ERROR_USER_NOT_FOUND"),
      });
    }


    const checkStorage: any = await checkStorageSize(req, res)
    if (checkStorage.status == false) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: checkStorage.message,
      });
    }

    // Fetch user role
    const userRole: any = await Role.findOne({
      attributes: ['role_name'],
      where: { id: user.web_user_active_role_id, role_status: role_status.ACTIVE }, raw: true
    });

    if (
      !userRole ||
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(userRole.role_name)
    ) {
      await deleteFiles([req.file]);
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const checkCategoryExist = await DocumentCategory.findOne({ attributes: ['id', 'category_status', 'category_image', 'category_type', 'parent_id'], where: { id: category_id, organization_id: req.user.organization_id, category_status: { [Op.not]: categoryStatus.DELETED } }, raw: true });
    if (!checkCategoryExist) {
      return res.status(StatusCodes.CONFLICT).json({
        status: false,
        message: res.__("CATEGORY_NOT_FOUND"),
      });
    }

    const findCategoryExist = await DocumentCategory.findOne({ attributes: ['id', 'category_status', 'category_image', 'category_type', 'parent_id'], where: { id: { [Op.not]: category_id }, category_name: category_name, category_status: { [Op.not]: categoryStatus.DELETED }, organization_id: req.user.organization_id }, raw: true });
    if (findCategoryExist) {
      return res.status(StatusCodes.CONFLICT).json({
        status: false,
        message: res.__("CATEGORY_EXIST"),
      });
    }

    // Validate branches
    const validBranches = await Branch.findAll({
      where: {
        id: { [Op.in]: branch_ids },
        branch_status: branch_status.ACTIVE,
        organization_id: req.user.organization_id
      }, raw: true
    });

    if (validBranches.length !== branch_ids.length) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("BRANCH_NOT_FOUND"),
      });
    }

    // Validate departments
    const validDepartments = await Department.findAll({
      where: {
        id: { [Op.in]: department_ids },
        department_status: department_status.ACTIVE,
        organization_id: req.user.organization_id
      }, raw: true
    });

    if (validDepartments.length !== department_ids.length) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("DEPARTMENT_NOT_FOUND"),
      });
    }

    let getImageId: any;
    let itemListId: any = [];

    if (
      req.files &&
      req.files.category_image &&
      req.files.category_image.length > 0
    ) {
      const categoryImage = req.files.category_image[0];
      getImageId = categoryImage.item_id;

      // Move file to proper location if needed
      if (categoryImage.isMovable) {
        await moveFileInBucket(
          categoryImage.bucket,
          categoryImage.path,
          FILE_UPLOAD_CONSTANT.DOCUMENT_CATEGORY_FILES.destinationPath(
            req.user.organization_id,
            categoryImage.filename,
          ),
          categoryImage.item_id,
          true,
        );
      }
    } else {
      getImageId = checkCategoryExist.category_image;
    }

    if (req.files && req.files.item_list && req.files.item_list.length > 0) {
      itemListId = req.files.item_list.map((item: any) => item.item_id);

      // Process each item in item_list
      for (const item of req.files.item_list) {
        if (item.isMovable) {
          await moveFileInBucket(
            item.bucket,
            item.path,
            FILE_UPLOAD_CONSTANT.DOCUMENT_CATEGORY_FILES.destinationPath(
              req.user.organization_id,
              item.filename,
            ),
            item.item_id,
            true,
          );
        }
      }
    } else {
      itemListId = existing_item;
    }

    if (existing_item.length > 0 && itemListId.length > 0) {
      itemListId = [...new Set([...existing_item, ...itemListId])];
    }

    const updateObj: any = {
      category_name,
      category_image: getImageId ? getImageId : null,
      category_description,
      category_status,
      dashboard_view,
      category_type: checkCategoryExist.category_type,
      parent_id: checkCategoryExist.parent_id,
      has_video_control,
      has_agreement_required,
      is_external_link,
      created_by: req.user.id,
      updated_by: req.user.id,
    };

    if (checkCategoryExist.parent_id) {
      const findParentCategory = await DocumentCategory.findOne({ where: { parent_id: checkCategoryExist.parent_id, organization_id: req.user.organization_id }, raw: true, order: [['category_order', 'DESC']] })
      updateObj.category_order = findParentCategory ? findParentCategory.category_order + 1 : 1
    } else {
      const findParentCategory = await DocumentCategory.findOne({
        where: {
          parent_id: { [Op.eq]: null } as any,
          organization_id: req.user.organization_id,
        },
        order: [["category_order", "DESC"]],
        attributes: ["category_order"], // Only select category_order for efficiency
      });
      updateObj.category_order = findParentCategory
        ? findParentCategory.category_order + 1
        : 1;
    }

    // Create new category
    const newCategory = await DocumentCategory.setHeaders(req).update(
      updateObj,
      {
        where: { id: category_id, organization_id: req.user.organization_id },
      } as any,
    );

    if (newCategory.length > 0) {
      if (itemListId.length > 0) {
        // Fetch the maximum order count for the current category
        const lastCategoryItems = await DocumentCategoryItem.findAll({
          where: { category_id: checkCategoryExist.id },
          order: [["order", "DESC"]],
        });

        // Determine the next order count
        let nextOrderCount =
          lastCategoryItems.length > 0 ? lastCategoryItems[0].order + 1 : 1;
        itemListId = itemListId.map(Number);
        await DocumentCategoryItem.update(
          {
            document_category_item_status:
              document_category_item_status.INACTIVE,
          },
          {
            where: {
              category_id: checkCategoryExist.id,
              item_id: { [Op.notIn]: itemListId },
            },
          },
        );
        const itemDetail = await Item.findAll({
          where: { id: { [Op.in]: itemListId } },
        });
        if (itemDetail.length > 0) {
          for (const item of itemDetail) {
            // Check if the item_id already exists for this category_id
            const existingItem = await DocumentCategoryItem.findOne({
              attributes: ['document_category_item_status', 'category_id', 'item_id'],
              where: {
                category_id: checkCategoryExist.id,
                item_id: item.id
              }, raw: true
            });

            if (!existingItem) {
              await DocumentCategoryItem.create({
                category_id: checkCategoryExist.id,
                item_id: item.id,
                document_category_item_type: item.item_type,
                document_category_item_status:
                  checkCategoryExist.category_status,
                created_by: req.user.id,
                updated_by: req.user.id,
                order: nextOrderCount,
              } as any);
            } else {
              if (
                existingItem?.document_category_item_status ==
                document_category_item_status.INACTIVE
              ) {
                await DocumentCategoryItem.update(
                  {
                    document_category_item_status:
                      document_category_item_status.ACTIVE,
                    document_category_item_type: item.item_type,
                    created_by: req.user.id,
                    updated_by: req.user.id,
                  },
                  {
                    where: {
                      category_id: existingItem.category_id,
                      item_id: existingItem.item_id,
                    },
                  },
                );
              }
              await DocumentCategoryItem.update(
                {
                  document_category_item_type: item.item_type,
                  created_by: req.user.id,
                  updated_by: req.user.id,
                },
                {
                  where: {
                    category_id: existingItem.category_id,
                    item_id: existingItem.item_id,
                  },
                },
              );
            }
            // Increment order count for the next item
            nextOrderCount++;
          }
        }
      } else {
        await DocumentCategoryItem.update(
          {
            document_category_item_status:
              document_category_item_status.INACTIVE,
          },
          {
            where: {
              category_id: checkCategoryExist.id,
              item_id: { [Op.not]: null as any },
            },
          },
        );
      }
      if (external_links.length > 0 && is_external_link) {
        // Fetch the maximum order count for the current category
        const lastCategoryItems = await DocumentCategoryItem.findAll({
          where: { category_id: checkCategoryExist.id },
          order: [["order", "DESC"]],
        });

        // Determine the next order count
        let nextOrderCount =
          lastCategoryItems.length > 0 ? lastCategoryItems[0].order + 1 : 1;
        await DocumentCategoryItem.destroy({
          where: {
            category_id: checkCategoryExist.id,
            item_id: { [Op.is]: null } as any,
          },
        });
        for (const link of external_links) {
          await DocumentCategoryItem.create({
            category_id: checkCategoryExist.id,
            document_category_item_status: checkCategoryExist.category_status,
            document_category_item_link: link.document_category_item_link, // Link
            document_category_item_type: link.document_category_item_type, // Type (pdf, image, etc.)
            created_by: req.user.id,
            updated_by: req.user.id,
            order: nextOrderCount,
          } as any);

          // Increment order count for the next item
          nextOrderCount++;
        }
      } else {
        await DocumentCategoryItem.destroy({
          where: {
            category_id: checkCategoryExist.id,
            item_id: { [Op.is]: null } as any,
          },
        });
      }
      await updateBranchesAndDepartmentsInAllParents(
        checkCategoryExist.id,
        branch_ids,
        department_ids,
        req,
      );

      if (is_notify == "true") {
        const findUserDetail = await User.findAll({
          attributes: ['id', 'appToken', 'webAppToken'],
          where: {
            branch_id: { [Op.in]: branch_ids },
            department_id: { [Op.in]: department_ids },
            organization_id: req.user.organization_id
          }, raw: true, nest: true
        })
        await createNotification(findUserDetail, req, NOTIFICATION_TYPE.INDIVIDUAL, notification_content, NOTIFICATIONCONSTANT.CATEGORY_ADDED.heading, REDIRECTION_TYPE.CATEGORY, checkCategoryExist.id, { category_id: checkCategoryExist.id, category_type: checkCategoryExist.category_type, parent_id: Number(checkCategoryExist.parent_id) })
      }
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESSFULL_CATEGORY_UPDATE"),
    });
  } catch (error) {
    console.error("Error creating category:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
    });
  }
};

/**
 * get All Category list
 * @param req
 * @param res
 * @returns
 */

const getAllCategoryList = async (req: any, res: any) => {
  try {
    const {
      page,
      size,
      search,
      departments,
      branches,
      parent_id,
      category_use,
      category_status,
    }: any = req.query;
    let branch = branches;
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id },
      raw: true,
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    const findUserRole: any = await Role.findOne({
      attributes: ['role_name'],
      where: {
        id:
          req.headers["platform-type"] == "web"
            ? getUserDetail.web_user_active_role_id
            : getUserDetail.user_active_role_id,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.ACCOUNTANT,
        ROLE_CONSTANT.AREA_MANAGER,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(findUserRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    if (
      findUserRole &&
      (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER)
    ) {
      branch = getUserDetail?.branch_id;
    }

    const findUserBranch = await UserBranch.findAll({
      where: {
        user_id: req.user.id,
      },
      raw: true,
    });

    if (
      findUserRole &&
      findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER &&
      findUserBranch.length > 0
    ) {
      const branchIds = findUserBranch.map((branch) => {
        return branch.branch_id;
      });
      branch = branchIds.join(", ");
    }

    // Fetch all categories starting from the root (parent_id = null)
    let categories: any = [];
    const searchObj = {
      search,
      departments,
      branches: branch,
      category_use,
      category_status,
    };
    categories = await fetchCategoriesRecursively(
      req.user.id,
      parent_id ? parent_id : null,
      10,
      categoryType.FOLDER,
      1,
      page,
      size,
      searchObj,
      "",
      "",
      req.user.organization_id,
    );

    return res.send({ status: true, ...categories });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 * get Category list
 * @param req
 * @param res
 * @returns
 */

const getCategoryList = async (req: any, res: any) => {
  try {
    const {
      page,
      size,
      search,
      departments,
      branches,
      parent_id,
      category_use,
      category_status,
    }: any = req.query;

    let branch = branches;
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    const findUserRole: any = await Role.findOne({
      attributes: ['role_name'],
      where: {
        id:
          req.headers["platform-type"] == "web"
            ? getUserDetail.web_user_active_role_id
            : getUserDetail.user_active_role_id,
      },
      raw: true,
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.ACCOUNTANT,
        ROLE_CONSTANT.AREA_MANAGER,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(findUserRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    if (
      findUserRole &&
      (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER)
    ) {
      branch = getUserDetail?.branch_id;
    }
    const findUserBranch = await UserBranch.findAll({
      where: {
        user_id: req.user.id,
      },
      raw: true,
    });

    if (
      findUserRole &&
      findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER &&
      findUserBranch.length > 0
    ) {
      const branchIds = findUserBranch.map((branch) => {
        return branch.branch_id;
      });
      branch = branchIds.join(", ");
    }
    // Fetch all categories starting from the root (parent_id = null)
    let categories: any = [];
    const searchObj = {
      search,
      departments,
      branches: branch,
      category_use,
      category_status,
    };
    categories = await fetchCategoriesRecursively(
      req.user.id,
      parent_id ? parent_id : null,
      1,
      null,
      1,
      page,
      size,
      searchObj,
      "",
      "",
      req.user.organization_id,
    );
    // Calculate total size and percentage
    const { length: totalSizeLength, unit } = await calculateTotalSize(
      categories.data,
    );

    let totalSizeInMB = 0;
    if (unit === "B") {
      totalSizeInMB = parseFloat(totalSizeLength.toString()) / (1024 * 1024);
    } else if (unit === "KB") {
      totalSizeInMB = parseFloat(totalSizeLength.toString()) / 1024;
    } else if (unit === "MB") {
      totalSizeInMB = parseFloat(totalSizeLength.toString());
    } else if (unit === "GB") {
      totalSizeInMB = parseFloat(totalSizeLength.toString()) * 1024;
    }

    let limitInMB = global.config.FILE_SIZE_LIMIT;
    let totalStorageLimit = global.config.FILE_SIZE_LIMIT;

    /** check if user has any subscription plan of storage */
    const checkUserStorage: any = await checkUserStorageLimit(
      req.user.organization_id,
    );
    if (checkUserStorage.status) {
      limitInMB += checkUserStorage.limitInMB;
      totalStorageLimit = limitInMB;
    }
    limitInMB = limitInMB * 1024;
    const percentage = (totalSizeInMB / limitInMB) * 100;
    const memoryCalculation = {
      total_size: `${totalSizeLength} ${unit}`,
      limit: `${totalStorageLimit} ${checkUserStorage.subs_storage_size.toUpperCase()}`,
      percentage: `${percentage.toFixed(2)}%`,
    };

    return res.send({
      status: true,
      ...categories,
      memory_calculation: memoryCalculation,
    });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 * get Own Category list
 * @param req
 * @param res
 * @returns
 */

const getOwnCategoryList = async (req: any, res: any) => {
  try {
    const { page, size, search, parent_id, category_use, dashboard_view }: any =
      req.query;

    // Fetch all categories starting from the root (parent_id = null)
    let categories: any = [];
    const searchObj = {
      search,
      departments: req.user.department_id,
      branches: req.user.branch_id,
      category_use,
      category_status: categoryStatus.ACTIVE,
      dashboard_view,
    };
    categories = await fetchCategoriesRecursively(
      req.user.id,
      parent_id ? parent_id : null,
      1,
      null,
      1,
      page,
      size,
      searchObj,
      search && !parent_id ? "all" : null,
      [],
      req.user.organization_id,
    );
    // Calculate total size and percentage
    const { length: totalSizeLength, unit } = await calculateTotalSize(
      categories.data,
    );
    let totalSizeInMB = 0;
    if (unit === "B") {
      totalSizeInMB = parseFloat(totalSizeLength.toString()) / (1024 * 1024);
    } else if (unit === "KB") {
      totalSizeInMB = parseFloat(totalSizeLength.toString()) / 1024;
    } else if (unit === "MB") {
      totalSizeInMB = parseFloat(totalSizeLength.toString());
    } else if (unit === "GB") {
      totalSizeInMB = parseFloat(totalSizeLength.toString()) * 1024;
    }

    let limitInMB = global.config.FILE_SIZE_LIMIT;
    let totalStorageLimit = global.config.FILE_SIZE_LIMIT;

    /** check if user has any subscription plan of storage */
    const checkUserStorage: any = await checkUserStorageLimit(
      req.user.organization_id,
    );
    if (checkUserStorage.status) {
      limitInMB += checkUserStorage.limitInMB;
      totalStorageLimit = limitInMB;
    }
    limitInMB = limitInMB * 1024;
    const percentage = (totalSizeInMB / limitInMB) * 100;
    const memoryCalculation = {
      total_size: `${totalSizeLength} ${unit}`,
      limit: `${totalStorageLimit} ${checkUserStorage.subs_storage_size.toUpperCase()}`,
      percentage: `${percentage.toFixed(2)}%`,
    };
    return res.send({
      status: true,
      ...categories,
      memory_calculation: memoryCalculation,
    });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 * get All Own Category list
 * @param req
 * @param res
 * @returns
 */

const getAllOwnCategoryList = async (req: any, res: any) => {
  try {
    const { page, size, search, parent_id, category_use }: any = req.query;

    // Fetch all categories starting from the root (parent_id = null)
    let categories: any = [];
    const searchObj = {
      search,
      departments: req.user.department_id,
      branches: req.user.branch_id,
      category_use,
      category_status: categoryStatus.ACTIVE,
    };
    categories = await fetchCategoriesRecursively(
      req.user.id,
      parent_id ? parent_id : null,
      10,
      categoryType.FOLDER,
      1,
      page,
      size,
      searchObj,
      null,
      [],
      req.user.organization_id,
    );

    return res.send({ status: true, ...categories });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 * get Category details
 * @param req
 * @param res
 * @returns
 */

const getCategoryDetailsById = async (req: any, res: any) => {
  try {
    const { category_id } = req.params;
    const categoryObj: any = {
      where: {
        category_status: { [Op.not]: categoryStatus.DELETED },
        id: category_id,
        organization_id: req.user.organization_id
      },
      attributes: [
        "id",
        "category_name",
        "category_image",
        "category_description",
        [
          sequelize.literal(`(
            SELECT CASE
              WHEN item_location IS NOT NULL AND item_location != ''
              THEN CASE item_external_location
                WHEN 'yes' THEN item_location 
                ELSE CONCAT('${global.config.API_BASE_URL}', item_location)
              END
              ELSE ''
            END
            FROM nv_items WHERE id = DocumentCategory.category_image
          )`),
          "category_image_url",
        ],
        [
          sequelize.literal(`(SELECT id FROM nv_items WHERE id = DocumentCategory.category_image)`),
          "category_image_id"
        ],
        [
          sequelize.literal(
            `(SELECT item_name FROM nv_items WHERE nv_items.id = DocumentCategory.category_image)`,
          ),
          "item_image_name",
        ],
        "category_status",
        "category_type",
        "category_use",
        "parent_id",
        "dashboard_view",
        "has_agreement_required",
        "category_order",
        "is_external_link",
        "has_video_control",
        "updatedAt",
      ],
      order: [["category_order", "ASC"]],
      raw: true,
    };

    const categoriesDetails: any = await DocumentCategory.findOne(categoryObj);

    if (!categoriesDetails) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("CATEGORY_NOT_FOUND"),
      });
    }

    const branchDetails = await Branch.findAll({
      attributes: ["id", "branch_name"],
      where: {
        id: {
          [Op.in]: sequelize.literal(`
            (SELECT branch_id 
            FROM nv_document_category_branch 
            WHERE category_id = ${category_id} 
            AND document_category_branch_status = '${document_category_branch_status.ACTIVE}')
          `),
        },
        organization_id: req.user.organization_id
      },
    });

    const departmentDetails = await Department.findAll({
      attributes: ["id", "department_name"],
      where: {
        id: {
          [Op.in]: sequelize.literal(
            `(select department_id from nv_document_category_deparment where category_id = ${category_id} and document_category_department_status	= '${document_category_department_status.ACTIVE}')`,
          ),
        },
        organization_id: req.user.organization_id
      },
    });

    categoriesDetails.branches = branchDetails;
    categoriesDetails.departments = departmentDetails;

    categoriesDetails.categoryItemDetails =
      await getCategoryItemDetails(category_id);

    return res.send({ status: true, data: categoriesDetails });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

const getCategoryItemDetails = async (category_id: any = null) => {
  const categoryItemDetails = await DocumentCategoryItem.findAll({
    where: {
      category_id: category_id,
      document_category_item_status: document_category_item_status.ACTIVE,
    },
    attributes: [
      "category_id",
      "item_id",
      [
        sequelize.literal(`(
            SELECT CASE
              WHEN item_location IS NOT NULL AND item_location != ''
              THEN CASE item_external_location
                WHEN 'yes' THEN item_location 
                ELSE CONCAT('${global.config.API_BASE_URL}', item_location)
              END
              ELSE ''
            END
            FROM nv_items WHERE id = DocumentCategoryItem.item_id
          )`),
        "item_image_url",
      ],
      [
        sequelize.literal(
          `(SELECT item_name FROM nv_items WHERE nv_items.id = DocumentCategoryItem.item_id)`,
        ),
        "item_image_name",
      ],
      "document_category_item_status",
      "document_category_item_type",
      "document_category_item_link",
      "order",
      "updatedAt",
    ],
    order: [["order", "ASC"]],
    raw: true,
  });

  return categoryItemDetails;
};

/**
 *  category item order
 * @param req
 * @param res
 * @returns
 */

const updateCategoryItemOrder = async (req: any, res: any) => {
  try {
    const { category_id, order } = req.body;

    const findParent = await DocumentCategory.findOne({
      where: {
        id: category_id,
        category_status: { [Op.not]: categoryStatus.DELETED },
        organization_id: req.user.organization_id
      }, raw: true
    });
    if (!findParent) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CATEGORY_NOT_FOUND") });
    }

    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    const findUserRole: any = await Role.findOne({
      attributes: ['role_name'],
      where: {
        id:
          req.headers["platform-type"] == "web"
            ? getUserDetail.web_user_active_role_id
            : getUserDetail.user_active_role_id,
      }, raw: true
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.ACCOUNTANT,
        ROLE_CONSTANT.AREA_MANAGER,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(findUserRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const categoryObj: any = {
      where: {
        category_status: { [Op.not]: categoryStatus.DELETED },
        organization_id: req.user.organization_id,
      },
      attributes: ["id", "category_order"],
      order: [["category_order", "ASC"]],
    };

    categoryObj.where.parent_id = findParent.parent_id
      ? findParent.parent_id
      : null;

    if (
      findUserRole &&
      (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER)
    ) {


      const branch = getUserDetail?.branch_id;
      categoryObj.where[Op.and] = [
        sequelize.literal(
          `(SELECT id FROM nv_document_category_branch WHERE branch_id IN(${String(branch).split(",").map(Number)}) AND category_id = DocumentCategory.id AND document_category_branch_status = 'active' LIMIT 1) `,
        ),
      ];
    }

    const findUserBranch = await UserBranch.findAll({
      where: {
        user_id: req.user.id,
      }, raw: true
    });

    if (
      findUserRole &&
      findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER &&
      findUserBranch.length > 0
    ) {
      const branchIds = findUserBranch.map((branch) => {
        return branch.branch_id;
      });
      const branch = branchIds.join(", ");
      categoryObj.where[Op.and] = [
        sequelize.literal(
          `(SELECT id FROM nv_document_category_branch WHERE branch_id IN(${String(branch).split(",").map(Number)}) AND category_id = DocumentCategory.id AND document_category_branch_status = 'active' LIMIT 1) `,
        ),
      ];
    }

    const categories = await DocumentCategory.findAll(categoryObj);
    const oldPosition = findParent.category_order;
    const newPosition = order;
    categories.forEach(async (item) => {
      let new_order = item.category_order;
      if (item.id !== category_id) {
        // If the item's position is between the old position and new position
        if (
          oldPosition < newPosition &&
          item.category_order > oldPosition &&
          item.category_order <= newPosition
        ) {
          new_order = item.category_order - 1; // Move up
        }
        if (
          oldPosition > newPosition &&
          item.category_order < oldPosition &&
          item.category_order >= newPosition
        ) {
          new_order = item.category_order + 1; // Move down
        }
      } else {
        new_order = order;
      }
      await DocumentCategory.setHeaders(req).update(
        {
          category_order: new_order,
          updated_by: req.user.id,
        },
        {
          where: {
            id: item.id,
            organization_id: req.user.organization_id,
          },
        },
      );
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CATEGORY_ITEM_ORDER_UPDATED"),
    });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 *  update item order
 * @param req
 * @param res
 * @returns
 */

const updateItemOrder = async (req: any, res: any) => {
  try {
    const { item_list } = req.body;
    const { category_id }: any = req.params;

    const findParent = await DocumentCategory.findOne({
      where: {
        id: category_id,
        category_status: { [Op.not]: categoryStatus.DELETED },
        organization_id: req.user.organization_id
      },
    });
    if (!findParent) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ITEM_NOT_FOUND") });
    }

    for (let i = 0; item_list.length > i; i++) {
      const findCategory = await DocumentCategoryItem.findOne({
        where: { item_id: item_list[i], document_category_item_status: document_category_item_status.ACTIVE }, raw: true
      });

      if (findCategory) {
        await DocumentCategoryItem.setHeaders(req).update(
          {
            order: i + 1,
            updated_by: req.user.id,
          },
          {
            where: {
              item_id: item_list[i],
            },
          },
        );
      }
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CATEGORY_ITEM_ORDER_UPDATED"),
    });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 *  move category
 * @param req
 * @param res
 * @returns
 */

const moveCategory = async (req: any, res: any) => {
  try {
    const { to_move_id, from_move_id }: any = req.body;
    const { error } = await documentCategoryValidator.moveCategory.validate(
      req.body,
    );
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    if (to_move_id == from_move_id) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({
          status: false,
          message: res.__("CANNOT_MOVE_SAME_CATEGORY_INTO_SAME_CATEGORY"),
        });
    }
    const findParent = await DocumentCategory.findOne({
      where: {
        id: to_move_id,
        category_status: { [Op.not]: categoryStatus.DELETED },
        organization_id: req.user.organization_id
      }, raw: true
    });
    if (!findParent) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ITEM_NOT_FOUND") });
    }

    if (!findParent.parent_id) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CAN'T_MOVE_PARENT_FOLDER") });
    }

    const findFromMoveId: any = await DocumentCategory.findOne({ where: { id: from_move_id, category_status: { [Op.not]: categoryStatus.DELETED }, organization_id: req.user.organization_id }, raw: true })

    if (!findFromMoveId) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("MOVE_DESTINATION_NOT_FOUND") });
    }

    if (findFromMoveId.category_type != categoryType.FOLDER) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CAN'T_MOVE_FILE_TYPE") });
    }

    let currentLevel = 1;
    let parentCategory = findFromMoveId;

    // Traverse up the parent chain to determine the level
    while (parentCategory && parentCategory.parent_id) {
      currentLevel++;
      parentCategory = await DocumentCategory.findOne({
        where: { id: parentCategory.parent_id, organization_id: req.user.organization_id }, raw: true
      });

      if (currentLevel >= global.config.CATEGORY_CREATE_LIMIT) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("CANNOT_MOVE_BEYOND_10_LEVELS"),
        });
      }
    }
    if (findFromMoveId.category_use != findParent.category_use) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_MOVE_INTO_DIFFERENT_CATEGORY_USE"),
      });
    }
    if (findFromMoveId.category_use == categoryUse.TRAINING) {
      const findToMoveId: any = await DocumentCategory.findOne({ where: { id: to_move_id, category_status: { [Op.not]: categoryStatus.DELETED }, organization_id: req.user.organization_id }, raw: true })
      if (findFromMoveId.parent_id) {
        const findParentOfParent = await DocumentCategory.findOne({ where: { id: findFromMoveId.parent_id, organization_id: req.user.organization_id }, raw: true })
        const findCategory = await DocumentCategory.findOne({ where: { id: findToMoveId.id, category_type: categoryType.FOLDER, organization_id: req.user.organization_id }, raw: true })
        if (findParentOfParent && findParentOfParent.category_type == categoryType.FOLDER && findParentOfParent.category_use == categoryUse.TRAINING && findFromMoveId.category_type == categoryType.FOLDER && findCategory) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__("CAN'T_MOVE_FOLDER_MORE_THAN_ONE_IN_TRANING"),
          });
        }

      } else {
        if (findFromMoveId.category_type == categoryType.FOLDER && findToMoveId.category_type == categoryType.FILE) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__("CAN'T_MOVE_FILE_MORE_THAN_ONE_IN_TRANING"),
          });
        }
        const findCategory = await DocumentCategory.findOne({ where: { parent_id: findToMoveId.id, category_type: categoryType.FOLDER, organization_id: req.user.organization_id }, raw: true })
        if (findCategory && findToMoveId.category_type == categoryType.FOLDER && findFromMoveId.category_type == categoryType.FOLDER) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__("CAN'T_MOVE_FOLDER_MORE_THAN_ONE_IN_TRANING"),
          });
        }
      }
    }

    if (findFromMoveId.id == findParent.parent_id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CATEGORY_ALREADY_EXIST"),
      });
    }

    // Get all ancestors of the from_move_id
    const allAncestors = await getAllAncestors(from_move_id);

    // Check if to_move_id is an ancestor of from_move_id (moving upwards in the hierarchy)
    if (allAncestors.includes(to_move_id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CAN'T_MOVE_INTO_CHILD"),
      });
    }

    const findBranch = await DocumentCategoryBranch.findAll({ where: { category_id: findParent.id, document_category_branch_status: { [Op.not]: document_category_branch_status.DELETED } } })
    const branch_ids = findBranch.length > 0 ? findBranch.map((branch: any) => { return branch.branch_id }) : []
    const findDepartment = await DocumentCategoryDepartment.findAll({ where: { category_id: findParent.id, document_category_department_status: { [Op.not]: document_category_department_status.DELETED } } })
    const department_ids = findDepartment.length > 0 ? findDepartment.map((department: any) => { return department.department_id }) : []
    const moveCategory = await DocumentCategory.update({ parent_id: from_move_id, updated_by: req.user.id }, { where: { id: findParent.id, organization_id: req.user.organization_id } })
    if (moveCategory.length > 0) {
      await updateBranchesAndDepartmentsInAllParents(
        findParent.id,
        branch_ids,
        department_ids,
        req,
      );
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("CATEGORY_MOVED_SUCCESSFULLY"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAILED_TO_MOVE_CATEGORY"),
      });
    }
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 *  delete item
 * @param req
 * @param res
 * @returns
 */

const deleteCategory = async (req: any, res: any) => {
  try {
    const { category_ids = [] }: any = req.body;
    if (category_ids.length > 0) {
      const findCategory = await DocumentCategory.findAll({ attributes: ['id'], where: { id: { [Op.in]: category_ids }, organization_id: req.user.organization_id }, raw: true })
      if (findCategory.length) {
        if (findCategory.length != category_ids.length) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__("CATEGORY_NOT_FOUND"),
          });
        }


        const findExistHealthSafetyCategory = await HealthSafetyCategoryItem.findAll({
          where: { category_id: { [Op.in]: category_ids }, status: status.ACTIVE },
          raw: true,
        });

        if (findExistHealthSafetyCategory.length > 0) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__("CANNOT_DELETE_CATEGORY_WITH_BRANCH_ASSIGNMENTS"),
          });
        } else {
          for (const category of findCategory) {
            const childCategoryIds = await findAllChildren([category.id]);
            // Mark parent category and all its children as deleted
            const allCategoryIds = [category.id, ...childCategoryIds];

            const deleteCategory = await DocumentCategory.update({ category_status: categoryStatus.DELETED }, { where: { id: allCategoryIds, organization_id: req.user.organization_id } })
            if (deleteCategory.length > 0) {

              await DocumentCategoryItem.update({ document_category_item_status: document_category_item_status.DELETED }, { where: { category_id: allCategoryIds } })
              await DocumentCategoryBranch.update({ document_category_branch_status: document_category_branch_status.DELETED }, { where: { category_id: allCategoryIds } })
              await DocumentCategoryDepartment.update({ document_category_department_status: document_category_department_status.DELETED }, { where: { category_id: allCategoryIds } })
            }
          }
          return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("CATEGORY_DELETE_SUCCESSFULLY"),
          });
        }
      }
    }
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 *  Copy category
 * @param req
 * @param res
 * @returns
 */

const copyCategory = async (req: any, res: any) => {
  try {
    const { category_ids = [], from_copy_id }: any = req.body;
    if (category_ids.length > 0) {
      const findCategory = await DocumentCategory.findAll({ where: { id: category_ids, organization_id: req.user.organization_id }, raw: true });
      if (findCategory.length > 0) {
        if (findCategory.length != category_ids.length) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__("CATEGORY_NOT_FOUND"),
          });
        } else {
          const findDestination = await DocumentCategory.findOne({ where: { id: from_copy_id, category_status: { [Op.not]: categoryStatus.DELETED }, organization_id: req.user.organization_id }, raw: true })
          if (!findDestination) {
            return res.status(StatusCodes.BAD_REQUEST).json({
              status: false,
              message: res.__("DESTINATION_CATEGORY_NOT_FOUND"),
            });
          }
          if (findDestination.category_use == categoryUse.TRAINING) {
            for (const cat of findCategory) {
              if (findDestination.parent_id) {
                const findParentOfParent = await DocumentCategory.findOne({ where: { id: findDestination.parent_id, organization_id: req.user.organization_id }, raw: true })
                const findCategory = await DocumentCategory.findOne({ where: { id: cat.id, category_type: categoryType.FOLDER, organization_id: req.user.organization_id }, raw: true })
                if (findParentOfParent && findParentOfParent.category_type == categoryType.FOLDER && findParentOfParent.category_use == categoryUse.TRAINING && findDestination.category_type == categoryType.FOLDER && findCategory) {
                  return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__(
                      "CAN'T_COPY_FOLDER_MORE_THAN_ONE_IN_TRANING",
                    ),
                  });
                }
              } else {
                if (findDestination.category_type == categoryType.FOLDER && cat.category_type == categoryType.FILE) {
                  return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("CAN'T_COPY_FILE_MORE_THAN_ONE_IN_TRANING"),
                  });
                }
                const findCategory = await DocumentCategory.findOne({ where: { parent_id: cat.id, category_type: categoryType.FOLDER, organization_id: req.user.organization_id }, raw: true })
                if (findCategory && cat.category_type == categoryType.FOLDER && findDestination.category_type == categoryType.FOLDER) {
                  return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__(
                      "CAN'T_COPY_FOLDER_MORE_THAN_ONE_IN_TRANING",
                    ),
                  });
                }
              }
            }
          }
        }
        for (const cat of findCategory) {
          await copyCategoryWithChildren(cat, from_copy_id, req);
        }
        if (!res.headersSent) {
          return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("CATEGORY_COPY_SUCCESSFULLY"),
          });
        }
      }
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CATEGORY_NOT_FOUND"),
      });
    }
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

// Recursive function to get all ancestors of a category
const getAllAncestors = async (
  categoryId: number,
  ancestors: number[] = [],
): Promise<number[]> => {
  let currentCategory = await DocumentCategory.findOne({
    where: {
      id: categoryId,
      category_status: { [Op.not]: categoryStatus.DELETED },
    },
    attributes: ["parent_id"],
    raw: true
  });

  while (currentCategory && currentCategory.parent_id) {
    ancestors.push(currentCategory.parent_id);
    currentCategory = await DocumentCategory.findOne({
      where: {
        id: currentCategory.parent_id,
        category_status: { [Op.not]: categoryStatus.DELETED },
      },
      attributes: ["parent_id"],
      raw: true
    });
  }

  return ancestors;
};

const copyCategoryWithChildren = async (
  category: any,
  destination_id: any,
  req: any,
) => {
  try {
    const findDestinationCategory = await DocumentCategory.findOne({ where: { id: destination_id, organization_id: req.user.organization_id }, raw: true })
    // Start with the original name and append "- Copy"
    const baseName = `${category.category_name} - Copy`;
    let newCategoryName = baseName;
    let count = 1;
    let exists = true;

    while (exists) {
      const existingCategory = await DocumentCategory.findOne({
        where: {
          category_name: newCategoryName,
          category_status: { [Op.not]: categoryStatus.DELETED },
          organization_id: req.user.organization_id
        }, raw: true
      });

      if (existingCategory) {
        // If the name already exists, append (count) and increase it for the next check
        newCategoryName = `${baseName} (${count})`;
        count++;
      } else {
        exists = false;
      }
    }
    const newCategory = await DocumentCategory.create({
      category_image: category.category_image,
      category_name: newCategoryName,
      category_description: category.category_description,
      category_status: category.category_status,
      category_type: category.category_type,
      // category_use: category.category_use,
      category_use:
        findDestinationCategory && findDestinationCategory?.category_use
          ? findDestinationCategory?.category_use
          : null,
      parent_id:
        findDestinationCategory && findDestinationCategory?.id
          ? findDestinationCategory?.id
          : null,
      dashboard_view: category.dashboard_view,
      has_agreement_required: category.has_agreement_required,
      category_order: category.category_order,
      is_external_link: category.is_external_link,
      has_video_control: category.has_video_control,
      created_by: req.user.id,
      updated_by: req.user.id,
      organization_id: req.user.organization_id
    } as any);

    if (newCategory) {
      // Copy branches
      const findBranch = await DocumentCategoryBranch.findAll({
        where: {
          category_id: category.id,
          document_category_branch_status: category.category_status,
        }, raw: true
      });
      const branch_ids =
        findBranch.length > 0
          ? findBranch.map((branch) => {
            return branch.branch_id;
          })
          : [];
      if (findBranch.length > 0) {
        for (const branch of findBranch) {
          await DocumentCategoryBranch.create({
            category_id: newCategory.id,
            branch_id: branch.branch_id,
            document_category_branch_status:
              branch.document_category_branch_status,
            created_by: req.user.id,
            updated_by: req.user.id,
          } as any);
        }
      }

      // Copy departments
      const findDepartment = await DocumentCategoryDepartment.findAll({
        where: {
          category_id: category.id,
          document_category_department_status: category.category_status,
        }, raw: true
      });
      const department_ids =
        findDepartment.length > 0
          ? findDepartment.map((department) => {
            return department.department_id;
          })
          : [];
      if (findDepartment.length > 0) {
        for (const department of findDepartment) {
          await DocumentCategoryDepartment.create({
            category_id: newCategory.id,
            department_id: department.department_id,
            document_category_department_status:
              department.document_category_department_status,
            created_by: req.user.id,
            updated_by: req.user.id,
          } as any);
        }
      }
      await updateBranchesAndDepartmentsInAllParents(
        newCategory.parent_id,
        branch_ids,
        department_ids,
        req,
      );
      // Copy items (if category type is file)
      if (category.category_type === categoryType.FILE) {
        const findItems = await DocumentCategoryItem.findAll({
          where: {
            category_id: category.id,
            document_category_item_status: category.category_status,
          }, raw: true
        });
        if (findItems.length > 0) {
          for (const item of findItems) {
            await DocumentCategoryItem.create({
              category_id: newCategory.id,
              item_id: item?.item_id,
              document_category_item_status: item.document_category_item_status,
              document_category_item_type: item.document_category_item_type,
              document_category_item_link: item.document_category_item_link,
              order: item.order,
              created_by: req.user.id,
              updated_by: req.user.id,
            } as any);
          }
        }
      }
      await copyChildCategory(category, newCategory.id, req);
    }
    return newCategory;
  } catch (error) {
    console.log("error", error);
    return error;
  }
};
/**
 *  get User Statistics
 * @param req
 * @param res
 * @returns
 */

const getUserStatisticsList = async (req: any, res: any) => {
  try {
    // Get the filters from request query params
    const { size, page, category_id, search_category_id }: any = req.query;

    const getUserList = await getUserStatisticsDetails(
      req.user.id,
      size,
      page,
      category_id,
      search_category_id,
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      ...getUserList,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getUserStatisticsDetails = async (
  login_user_id: any,
  size: any = null,
  page: any = null,
  category_id: any = null,
  search_category_id: any = null,
  user_ids: any = null,
) => {
  const { limit, offset } = getPagination(Number(page), Number(size));


  const getUserDetail: any = await User.findOne({
    attributes: ['id', 'organization_id', 'web_user_active_role_id'],
    where: { id: login_user_id }, raw: true
  });

  let whereQry = ` AND dc.parent_id IS NULL AND dc.organization_id = '${getUserDetail.organization_id}'`
  if (category_id)
    whereQry = ` AND dc.parent_id = ${category_id}`

  if (search_category_id) {
    const searchCategory = search_category_id
      .split(",")
      .map((id: string) => Number(id));
    whereQry += ` AND dc.id IN (${searchCategory})`;
  }


  const findUserRole: any = await Role.findOne({
    attributes: ['id', 'role_name'],
    where: { id: getUserDetail.web_user_active_role_id }, raw: true
  });
  /** Get all super admin user id's */
  const getUserId = await getSuperAdminUserId(getUserDetail.organization_id)
  const excludedIds = [login_user_id, ...getUserId]; // Combine req.user.id with fetched IDs
  const whereObj: any = {
    organization_id: getUserDetail.organization_id,
    user_status: {
      [Op.not]: [user_status.CANCELLED],
    },
    id: {
      [Op.not]: excludedIds, //remove static super admin id and add all super admin id array
    },
  };

  whereObj.branch_id = {
    [Op.in]: [
      sequelize.literal(
        `SELECT dcb.branch_id
            FROM nv_document_category_branch AS dcb
            JOIN nv_document_category AS dc ON dc.id = dcb.category_id
            WHERE dc.category_status != 'deleted' ${whereQry}
            GROUP BY dcb.branch_id`,
      ),
    ],
  };

  whereObj.department_id = {
    [Op.in]: [
      sequelize.literal(
        `(SELECT dcd.department_id 
            FROM nv_document_category_deparment AS dcd
            JOIN nv_document_category AS dc ON dc.id = dcd.category_id
            WHERE dc.category_status != 'deleted' ${whereQry}
            GROUP BY dcd.department_id)`,
      ),
    ],
  };

  whereObj[Op.and] = [
    sequelize.literal(
      `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1,2))) `,
    ),
  ];

  if (user_ids) {
    const userIds = user_ids.split(",").map((id: string) => Number(id));
    whereObj.id = {
      [Op.in]: userIds,
    };
  }

  const getUserListQuery: any = {
    attributes: [
      "id",
      [
        sequelize.fn(
          "concat",
          sequelize.col("user_first_name"),
          " ",
          sequelize.col("user_last_name"),
        ),
        "user_full_name",
      ],
      "user_status",
      "user_email",
      [
        sequelize.literal(
          `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = User.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', User.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = User.user_avatar))
          END`
        ),
        "user_avatar_link",
      ],
      "user_avatar",
      [sequelize.literal(`(User.user_avatar)`), "user_avatar_id"],
      [
        sequelize.literal(
          `(
            SELECT MAX(updatedAt) FROM nv_document_category_item_track WHERE document_category_item_track_status = 'completed' AND user_id = User.id AND updatedAt IS NOT NULL
          )`,
        ),
        "last_view",
      ],
      [
        sequelize.literal(
          `(
             (
            SELECT MIN(createdAt) FROM nv_document_category_item_track WHERE document_category_item_track_status = 'completed' AND user_id = User.id AND createdAt IS NOT NULL
          )
          )`,
        ),
        "intial_view",
      ],
    ],
    where: whereObj,
    nest: true,
    raw: true,
    include: [
      {
        model: Branch,
        as: "branch",
        attributes: ["id", "branch_name"],
      },
      {
        model: Department,
        as: "department",
        attributes: ["id", "department_name"],
      },
    ],
  };
  if (page && size) {
    getUserListQuery.limit = Number(limit);
    getUserListQuery.offset = Number(offset);
  }

  if (
    findUserRole &&
    (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
      findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
      findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER) &&
    getUserDetail.branch_id
  ) {
    const getUserBranch = await UserBranch.findAll({
      where: {
        user_id: getUserDetail.id,
      },
      raw: true,
    });
    if (
      findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER &&
      getUserBranch.length > 0
    ) {
      const branchIds = getUserBranch.map((branch) => {
        return branch.branch_id;
      });
      whereObj.branch_id = {
        [Op.in]: branchIds,
      };
    } else {
      whereObj.branch_id = getUserDetail.branch_id;
    }
    // Construct recursive query to find child roles
    const getChildRolesQuery = `
        WITH RECURSIVE ChildRoles AS (
          SELECT id, role_name, parent_role_id
          FROM nv_roles
          WHERE id = :activeRoleId
          UNION ALL
          SELECT r.id, r.role_name, r.parent_role_id
          FROM nv_roles r
          INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
        )
        SELECT id
        FROM ChildRoles
        WHERE id != :activeRoleId`;

    // Execute recursive query to find child roles
    const getChildRoles = await sequelize.query(getChildRolesQuery, {
      replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
      type: QueryTypes.SELECT,
    });

    // Build WHERE clause for user roles based on child roles
    let whereStr = "";
    getChildRoles.forEach((child_role: any, index: number) => {
      if (index > 0) {
        whereStr += " OR ";
      }
      whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = User.id)) > 0`;
    });

    if (whereStr) {
      whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
    }
  }

  const getUserList = await User.findAll(getUserListQuery);
  delete getUserListQuery.attributes;
  const result = await Promise.all(
    getUserList.map(async (user: any) => {
      const searchObj = {
        departments: user.department_id,
        branches: user.branch_id,
      };

      return {
        ...user, // Convert Sequelize instance to plain object
        ...(await getUserItemTrackDetails(
          user.id,
          category_id,
          searchObj,
          search_category_id,
        )),
      };
    }),
  );
  const getUserCount: any = await User.count(getUserListQuery);
  const { total_pages } = getPaginatedItems(size, page, getUserCount || 0);

  return {
    data: result,
    count: getUserCount,
    page: parseInt(page),
    size: parseInt(size),
    total_pages,
  };
}

const getUserItemTrackDetails = async (login_user_id: any = null, category_id: any = null, searchObj: any = null, search_category_id: any = null) => {

  const findLoginUser: any = await User.findOne({
    where: { id: login_user_id },
    attributes: ['organization_id'],
    raw: true
  });

  let departmentWhere = '';
  if (typeof searchObj.departments !== 'undefined' && searchObj.departments) {
    departmentWhere = `AND (SELECT department_id FROM nv_document_category_deparment WHERE department_id IN(${String(searchObj.departments).split(",").map(Number)}) AND category_id = id AND document_category_department_status = 'active' LIMIT 1) `
  }

  let branchWhere = "";
  if (typeof searchObj.branches !== "undefined" && searchObj.branches) {
    branchWhere = `AND (SELECT branch_id FROM nv_document_category_branch WHERE branch_id IN(${String(searchObj.branches).split(",").map(Number)}) AND category_id = id AND document_category_branch_status = 'active' LIMIT 1) `;
  }

  let whereQry = `id IN(SELECT id FROM nv_document_category WHERE parent_id IS NULL AND organization_id = '${findLoginUser.organization_id}') AND category_status != 'deleted'`;
  if (category_id) {
    whereQry = ` id IN(SELECT id FROM nv_document_category WHERE parent_id = ${category_id}) AND category_status != 'deleted'`;
  }

  if (search_category_id) {
    const searchCategory = search_category_id
      .split(",")
      .map((id: string) => Number(id));
    whereQry += `AND id IN(${searchCategory})`;
  }

  const getChildCategoryQuery = `
    WITH RECURSIVE ChildRoles AS (
      SELECT id, category_name, parent_id, category_type, category_status
      FROM nv_document_category
      WHERE ${whereQry} AND category_status != 'deleted' 
      UNION ALL
      SELECT r.id, r.category_name, r.parent_id, r.category_type, r.category_status
      FROM nv_document_category r
      INNER JOIN ChildRoles cr ON r.parent_id = cr.id
      WHERE r.category_status != 'deleted'
    )
    SELECT GROUP_CONCAT(id SEPARATOR ',') AS category_ids 
    FROM ChildRoles
    WHERE category_type = 'file' AND category_status != 'deleted'  ${departmentWhere} ${branchWhere} 
  `;

  // Execute recursive query to find child roles
  const getChildCategory = await sequelize.query(getChildCategoryQuery, {
    type: QueryTypes.SELECT,
  });

  const total_items =
    getChildCategory && getChildCategory[0]?.category_ids
      ? getChildCategory[0]?.category_ids.split(",")
      : [];

  const categoryIdsAsNumbers = total_items?.map((id: string) => Number(id));
  const total_track = await DocumentCategoryItemTrack.count({
    where: {
      category_id: { [Op.in]: categoryIdsAsNumbers },
      user_id: login_user_id,
      document_category_item_track_status:
        document_category_item_track_status.COMPLETED,
    },
  });

  const trackPercentage = Number(
    ((total_track * 100) / total_items.length).toFixed(),
  );

  return {
    total_items: total_items.length,
    total_track: total_track,
    track_percentage: trackPercentage,
  };
};

export const copyChildCategory = async (
  category: any,
  newCategory_id: number,
  req: any,
) => {
  try {
    const findDestinationCategory = await DocumentCategory.findOne({ where: { id: newCategory_id, organization_id: req.user.organization_id }, raw: true })
    const getChildEntry = await findAllChildren([category.id], false)
    if (getChildEntry.length > 0) {
      for (const child of getChildEntry) {
        const findChildDetail: any = await DocumentCategory.findOne({
          where: { id: child }, raw: true
        });
        let newChildCategory: any;
        if (findChildDetail) {
          // Start with the original name and append "- Copy"
          const baseName = `${findChildDetail.category_name} - Copy`;
          let newCategoryName = baseName;
          let count = 1;
          let exists = true;

          while (exists) {
            const existingCategory = await DocumentCategory.findOne({
              where: {
                category_name: newCategoryName,
                category_status: { [Op.not]: categoryStatus.DELETED },
                organization_id: req.user.organization_id
              }, raw: true
            });

            if (existingCategory) {
              // If the name already exists, append (count) and increase it for the next check
              newCategoryName = `${baseName} (${count})`;
              count++;
            } else {
              exists = false;
            }
          }
          newChildCategory = await DocumentCategory.create({
            category_image: findChildDetail.category_image,
            category_name: newCategoryName,
            category_description: findChildDetail.category_description,
            category_status: findChildDetail.category_status,
            category_type: findChildDetail.category_type,
            // category_use: findChildDetail.category_use,
            category_use:
              findDestinationCategory && findDestinationCategory?.category_use
                ? findDestinationCategory?.category_use
                : null,
            parent_id: newCategory_id,
            dashboard_view: findChildDetail.dashboard_view,
            has_agreement_required: findChildDetail.has_agreement_required,
            category_order: findChildDetail.category_order,
            is_external_link: findChildDetail.is_external_link,
            has_video_control: findChildDetail.has_video_control,
            created_by: req.user.id,
            updated_by: req.user.id,
            organization_id: req.user.organization_id
          } as any);

          if (!newChildCategory) return null;

          // Copy branches
          const findBranch = await DocumentCategoryBranch.findAll({
            where: { category_id: child }, raw: true
          });
          const childBranch_ids =
            findBranch.length > 0
              ? findBranch.map((branch) => {
                return branch.branch_id;
              })
              : [];
          if (findBranch.length > 0) {
            for (const branch of findBranch) {
              await DocumentCategoryBranch.create({
                category_id: newChildCategory.id,
                branch_id: branch.branch_id,
                document_category_branch_status:
                  branch.document_category_branch_status,
                created_by: req.user.id,
                updated_by: req.user.id,
              } as any);
            }
          }

          // Copy departments
          const findDepartment = await DocumentCategoryDepartment.findAll({
            where: { category_id: child }, raw: true
          });
          const childDepartment_ids =
            findDepartment.length > 0
              ? findDepartment.map((department) => {
                return department.department_id;
              })
              : [];
          if (findDepartment.length > 0) {
            for (const department of findDepartment) {
              await DocumentCategoryDepartment.create({
                category_id: newChildCategory.id,
                department_id: department.department_id,
                document_category_department_status:
                  department.document_category_department_status,
                created_by: req.user.id,
                updated_by: req.user.id,
              } as any);
            }
          }
          await updateBranchesAndDepartmentsInAllParents(
            newChildCategory.id,
            childBranch_ids,
            childDepartment_ids,
            req,
          );
          // Copy items (if category type is file)
          if (category.category_type === categoryType.FILE) {
            const findItems = await DocumentCategoryItem.findAll({
              where: { category_id: child }, raw: true
            });
            if (findItems.length > 0) {
              for (const item of findItems) {
                await DocumentCategoryItem.create({
                  category_id: newChildCategory.id,
                  item_id: item?.item_id,
                  document_category_item_status:
                    item.document_category_item_status,
                  document_category_item_type: item.document_category_item_type,
                  document_category_item_link: item.document_category_item_link,
                  order: item.order,
                  created_by: req.user.id,
                  updated_by: req.user.id,
                } as any);
              }
            }
          }
          const getChildEntry = await findAllChildren(
            [findChildDetail.id],
            false,
          );
          if (getChildEntry.length > 0) {
            await copyChildCategory(findChildDetail, newChildCategory.id, req);
          }
        }
      }
    }
  } catch (error) {
    console.log("error", error);
    return null;
  }
};
/**
 *  track category
 * @param req
 * @param res
 * @returns
 */

const trackCategory = async (req: any, res: any) => {
  try {
    const { category_id }: any = req.body;
    const { error } = await documentCategoryValidator.trackCategory.validate(
      req.body,
    );

    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }

    const findCategory = await DocumentCategory.findOne({
      where: {
        id: category_id,
        category_status: { [Op.not]: categoryStatus.DELETED },
        organization_id: req.user.organization_id
      }, raw: true
    });
    if (!findCategory) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ITEM_NOT_FOUND") });
    }

    if (findCategory.category_type == categoryType.FOLDER) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({
          status: false,
          message: res.__("ERROR_CANNOT_TRACK_CATEGORY_FILE_TYPE"),
        });
    }

    const findCategoryTrack = await DocumentCategoryItemTrack.findOne({
      where: {
        category_id: category_id,
        user_id: req.user.id,
      },
      raw: true,
    });

    if (findCategoryTrack) {
      await DocumentCategoryItemTrack.setHeaders(req).update(
        {
          document_category_item_track_status:
            document_category_item_track_status.COMPLETED,
          updated_by: req.user.id,
        },
        {
          where: {
            category_id: category_id,
            user_id: req.user.id,
          },
        },
      );
    } else {
      await DocumentCategoryItemTrack.setHeaders(req).create({
        user_id: req.user.id,
        category_id: category_id,
        document_category_item_track_status:
          document_category_item_track_status.COMPLETED,
        created_by: req.user.id,
      } as any);
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("TRACK_CATEGORY_SUCCESSFULLY"),
    });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 *  restore track category
 * @param req
 * @param res
 * @returns
 */

const restoreTrackCategory = async (req: any, res: any) => {
  try {
    const { user_id, category_id, search_category_id }: any = req.body;
    const { error } =
      await documentCategoryValidator.restoreTrackCategory.validate(req.body);

    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }

    const getUserList = await getUserStatisticsDetails(req.user.id, null, null, category_id, search_category_id, user_id)

    await Promise.all(getUserList.data.map(async (user: any) => {
      const user_id = user.id;
      const getUserDetail: any = await User.findOne({
        attributes: ['id', 'department_id', 'branch_id', 'appToken', 'webAppToken', 'user_first_name', 'user_middle_name', 'user_last_name'],
        where: { id: user_id, organization_id: req.user.organization_id }, raw: true
      });
      let departmentWhere = '';
      if (getUserDetail && getUserDetail.department_id) {
        departmentWhere = `AND (SELECT department_id FROM nv_document_category_deparment WHERE department_id IN(${getUserDetail.department_id}) AND category_id = id AND document_category_department_status = 'active' LIMIT 1) `

      }

      let branchWhere = '';
      if (getUserDetail && getUserDetail.branch_id) {
        branchWhere = `AND (SELECT branch_id FROM nv_document_category_branch WHERE branch_id IN(${getUserDetail.branch_id}) AND category_id = id AND document_category_branch_status = 'active' LIMIT 1) `
      }

      let whereQry = `id IN(SELECT id FROM nv_document_category WHERE parent_id IS NULL AND category_status != 'deleted') `;
      if (category_id) {
        whereQry = ` id IN(SELECT id FROM nv_document_category WHERE parent_id = ${category_id} AND category_status != 'deleted')`
      }

      if (search_category_id) {
        const searchCategory = search_category_id.split(',').map((id: string) => Number(id));
        whereQry += `AND id IN(${searchCategory})`
      }

      const getChildCategoryQuery = `
        WITH RECURSIVE ChildRoles AS (
          SELECT id, category_name, parent_id, category_type, category_status
          FROM nv_document_category
          WHERE ${whereQry} AND category_status != 'deleted'
          UNION ALL
          SELECT r.id, r.category_name, r.parent_id, r.category_type, r.category_status
          FROM nv_document_category r
          INNER JOIN ChildRoles cr ON r.parent_id = cr.id
          WHERE r.category_status != 'deleted'
        )
        SELECT GROUP_CONCAT(id SEPARATOR ',') AS category_ids 
        FROM ChildRoles
        WHERE category_type = 'file' AND category_status != 'deleted'  ${departmentWhere} ${branchWhere}`;

      // Execute recursive query to find child roles
      const getChildCategory = await sequelize.query(getChildCategoryQuery, {
        type: QueryTypes.SELECT,
      });

      const total_items = getChildCategory && getChildCategory[0]?.category_ids ? getChildCategory[0]?.category_ids.split(',') : [];
      const categoryIdsAsNumbers = total_items?.map((id: string) => Number(id));

      for (const cid of categoryIdsAsNumbers) {
        await DocumentCategoryItemTrack.setHeaders(req).update(
          {
            document_category_item_track_status: document_category_item_track_status.PENDING,
            updated_by: req.user.id
          },
          {
            where: {
              category_id: cid,
              user_id: user_id,
            },
            silent: true,
          },
        );
      }
      if (categoryIdsAsNumbers.length > 0) {
        const getParentCategoryQuery = `
        WITH RECURSIVE ParentRoles AS (
            SELECT id, parent_id
            FROM nv_document_category
            WHERE id IN (${categoryIdsAsNumbers})  
            UNION ALL
            SELECT r.id, r.parent_id
            FROM nv_document_category r
            INNER JOIN ParentRoles pr ON r.id = pr.parent_id
        )
        SELECT GROUP_CONCAT(id SEPARATOR ',') AS root_parent_id
        FROM ParentRoles
        WHERE parent_id IS NULL`;

        // Execute recursive query to find child roles
        const getParentCategory = await sequelize.query(getParentCategoryQuery, {
          type: QueryTypes.SELECT,
        });

        const total_parent_items = getParentCategory && getParentCategory[0]?.root_parent_id ? getParentCategory[0]?.root_parent_id.split(',') : [];

        const parentIdsAsNumbers = total_parent_items?.map((id: string) => Number(id));

        const findCategoryTrack = await HealthSafetyCategoryItem.findAll({
          where: {
            category_id: { [Op.in]: parentIdsAsNumbers },
            branch_id: getUserDetail.branch_id,
            status: status.ACTIVE
          },
        });
        if (findCategoryTrack.length > 0) {
          await UserCheckList.setHeaders(req).update({
            status: check_list_status.PENDING,
            is_last_rejected: true
          }, {
            where: {
              to_user_id: user_id,
              from_user_id: user_id,
              checklist_id: 3
            }
          });

        }
        const employeeName = [];

        if (getUserDetail?.user_first_name) {
          employeeName.push(getUserDetail.user_first_name);
        }
        if (getUserDetail?.user_middle_name) {
          employeeName.push(getUserDetail.user_middle_name);
        }
        if (getUserDetail?.user_last_name) {
          employeeName.push(getUserDetail.user_last_name);
        }
        // Extracting category names
        await createNotification([getUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.USER_CATEGORY_RESTORE.content(employeeName.join(" ")), NOTIFICATIONCONSTANT.USER_CATEGORY_RESTORE.heading, REDIRECTION_TYPE.CATEGORY, null, null)
      }
    }))
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RESTORE_TRACK_CATEGORY_SUCCESSFULLY"),
    });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 * get Category list
 * @param req
 * @param res
 * @returns
 */

const getBranchCategoryList = async (req: any, res: any) => {
  try {
    const { search }: any = req.query;
    const branch: any = req.params.branch_id ? `${req.params.branch_id}` : null

    // Fetch all categories starting from the root (parent_id = null)
    let categories: any = [];
    const searchObj = { search, departments: null, branches: branch, category_use: categoryUse.TRAINING, category_status: categoryStatus.ACTIVE }
    categories = await fetchCategoriesRecursively(req.user.id, null, 1, null, 1, null, null, searchObj, null, [], req.user.organization_id);

    return res.send({ status: true, ...categories });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 * get Category list
 * @param req
 * @param res
 * @returns
 */

const addCategoryIntoHealthCategory = async (req: any, res: any) => {
  try {
    const { category_id = [], branch_id } = req.body;
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id'],
      where: {
        id: req.user.id,
        organization_id: req.user.organization_id,
        user_status: {
          [Op.notIn]: [user_status.DELETED, user_status.CANCELLED],
        },
      },
      raw: true,
    });
    const findUserRole: any = await Role.findOne({
      attributes: ['id', 'role_name'],
      where: {
        id:
          req.headers["platform-type"] == "web"
            ? getUserDetail.web_user_active_role_id
            : getUserDetail.user_active_role_id,
      },
      raw: true,
    });
    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(findUserRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }
    const findCategoryExist = await DocumentCategory.findAll({
      where: {
        id: { [Op.in]: category_id },
        category_status: categoryStatus.ACTIVE,
        category_use: categoryUse.TRAINING,
        category_type: categoryType.FOLDER,
        organization_id: req.user.organization_id
      }, raw: true
    });
    if (findCategoryExist.length != category_id.length) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_CATEGORY_NOT_ACTIVE") });
    }

    const findBranch = await Branch.findOne({
      attributes: ['id'],
      where: { id: Number(branch_id), branch_status: branch_status.ACTIVE, organization_id: req.user.organization_id },
      raw: true,
    });
    if (!findBranch) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_BRANCH_NOT_ACTIVE") });
    }

    if (category_id.length > 0) {
      for (let i = 0; category_id.length > i; i++) {
        const findExistHealthSafetyCategory =
          await HealthSafetyCategoryItem.findOne({
            where: { category_id: category_id[i], branch_id: findBranch.id },
            raw: true,
          });

        if (findExistHealthSafetyCategory) {
          await HealthSafetyCategoryItem.setHeaders(req).update(
            {
              status: status.ACTIVE,
              updated_by: req.user.id,
            },
            {
              where: {
                category_id: category_id[i],
                branch_id: findBranch.id,
              },
            },
          );
          const findUpdateUser = await User.findAll({
            attributes: ['id'],
            where: {
              branch_id: findBranch.id,
              user_status: {
                [Op.in]: [user_status.VERIFIED, user_status.COMPLETED],
              },
              organization_id: req.user.organization_id,
            },
            raw: true,
          });
          let userIds;
          if (findUpdateUser.length > 0) {
            userIds = findUpdateUser.map((user: any) => {
              return user.id;
            });
            await User.setHeaders(req).update(
              { user_status: user_status.ONGOING },
              { where: { id: { [Op.in]: userIds } } },
            );
            await UserCheckList.update(
              { status: check_list_status.PENDING },
              {
                where: {
                  checklist_id: 3,
                  to_user_id: { [Op.in]: userIds },
                  from_user_id: { [Op.in]: userIds },
                },
              },
            );
          }
        } else {
          const findUpdateUser = await User.findAll({
            attributes: ['id'],
            where: {
              branch_id: findBranch.id,
              user_status: {
                [Op.in]: [user_status.VERIFIED, user_status.COMPLETED],
              },
              organization_id: req.user.organization_id
            },
            raw: true,
          });
          let userIds;
          if (findUpdateUser.length > 0) {
            userIds = findUpdateUser.map((user: any) => {
              return user.id;
            });
            await User.setHeaders(req).update(
              { user_status: user_status.ONGOING },
              { where: { id: { [Op.in]: userIds } } },
            );
            await UserCheckList.update(
              { status: check_list_status.PENDING },
              {
                where: {
                  checklist_id: 3,
                  to_user_id: { [Op.in]: userIds },
                  from_user_id: { [Op.in]: userIds },
                },
              },
            );
          }
          await HealthSafetyCategoryItem.setHeaders(req).create({
            category_id: category_id[i],
            branch_id: findBranch.id,
            status: status.ACTIVE,
            created_by: req.user.id,
            updated_by: req.user.id,
          } as any);
        }
      }
      await HealthSafetyCategoryItem.setHeaders(req).update(
        {
          status: status.INACTIVE,
          updated_by: req.user.id,
        },
        {
          where: {
            category_id: { [Op.notIn]: category_id },
            branch_id: findBranch.id,
          },
        },
      );
    } else {
      await HealthSafetyCategoryItem.setHeaders(req).update(
        {
          status: status.INACTIVE,
          updated_by: req.user.id,
        },
        {
          where: {
            branch_id: findBranch.id,
          },
        },
      );
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("HEALTH_SAFETY_BRANCH_STATUS_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  all restore track category
 * @param req
 * @param res
 * @returns
 */

const allRestoreTrackCategory = async (req: any, res: any) => {
  try {
    const { branches, departments, category_ids, user_ids }: any = req.body;
    const { error } =
      await documentCategoryValidator.allRestoreTrackCategory.validate(
        req.body,
      );

    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }

    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'branch_id', 'user_first_name', 'user_middle_name', 'user_last_name'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    let categories: any = [];
    const searchObj = {
      departments,
      branches,
      category_status: categoryStatus.ACTIVE,
    };

    let categoriesIds = [];
    if (category_ids) {
      categoriesIds = category_ids.split(",").map((id: string) => Number(id));
    }

    categories = await fetchCategoriesRecursively(
      req.user.id,
      null,
      1,
      null,
      1,
      null,
      null,
      searchObj,
      null,
      [],
      req.user.organization_id,
    );

    const category_id: any = categories.data.map(
      (item: { id: any }) => item.id,
    );

    if (category_id.length > 0) {
      let whereQry = `AND parent_id IS NULL`;
      if (categoriesIds.length > 0) {
        whereQry = `AND id IN(${categoriesIds})`;
      }

      const getChildCategoryQuery = `
        WITH RECURSIVE ChildRoles AS (
          SELECT id, category_name, parent_id, category_type, category_status
          FROM nv_document_category
          WHERE category_status != 'deleted' ${whereQry}
          UNION ALL
          SELECT r.id, r.category_name, r.parent_id, r.category_type, r.category_status
          FROM nv_document_category r
          INNER JOIN ChildRoles cr ON r.parent_id = cr.id
          WHERE r.category_status != 'deleted'
        )
        SELECT GROUP_CONCAT(id SEPARATOR ',') AS category_ids 
        FROM ChildRoles
        WHERE category_type = 'file' AND category_status != 'deleted' `;

      // Execute recursive query to find child roles
      const getChildCategory = await sequelize.query(getChildCategoryQuery, {
        type: QueryTypes.SELECT,
      });

      const total_items =
        getChildCategory && getChildCategory[0]?.category_ids
          ? getChildCategory[0]?.category_ids.split(",")
          : [];

      const categoryIdsAsNumbers = total_items?.map((id: string) => Number(id));
      let whereQryUser: any = "";
      if (categoryIdsAsNumbers.length > 0) {
        whereQryUser += ` AND dc.id IN (${categoryIdsAsNumbers})`;
      }

      const findUserRole: any = await Role.findOne({
        attributes: ['id', 'role_name'],
        where: { id: getUserDetail.web_user_active_role_id }, raw: true
      });

      const getUserId = await getSuperAdminUserId(req.user.organization_id)
      const excludedIds = [req.user.id, ...getUserId]; // Combine req.user.id with fetched IDs
      const whereObj: any = {
        user_status: {
          [Op.not]: [user_status.CANCELLED],
        },
        id: {
          [Op.not]: excludedIds,
        },
        organization_id: req.user.organization_id
      };

      whereObj.branch_id = {
        [Op.in]: [
          sequelize.literal(
            `SELECT dcb.branch_id
            FROM nv_document_category_branch AS dcb
            JOIN nv_document_category AS dc ON dc.id = dcb.category_id
            WHERE dc.category_status != 'deleted' ${whereQryUser}
            GROUP BY dcb.branch_id`,
          ),
        ],
      };

      whereObj.department_id = {
        [Op.in]: [
          sequelize.literal(
            `(SELECT dcd.department_id 
            FROM nv_document_category_deparment AS dcd
            JOIN nv_document_category AS dc ON dc.id = dcd.category_id
            WHERE dc.category_status != 'deleted' ${whereQryUser}
            GROUP BY dcd.department_id)`,
          ),
        ],
      };

      whereObj[Op.and] = [
        sequelize.literal(
          `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1,2))) `,
        ),
      ];

      if (user_ids) {
        const userIds = user_ids.split(",").map((id: string) => Number(id));
        whereObj.id = {
          [Op.in]: userIds,
        };
      }

      const getUserListQuery: any = {
        attributes: ["id", "branch_id"],
        where: whereObj,
        nest: true,
        raw: true,
      };

      if (
        findUserRole &&
        (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
          findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER) &&
        getUserDetail.branch_id
      ) {
        whereObj.branch_id = getUserDetail.branch_id;
        // Construct recursive query to find child roles
        const getChildRolesQuery = `
          WITH RECURSIVE ChildRoles AS (
            SELECT id, role_name, parent_role_id
            FROM nv_roles
            WHERE id = :activeRoleId
            UNION ALL
            SELECT r.id, r.role_name, r.parent_role_id
            FROM nv_roles r
            INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
          )
          SELECT id
          FROM ChildRoles
          WHERE id != :activeRoleId `;

        // Execute recursive query to find child roles
        const getChildRoles = await sequelize.query(getChildRolesQuery, {
          replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
          type: QueryTypes.SELECT,
        });

        // Build WHERE clause for user roles based on child roles
        let whereStr = "";
        getChildRoles.forEach((child_role: any, index: number) => {
          if (index > 0) {
            whereStr += " OR ";
          }
          whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = User.id)) > 0`;
        });

        if (whereStr) {
          whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
        }
      }

      if (
        findUserRole &&
        findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER
      ) {
        const findUserBranch = await UserBranch.findAll({
          where: { user_id: req.user.id }, raw: true
        });
        if (findUserBranch.length > 0) {
          whereObj.branch_id = {
            [Op.in]: findUserBranch.map((branch: any) => branch.branch_id),
          };
          // Construct recursive query to find child roles
          const getChildRolesQuery = `
            WITH RECURSIVE ChildRoles AS (
              SELECT id, role_name, parent_role_id
              FROM nv_roles
              WHERE id = :activeRoleId
              UNION ALL
              SELECT r.id, r.role_name, r.parent_role_id
              FROM nv_roles r
              INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
            )
            SELECT id
            FROM ChildRoles
            WHERE id != :activeRoleId `;

          // Execute recursive query to find child roles
          const getChildRoles = await sequelize.query(getChildRolesQuery, {
            replacements: {
              activeRoleId: getUserDetail.web_user_active_role_id,
            },
            type: QueryTypes.SELECT,
          });

          // Build WHERE clause for user roles based on child roles
          let whereStr = "";
          getChildRoles.forEach((child_role: any, index: number) => {
            if (index > 0) {
              whereStr += " OR ";
            }
            whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = User.id)) > 0`;
          });

          if (whereStr) {
            whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
          }
        }
      }

      const getUserList = await User.findAll(getUserListQuery);
      delete getUserListQuery.attributes;

      await Promise.all(
        getUserList.map(async (user: any) => {
          const user_id = user.id;
          for (const cid of categoryIdsAsNumbers) {
            await DocumentCategoryItemTrack.setHeaders(req).update(
              {
                document_category_item_track_status:
                  document_category_item_track_status.PENDING,
                updated_by: req.user.id,
              },
              {
                where: {
                  category_id: cid,
                  user_id: user_id,
                },
                silent: true,
              },
            );
          }
          if (categoryIdsAsNumbers.length > 0) {
            const getParentCategoryQuery = `
          WITH RECURSIVE ParentRoles AS (
              SELECT id, parent_id
              FROM nv_document_category
              WHERE id IN (${categoryIdsAsNumbers})  
              UNION ALL
              SELECT r.id, r.parent_id
              FROM nv_document_category r
              INNER JOIN ParentRoles pr ON r.id = pr.parent_id
          )
          SELECT GROUP_CONCAT(id SEPARATOR ',') AS root_parent_id
          FROM ParentRoles
          WHERE parent_id IS NULL `;

            // Execute recursive query to find child roles
            const getParentCategory = await sequelize.query(
              getParentCategoryQuery,
              {
                type: QueryTypes.SELECT,
              },
            );

            const total_parent_items =
              getParentCategory && getParentCategory[0]?.root_parent_id
                ? getParentCategory[0]?.root_parent_id.split(",")
                : [];

            const parentIdsAsNumbers = total_parent_items?.map((id: string) =>
              Number(id),
            );

            const findCategoryTrack = await HealthSafetyCategoryItem.findAll({
              where: {
                category_id: { [Op.in]: parentIdsAsNumbers },
                branch_id: user.branch_id,
                status: status.ACTIVE,
              },
            });

            if (findCategoryTrack.length > 0) {
              await UserCheckList.setHeaders(req).update({
                status: check_list_status.PENDING,
                is_last_rejected: true
              }, {
                where: {
                  to_user_id: user_id,
                  from_user_id: user_id,
                  checklist_id: 3
                }
              });
            }
            const employeeName = [];

            if (getUserDetail?.user_first_name) {
              employeeName.push(getUserDetail.user_first_name);
            }
            if (getUserDetail?.user_middle_name) {
              employeeName.push(getUserDetail.user_middle_name);
            }
            if (getUserDetail?.user_last_name) {
              employeeName.push(getUserDetail.user_last_name);
            }
            // Extracting category names
            const findUserDetails = await User.findOne({ attributes: ['id', 'appToken', 'webAppToken'], where: { id: user_id, organization_id: req.user.organization_id }, raw: true })
            await createNotification([findUserDetails], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.USER_CATEGORY_RESTORE.content(employeeName.join(" ")), NOTIFICATIONCONSTANT.USER_CATEGORY_RESTORE.heading, REDIRECTION_TYPE.CATEGORY, null, null)
          }
        }));
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RESTORE_TRACK_CATEGORY_SUCCESSFULLY"),
    });
  } catch (e: any) {
    console.log("Exception", e);
    return res.send({ status: false, message: e.message });
  }
};

/**
 *  user category track history
 * @param req
 * @param res
 * @returns
 */

const getUserCategoryTrackHistory = async (req: any, res: any) => {
  try {
    const { page, size, user_id, category_id, search_category_id }: any =
      req.query;

    let limitQry = ``;
    if (page && size) {
      const { limit, offset } = getPagination(Number(page), Number(size));
      limitQry = `LIMIT ${offset}, ${limit}`;
    }
    let whereQry = ``;

    if (user_id) {
      whereQry += ` AND CAST(JSON_EXTRACT(a.new_data, '$.user_id') AS SIGNED) IN ( ${String(user_id).split(",").map(Number)})`;
    }

    if (category_id || search_category_id) {
      let whereQry = `id IN(SELECT id FROM nv_document_category WHERE parent_id IS NULL) AND category_status != 'deleted'`;
      if (category_id) {
        whereQry = ` id IN(SELECT id FROM nv_document_category WHERE parent_id = ${category_id}) AND category_status != 'deleted'`;
      }

      if (search_category_id) {
        const searchCategory = search_category_id
          .split(",")
          .map((id: string) => Number(id));
        whereQry += `AND id IN(${searchCategory})`;
      }

      const getChildCategoryQuery = `
        WITH RECURSIVE ChildRoles AS (
          SELECT id, category_name, parent_id, category_type, category_status
          FROM nv_document_category
          WHERE ${whereQry} And category_status != 'deleted'
          UNION ALL
          SELECT r.id, r.category_name, r.parent_id, r.category_type, r.category_status
          FROM nv_document_category r
          INNER JOIN ChildRoles cr ON r.parent_id = cr.id
          WHERE r.category_status != 'deleted'
        )
        SELECT GROUP_CONCAT(id SEPARATOR ',') AS category_ids 
        FROM ChildRoles
        WHERE category_type = 'file' AND category_status != 'deleted'`;

      // Execute recursive query to find child roles
      const getChildCategory = await sequelize.query(getChildCategoryQuery, {
        type: QueryTypes.SELECT,
      });

      const total_items =
        getChildCategory && getChildCategory[0]?.category_ids
          ? getChildCategory[0]?.category_ids.split(",")
          : [];

      const categoryIdsAsNumbers = total_items?.map((id: string) => Number(id));
      whereQry += ` AND CAST(JSON_EXTRACT(a.new_data, '$.category_id') AS SIGNED) IN ( ${categoryIdsAsNumbers})`;
    }

    // Raw SQL query focusing only on specific fields
    const rawQuery = `SELECT
          a.id, a.previous_data, a.new_data, a.created_by, a.updatedAt, CAST(JSON_EXTRACT(a.new_data, '$.category_id') AS SIGNED) AS category_id, CAST(JSON_EXTRACT(a.new_data, '$.user_id') AS SIGNED) AS user_id, 
          CONCAT_WS(' ', u.user_first_name, u.user_middle_name, u.user_last_name) AS user_full_name,
          CASE 
            WHEN u.user_avatar IS NULL OR u.user_avatar = '' THEN ''
            WHEN NOT u.user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = u.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', u.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = u.user_avatar))
          END AS user_avatar, 
         u.user_avatar AS user_avatar_id,
         IF(CAST(JSON_EXTRACT(a.new_data, '$.user_id') AS SIGNED) != a.created_by, 'restored', a.activity_action ) AS activity_action
      FROM
          nv_activities a
      JOIN
        nv_users u ON a.created_by = u.id
      WHERE 
      a.activity_table = 'documentCategoryItemTrack' 
      AND (
        a.activity_action = 'created' OR a.activity_action = 'updated' 
        
      ) ${whereQry}
        ORDER BY a.updatedAt DESC
        ${limitQry};`;

    // Execute the raw query
    const getCategoryTrackDetail = await sequelize.query(rawQuery, {
      type: QueryTypes.SELECT,
    });

    const rawCountQuery = `SELECT COUNT(*) AS total
      FROM
          nv_activities a
      JOIN
        nv_users u ON a.created_by = u.id
      WHERE 
      a.activity_table = 'documentCategoryItemTrack' 
      AND (
        a.activity_action = 'created' OR a.activity_action = 'updated' 
        
      ) ${whereQry}
        ORDER BY a.updatedAt DESC;`;

    // Execute the raw query
    const getCountCategoryTrackDetail = await sequelize.query(rawCountQuery, {
      type: QueryTypes.SELECT,
    });

    const getCountCategoryCount: any =
      getCountCategoryTrackDetail && getCountCategoryTrackDetail[0]
        ? getCountCategoryTrackDetail[0].total
        : 0;
    const { total_pages } = getPaginatedItems(
      size,
      page,
      getCountCategoryCount || 0,
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getCategoryTrackDetail,
      count: getCountCategoryCount,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const addMultipleBranchIntoHealthCategory = async (req: any, res: any) => {
  try {
    const { category_id, branch_id = [], remove_branch_id = [] } = req.body;
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id'],
      where: {
        id: req.user.id,
        organization_id: req.user.organization_id,
        user_status: {
          [Op.notIn]: [user_status.DELETED, user_status.CANCELLED],
        },
      },
      raw: true,
    });
    const findUserRole: any = await Role.findOne({
      attributes: ['id', 'role_name'],
      where: {
        id:
          req.headers["platform-type"] == "web"
            ? getUserDetail.web_user_active_role_id
            : getUserDetail.user_active_role_id,
      },
      raw: true,
    });
    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(findUserRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }
    const findCategoryExist = await DocumentCategory.findOne({
      attributes: ['id'],
      where: {
        id: category_id,
        category_status: categoryStatus.ACTIVE,
        category_use: categoryUse.TRAINING,
        category_type: categoryType.FOLDER,
        organization_id: req.user.organization_id
      }, raw: true
    });
    if (!findCategoryExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_CATEGORY_NOT_ACTIVE") });
    }

    const findBranches = await Branch.findAll({
      where: {
        id: { [Op.in]: branch_id.map(Number) },
        branch_status: branch_status.ACTIVE,
        organization_id: req.user.organization_id,
      },
      raw: true,
    });
    if (findBranches.length !== branch_id.length) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_BRANCH_NOT_ACTIVE") });
    }

    if (branch_id.length > 0) {
      for (let i = 0; branch_id.length > i; i++) {
        const findExistHealthSafetyCategory =
          await HealthSafetyCategoryItem.findOne({
            where: { category_id: category_id, branch_id: branch_id[i] },
            raw: true,
          });

        if (findExistHealthSafetyCategory) {
          await HealthSafetyCategoryItem.setHeaders(req).update(
            {
              status: status.ACTIVE,
              updated_by: req.user.id,
            },
            {
              where: {
                category_id: category_id,
                branch_id: branch_id[i],
              },
            },
          );
          const findUpdateUser = await User.findAll({
            attributes: ['id'],
            where: {
              branch_id: branch_id[i],
              user_status: {
                [Op.in]: [user_status.VERIFIED, user_status.COMPLETED],
              },
              organization_id: req.user.organization_id,
            },
            raw: true,
          });
          let userIds;
          if (findUpdateUser.length > 0) {
            userIds = findUpdateUser.map((user: any) => {
              return user.id;
            });
            await User.setHeaders(req).update(
              { user_status: user_status.ONGOING },
              { where: { id: { [Op.in]: userIds } } },
            );
            await UserCheckList.update(
              { status: check_list_status.PENDING },
              {
                where: {
                  checklist_id: 3,
                  to_user_id: { [Op.in]: userIds },
                  from_user_id: { [Op.in]: userIds },
                },
              },
            );
          }
        } else {
          const findUpdateUser = await User.findAll({
            attributes: ['id'],
            where: {
              branch_id: branch_id[i],
              user_status: {
                [Op.in]: [user_status.VERIFIED, user_status.COMPLETED],
              },
              organization_id: req.user.organization_id
            },
            raw: true,
          });
          let userIds;
          if (findUpdateUser.length > 0) {
            userIds = findUpdateUser.map((user: any) => {
              return user.id;
            });
            await User.setHeaders(req).update(
              { user_status: user_status.ONGOING },
              { where: { id: { [Op.in]: userIds } } },
            );
            await UserCheckList.update(
              { status: check_list_status.PENDING },
              {
                where: {
                  checklist_id: 3,
                  to_user_id: { [Op.in]: userIds },
                  from_user_id: { [Op.in]: userIds },
                },
              },
            );
          }
          await HealthSafetyCategoryItem.setHeaders(req).create({
            category_id: category_id,
            branch_id: branch_id[i],
            status: status.ACTIVE,
            created_by: req.user.id,
            updated_by: req.user.id,
          } as any);
        }
      }

      await updateBranchesAndDepartmentsInAllParents(
        findCategoryExist?.id,
        branch_id,
        [],
        req,
      );
    }

    if (remove_branch_id && remove_branch_id.length > 0) {
      await HealthSafetyCategoryItem.setHeaders(req).update(
        {
          status: status.INACTIVE,
          updated_by: req.user.id,
        },
        {
          where: {
            category_id: category_id,
            branch_id: { [Op.in]: remove_branch_id },
          },
        },
      );
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("HEALTH_SAFETY_BRANCH_STATUS_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getBranchDetailsByCategoryId = async (req: any, res: any) => {
  try {
    const { category_id } = req.params;
    const { page, size, search }: any = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));

    const branchObj: any = {
      attributes: [
        "id",
        "branch_name",
        "branch_status",
        "branch_color",
        "text_color",
        [
          sequelize.literal(`(
          SELECT CASE 
            WHEN EXISTS (
              SELECT 1
              FROM nv_health_safety_category_item
              WHERE nv_health_safety_category_item.category_id = ${category_id}
                AND nv_health_safety_category_item.status = 'active'
                AND EXISTS (
                  SELECT 1
                  FROM nv_document_category_branch
                  WHERE nv_health_safety_category_item.branch_id = Branch.id AND 
                  nv_health_safety_category_item.category_id = ${category_id}
                )
            )
            THEN true
            ELSE false
          END
        )`),
          "is_health_and_safety",
        ],
      ],
    };

    const whereObj: any = {
      branch_status: branch_status.ACTIVE,
      organization_id: req.user.organization_id,
    };
    if (search) {
      whereObj.branch_name = {
        [Op.like]: `%${search}%`,
      };
    }

    if (page && size) {
      branchObj.limit = limit;
      branchObj.offset = offset;
    }

    branchObj.where = whereObj;
    const branchDetails = await Branch.findAll(branchObj);
    const branchCount = await Branch.count({
      where: whereObj,
    });
    const { total_pages } = getPaginatedItems(
      Number(size),
      Number(page),
      branchCount || 0,
    );
    return res.status(StatusCodes.OK).json({
      status: true,
      data: branchDetails,
      message: res.__("SUCCESS_FETCHED"),
      count: branchCount,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });
  } catch (e: any) {
    console.log("Exception", e);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: e.message,
    });
  }
};

export default {
  createCategory,
  updateCategory,
  getCategoryList,
  getOwnCategoryList,
  getAllCategoryList,
  getAllOwnCategoryList,
  getCategoryDetailsById,
  updateCategoryItemOrder,
  updateItemOrder,
  moveCategory,
  deleteCategory,
  copyCategory,
  getUserStatisticsList,
  trackCategory,
  restoreTrackCategory,
  getBranchCategoryList,
  addCategoryIntoHealthCategory,
  allRestoreTrackCategory,
  getUserCategoryTrackHistory,
  addMultipleBranchIntoHealthCategory,
  getBranchDetailsByCategoryId,
};
