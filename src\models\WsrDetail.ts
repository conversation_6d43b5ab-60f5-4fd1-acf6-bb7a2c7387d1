"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface wsrDetailAttributes {
  id: number;
  user_id: number;
  branch_id: number
  wsr_start_date: string;
  wsr_end_date: string;
  wsr_detail_status: string;
  wsr_amount_total: string;
  created_by: number;
  updated_by: number;
}

export enum wsr_detail_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}

export class WsrDetail
  extends Model<wsrDetailAttributes, never>
  implements wsrDetailAttributes {
  id!: number;
  user_id!: number;
  branch_id!: number
  wsr_start_date!: string;
  wsr_end_date!: string;
  wsr_detail_status!: string;
  wsr_amount_total!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

WsrDetail.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    branch_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    wsr_start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    wsr_end_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    wsr_detail_status: {
      type: DataTypes.ENUM,
      values: Object.values(wsr_detail_status),
      defaultValue: wsr_detail_status.ACTIVE,
    },
    wsr_amount_total: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_wsr_details",
    modelName: "WsrDetail",
  },
);

// DsrDetail.belongsTo(Branch, { foreignKey: "branch_id", as: "dsr_branch" });
// Branch.hasMany(DsrDetail, { foreignKey: "branch_id", as: "branch" });

// DsrDetail.belongsTo(User, { foreignKey: "user_id", as: "dsr_user" });
// User.hasMany(DsrDetail, { foreignKey: "user_id", as: "user" });


// Define hooks for Card model
WsrDetail.addHook("afterUpdate", async (wsrDetail: any) => {
  await addActivity("WsrDetail", "updated", wsrDetail);
});

WsrDetail.addHook("afterCreate", async (wsrDetail: WsrDetail) => {
  await addActivity("WsrDetail", "created", wsrDetail);
});

WsrDetail.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
