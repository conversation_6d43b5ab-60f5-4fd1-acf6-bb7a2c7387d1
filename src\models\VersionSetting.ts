"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface versionAttributes {
  id: number;
  key: string;
  value: string;
  setting_status: string;
  created_by: number;
  updated_by: number;
}

export enum setting_status {
  ACTIVE = "active",
  DELETE = "delete",
}

export class VersionSetting
  extends Model<versionAttributes, never>
  implements versionAttributes {
  id!: number;
  key!: string;
  value!: string;
  setting_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

VersionSetting.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    key: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    value: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    setting_status: {
      type: DataTypes.ENUM,
      values: Object.values(setting_status),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "version_settings",
    modelName: "VersionSetting",
  },
);


