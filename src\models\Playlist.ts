"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface playlistAttributes {
  id: number;
  playlist_image: string;
  playlist_name: string;
  playlist_description: string;
  playlist_status: string;
  created_by: number;
  updated_by: number;
}

export enum playlist_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class Playlist
  extends Model<playlistAttributes, never>
  implements playlistAttributes {
  id!: number;
  playlist_image!: string;
  playlist_name!: string;
  playlist_description!: string;
  playlist_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Playlist.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    playlist_image: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    playlist_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    playlist_description: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    playlist_status: {
      type: DataTypes.ENUM,
      values: Object.values(playlist_status),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_playlist",
    modelName: "Playlist",
  },
);

Playlist.addHook("afterUpdate", async (playlist: any) => {
  await addActivity("Playlist", "updated", playlist);
});

Playlist.addHook("afterCreate", async (playlist: Playlist) => {
  await addActivity("Playlist", "created", playlist);
});

Playlist.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
