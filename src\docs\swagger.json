{"paths": {"/public/auth/login": {"post": {"tags": ["Authentication"], "description": "", "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "team-train-Connect-Version", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user_email": {"example": "any"}, "user_password": {"example": "any"}, "webAppToken": {"example": "any"}, "appToken": {"example": "any"}, "login_type": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/public/auth/forgot-password": {"post": {"tags": ["Authentication"], "description": "", "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user_email": {"example": "any"}, "forgot_type": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/public/auth/forgot-password-verify": {"post": {"tags": ["Authentication"], "description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user_email": {"example": "any"}, "new_password": {"example": "any"}, "type": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/public/auth/forgot-pin": {"post": {"tags": ["Authentication"], "description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user_email": {"example": "any"}, "user_password": {"example": "any"}, "new_pin": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/public/auth/verify-otp": {"post": {"tags": ["Authentication"], "description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user_email": {"example": "any"}, "user_otp": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/public/auth/resend-otp": {"post": {"tags": ["Authentication"], "description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user_email": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/create": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "consumes": ["multipart/form-data"], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "geo_country", "in": "formData", "type": "string", "example": "any"}, {"name": "geo_state", "in": "formData", "type": "string", "example": "any"}, {"name": "geo_city", "in": "formData", "type": "string", "example": "any"}, {"name": "user_first_name", "in": "formData", "type": "string", "example": "any", "required": true}, {"name": "user_last_name", "in": "formData", "type": "string", "example": "any", "required": true}, {"name": "user_middle_name", "in": "formData", "type": "string", "example": "any"}, {"name": "user_email", "in": "formData", "type": "string", "example": "any", "required": true}, {"name": "address_line1", "in": "formData", "type": "string", "example": "any"}, {"name": "address_line2", "in": "formData", "type": "string", "example": "any"}, {"name": "country", "in": "formData", "type": "string", "example": "any"}, {"name": "pin_code", "in": "formData", "type": "string", "example": "any"}, {"name": "emergency_contact", "in": "formData", "type": "string", "example": "any"}, {"name": "user_phone_number", "in": "formData", "type": "string", "example": "any"}, {"name": "user_designation", "in": "formData", "type": "string", "example": "any"}, {"name": "branch_id", "in": "formData", "type": "string", "example": "any"}, {"name": "department_id", "in": "formData", "type": "string", "example": "any"}, {"name": "user_gender", "in": "formData", "type": "string", "example": "any"}, {"name": "user_gender_other", "in": "formData", "type": "string", "example": "any"}, {"name": "marital_status_other", "in": "formData", "type": "string", "example": "any"}, {"name": "marital_status", "in": "formData", "type": "string", "example": "any"}, {"name": "date_of_birth", "in": "formData", "type": "string", "example": "any"}, {"name": "role_ids", "in": "formData", "type": "array", "items": {"type": "integer"}, "collectionFormat": "multi", "description": "Array of role IDs sent as multiple parameters, e.g., role_ids=1&role_ids=2&role_ids=3.", "required": true}, {"name": "joining_date", "in": "formData", "type": "string", "example": "any", "required": true}, {"name": "assign_branch_ids", "in": "formData", "type": "array", "items": {"type": "integer"}, "collectionFormat": "multi", "description": "Array of role IDs sent as multiple parameters, e.g., assign_branch_ids=1&assign_branch_ids=2&assign_branch_ids=3.", "required": false}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/user/update/{user_id}": {"put": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "consumes": ["multipart/form-data"], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}, {"name": "geo_country", "in": "formData", "type": "string", "example": "any"}, {"name": "geo_state", "in": "formData", "type": "string", "example": "any"}, {"name": "geo_city", "in": "formData", "type": "string", "example": "any"}, {"name": "user_first_name", "in": "formData", "type": "string", "example": "any", "required": true}, {"name": "user_last_name", "in": "formData", "type": "string", "example": "any", "required": true}, {"name": "user_middle_name", "in": "formData", "type": "string", "example": "any"}, {"name": "user_email", "in": "formData", "type": "string", "example": "any", "required": true}, {"name": "address_line1", "in": "formData", "type": "string", "example": "any"}, {"name": "address_line2", "in": "formData", "type": "string", "example": "any"}, {"name": "country", "in": "formData", "type": "string", "example": "any"}, {"name": "pin_code", "in": "formData", "type": "string", "example": "any"}, {"name": "emergency_contact", "in": "formData", "type": "string", "example": "any"}, {"name": "user_phone_number", "in": "formData", "type": "string", "example": "any"}, {"name": "user_designation", "in": "formData", "type": "string", "example": "any"}, {"name": "branch_id", "in": "formData", "type": "string", "example": "any"}, {"name": "department_id", "in": "formData", "type": "string", "example": "any"}, {"name": "user_gender", "in": "formData", "type": "string", "example": "any"}, {"name": "user_gender_other", "in": "formData", "type": "string", "example": "any"}, {"name": "marital_status_other", "in": "formData", "type": "string", "example": "any"}, {"name": "marital_status", "in": "formData", "type": "string", "example": "any"}, {"name": "date_of_birth", "in": "formData", "type": "string", "example": "any"}, {"name": "role_ids", "in": "formData", "type": "array", "items": {"type": "integer"}, "collectionFormat": "multi", "description": "Array of role IDs sent as multiple parameters like role_ids=1&role_ids=2.", "required": true}, {"name": "joining_date", "in": "formData", "type": "string", "example": "any", "required": true}, {"name": "user_avatar", "in": "formData", "type": "file", "description": "User avatar image file"}, {"name": "assign_branch_ids", "in": "formData", "type": "array", "items": {"type": "integer"}, "collectionFormat": "multi", "description": "Array of role IDs sent as multiple parameters, e.g., assign_branch_ids=1&assign_branch_ids=2&assign_branch_ids=3.", "required": false}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/user/reset-password": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"old_password": {"example": "any"}, "new_password": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/set-login-pin": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"is_login_pin": {"example": "any"}, "pin": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/reset-pin": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"old_pin": {"example": "any"}, "new_pin": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/update-profile": {"put": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "consumes": ["multipart/form-data"], "parameters": [{"name": "user_first_name", "in": "formData", "type": "string", "required": true, "description": "User's first name"}, {"name": "user_last_name", "in": "formData", "type": "string", "required": true, "description": "User's last name"}, {"name": "user_middle_name", "in": "formData", "type": "string", "required": true, "description": "User's middle name"}, {"name": "user_email", "in": "formData", "type": "string", "required": true, "description": "User's email"}, {"name": "user_phone_number", "in": "formData", "type": "string", "description": "User's phone number", "pattern": "^[0-9]{10,11}$"}, {"name": "address_line1", "in": "formData", "type": "string", "required": true, "description": "Address line 1"}, {"name": "address_line2", "in": "formData", "type": "string", "required": true, "description": "Address line 2"}, {"name": "pin_code", "in": "formData", "type": "string", "description": "Pin code"}, {"name": "emergency_contact", "in": "formData", "type": "string", "description": "Emergency contact number", "pattern": "^[0-9]{10,11}$"}, {"name": "country", "in": "formData", "type": "string", "required": true, "description": "User's country"}, {"name": "marital_status_other", "in": "formData", "type": "string", "description": "Other marital status"}, {"name": "user_gender_other", "in": "formData", "type": "string", "description": "User's gender (other)"}, {"name": "user_gender", "in": "formData", "type": "string", "description": "User's gender", "enum": ["male", "female", "other"]}, {"name": "marital_status", "in": "formData", "type": "string", "description": "User's marital status"}, {"name": "date_of_birth", "in": "formData", "type": "string", "format": "date", "required": true, "description": "User's date of birth"}, {"name": "user_avatar", "in": "formData", "type": "file", "description": "User's profile avatar"}, {"name": "user_signature", "in": "formData", "type": "string", "description": "User's signature"}, {"name": "geo_country", "in": "formData", "type": "string", "description": "User's country location"}, {"name": "geo_state", "in": "formData", "type": "string", "description": "User's state location"}, {"name": "geo_city", "in": "formData", "type": "string", "description": "User's city location"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/view-profile": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/delete/{user_id}": {"delete": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/role-list": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/switch-role": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"role_id": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/re-enter-pin": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/logout": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"webAppToken": {"example": "any"}, "appToken": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/get-one-user/{user_id}": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/get-activity-log": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/get-activity-by-user/{user_id}": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/get-permission-list": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/delete-account/{user_id}": {"delete": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/login-with-pin": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user_pin": {"example": "any"}, "client_ip": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/update-notification-token": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"webAppToken": {"example": "any"}, "appToken": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/reset-user-password": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/reset-user-profile": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/get-user-update-activity/{user_id}": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/send-invitation": {"post": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user_ids": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/get-invite-user-list": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/get-geo-list": {"get": {"tags": ["User"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "geo_type", "in": "query", "type": "string"}, {"name": "parent_place", "in": "query", "type": "string"}, {"name": "place_code", "in": "query", "type": "string"}, {"name": "place_name", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/branch/add": {"post": {"tags": ["Branch"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"branch_name": {"type": "string", "description": "Name of the branch", "example": "Downtown Branch"}, "branch_remark": {"type": "string", "description": "Remarks about the branch", "example": "Near central park"}, "branchStatus": {"type": "string", "description": "Status of the branch", "example": "active"}, "branch_color": {"type": "string", "description": "Color representing the branch", "example": "blue"}}, "required": ["branch_name", "branchStatus"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/branch/update/{branch_id}": {"put": {"tags": ["Branch"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "branch_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"branch_name": {"example": "any"}, "branch_remark": {"example": "any"}, "branchStatus": {"example": "any"}, "branch_color": {"example": "any"}, "branch_sign": {"example": "any"}, "branch_employer_name": {"example": "any"}, "branch_heading_employer_name": {"example": "any"}, "branch_heading_name": {"example": "any"}, "branch_work_place": {"example": "any"}, "branch_heading_work_place": {"example": "any"}, "registration_number": {"example": "any"}}, "required": ["branch_name", "branchStatus"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/branch/delete/{branch_id}": {"delete": {"tags": ["Branch"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "branch_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/branch/list": {"get": {"tags": ["Branch"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "integer", "description": "Page number for pagination", "example": 1, "required": false}, {"name": "size", "in": "query", "type": "integer", "description": "Number of items per page", "example": 20, "required": false}, {"name": "search", "in": "query", "type": "string", "description": "Search term to filter branches", "example": "Downtown"}, {"name": "branchStatus", "in": "query", "type": "string", "description": "Status of the branch (e.g., active, inactive)", "example": "active", "required": false}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/branch/get-one/{branch_id}": {"get": {"tags": ["Branch"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "branch_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/department/create": {"post": {"tags": ["Department"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"department_name": {"example": "any"}, "department_remark": {"example": "any"}, "departmentStatus": {"example": "any"}}, "required": ["department_name", "departmentStatus"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/department/update/{department_id}": {"put": {"tags": ["Department"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "department_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"department_name": {"example": "any"}, "department_remark": {"example": "any"}, "departmentStatus": {"example": "any"}}, "required": ["department_name", "departmentStatus"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/department/delete/{department_id}": {"delete": {"tags": ["Department"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "department_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/department/get-one/{department_id}": {"get": {"tags": ["Department"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "department_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/department/get-payment-type/{branch_id}": {"get": {"tags": ["Department"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "branch_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/department/list": {"get": {"tags": ["Department"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "integer", "description": "Page number for pagination", "example": 1, "required": false}, {"name": "size", "in": "query", "type": "integer", "description": "Number of items per page", "example": 20, "required": false}, {"name": "search", "in": "query", "type": "string", "description": "Search term to filter deparments by name", "example": "Downtown"}, {"name": "departmentStatus", "in": "query", "type": "string", "description": "Status of the department (e.g., active, inactive)", "example": "active", "required": false}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/create-form": {"post": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"is_uk_citizen": {"example": "any"}, "has_right_to_work_in_uk": {"example": "any"}, "has_student_or_pg_loan": {"example": "any"}, "has_p45_form": {"example": "any"}, "check_date": {"example": "any"}, "medical_disability": {"example": "any"}, "medical_disability_detail": {"example": "any"}, "kin1_name": {"example": "any"}, "kin1_relation": {"example": "any"}, "kin1_address": {"example": "any"}, "kin1_mobile_number": {"example": "any"}, "kin2_name": {"example": "any"}, "kin2_relation": {"example": "any"}, "kin2_address": {"example": "any"}, "kin2_mobile_number": {"example": "any"}, "professional1_name_contact": {"example": "any"}, "professional1_role_description": {"example": "any"}, "professional2_name_contact": {"example": "any"}, "professional2_role_description": {"example": "any"}, "professional1_start_date": {"example": "any"}, "professional1_end_date": {"example": "any"}, "professional2_start_date": {"example": "any"}, "professional2_end_date": {"example": "any"}, "passport_no": {"example": "any"}, "issued_date": {"example": "any"}, "permit_type": {"example": "any"}, "permit_type_other": {"example": "any"}, "validity": {"example": "any"}, "bank_account_name": {"example": "any"}, "bank_account_number": {"example": "any"}, "bank_sort_code": {"example": "any"}, "bank_society_name": {"example": "any"}, "bank_address": {"example": "any"}, "insurance_number": {"example": "any"}, "postgraduate_loan": {"example": "any"}, "statement_apply": {"example": "any"}, "is_current_information": {"example": "any"}, "another_job": {"example": "any"}, "private_pension": {"example": "any"}, "payment_from": {"example": "any"}, "load_guidance": {"example": "any"}, "statementA": {"example": "any"}, "statementB": {"example": "any"}, "statementC": {"example": "any"}, "is_confirm_upload": {"example": "any"}, "is_confirm_sign": {"example": "any"}}}}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/onbording/request-form": {"get": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/update-form": {"put": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"is_uk_citizen": {"example": "any"}, "has_right_to_work_in_uk": {"example": "any"}, "has_student_or_pg_loan": {"example": "any"}, "has_p45_form": {"example": "any"}, "check_date": {"example": "any"}, "user_gender": {"example": "any"}, "user_gender_other": {"example": "any"}, "marital_status": {"example": "any"}, "marital_status_other": {"example": "any"}, "user_first_name": {"example": "any"}, "user_middle_name": {"example": "any"}, "user_last_name": {"example": "any"}, "user_phone_number": {"example": "any"}, "emergency_contact": {"example": "any"}, "user_email": {"example": "any"}, "birth_country": {"example": "any"}, "date_of_birth": {"example": "any"}, "user_joining_date": {"example": "any"}, "pin_code": {"example": "any"}, "address_line1": {"example": "any"}, "user_designation": {"example": "any"}, "address_line2": {"example": "any"}, "medical_disability": {"example": "any"}, "medical_disability_detail": {"example": "any"}, "kin1_name": {"example": "any"}, "kin1_relation": {"example": "any"}, "kin1_address": {"example": "any"}, "kin1_mobile_number": {"example": "any"}, "kin2_name": {"example": "any"}, "kin2_relation": {"example": "any"}, "kin2_address": {"example": "any"}, "kin2_mobile_number": {"example": "any"}, "professional1_name_contact": {"example": "any"}, "professional1_role_description": {"example": "any"}, "professional2_name_contact": {"example": "any"}, "professional2_role_description": {"example": "any"}, "professional1_start_date": {"example": "any"}, "professional1_end_date": {"example": "any"}, "professional2_start_date": {"example": "any"}, "professional2_end_date": {"example": "any"}, "passport_no": {"example": "any"}, "issued_date": {"example": "any"}, "permit_type": {"example": "any"}, "permit_type_other": {"example": "any"}, "validity": {"example": "any"}, "bank_account_name": {"example": "any"}, "bank_account_number": {"example": "any"}, "bank_sort_code": {"example": "any"}, "bank_society_name": {"example": "any"}, "bank_address": {"example": "any"}, "insurance_number": {"example": "any"}, "postgraduate_loan": {"example": "any"}, "statement_apply": {"example": "any"}, "is_current_information": {"example": "any"}, "another_job": {"example": "any"}, "private_pension": {"example": "any"}, "payment_from": {"example": "any"}, "load_guidance": {"example": "any"}, "statementA": {"example": "any"}, "statementB": {"example": "any"}, "statementC": {"example": "any"}, "is_confirm_upload": {"example": "any"}, "is_confirm_sign": {"example": "any"}}}}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/onbording/delete-file": {"delete": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "file_name", "in": "query", "type": "string"}, {"name": "checklist_id", "in": "query", "type": "string"}, {"name": "from_user_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/get-check-list": {"get": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/get-form-detail": {"get": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "checklist_id", "in": "query", "type": "string"}, {"name": "from_user_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/get-health-safety-checklist": {"get": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/checklist-verification": {"post": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"verifiedCheckList": {"example": "any"}, "to_user_id": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/approve-reject-form": {"post": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"approval_status": {"example": "any"}, "to_user_id": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/get-verification-checklist": {"get": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "to_user_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/get-right-to-work-form": {"get": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/onbording/download-right-to-work-zip": {"get": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "produces": ["application/octet-stream"], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}}}, "/private/onbording/get-health-safety-progress": {"get": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "query", "type": "string"}, {"name": "health_safety_category_id", "in": "query", "type": "string"}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/onbording/regenerate-employment-contract/{user_id}": {"get": {"tags": ["Onboarding"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}, {"name": "notify", "in": "query", "type": "string"}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/category/add-category": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform_type", "in": "formData", "type": "string", "required": true, "description": "Platform type (e.g., web, mobile)"}, {"name": "category_name", "in": "formData", "type": "string", "required": true, "description": "Name of the category"}, {"name": "category_description", "in": "formData", "type": "string", "required": false, "description": "Description of the category"}, {"name": "category_status", "in": "formData", "type": "string", "required": true, "description": "Status of the category"}, {"name": "dashboard_view", "in": "formData", "type": "boolean", "required": false, "description": "Dashboard view setting"}, {"name": "category_use", "in": "formData", "type": "string", "required": false, "description": "Additional use of the category"}, {"name": "parent_id", "in": "formData", "type": "integer", "required": false, "description": "Parent category ID"}, {"name": "branch_ids", "in": "formData", "type": "array", "items": {"type": "integer"}, "required": true, "description": "Branch IDs associated with the category"}, {"name": "department_ids", "in": "formData", "type": "array", "items": {"type": "integer"}, "required": true, "description": "Department IDs associated with the category"}, {"name": "is_notify", "in": "formData", "type": "boolean", "required": false, "description": "Should notifications be sent?"}, {"name": "notification_content", "in": "formData", "type": "string", "required": false, "description": "Notification content to send"}, {"name": "has_video_control", "in": "formData", "type": "boolean", "required": false, "description": "Whether video control is enabled"}, {"name": "has_agreement_required", "in": "formData", "type": "boolean", "required": false, "description": "Whether agreement is required"}, {"name": "is_external_link", "in": "formData", "type": "boolean", "required": false, "description": "Whether the category has an external link"}, {"name": "external_links", "in": "formData", "type": "array", "items": {"type": "string"}, "required": false, "description": "External links associated with the category"}, {"name": "category_type", "in": "formData", "type": "string", "required": true, "description": "Type of the category (file or folder)", "enum": ["file", "folder"]}, {"name": "category_image", "in": "formData", "type": "file", "required": false, "description": "Upload a category image"}, {"name": "item_list", "in": "formData", "type": "array", "items": {"type": "file"}, "required": false, "description": "Upload up to 10 files for the category"}], "responses": {"400": {"description": "Bad Request"}, "default": {"description": ""}}}}, "/private/category/update-category/{category_id}": {"put": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"category_name": {"example": "any"}, "category_description": {"example": "any"}, "categoryStatus": {"example": "any"}, "dashboard_view": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/category/get-category-list": {"get": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "CategoryStatus", "in": "query", "type": "string"}, {"name": "view_type", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}, "default": {"description": ""}}}}, "/private/category/get-all-category-list": {"get": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "category_status", "in": "query", "type": "string"}, {"name": "category_use", "in": "query", "type": "string"}, {"name": "parent_id", "in": "query", "type": "string"}, {"name": "branches", "in": "query", "type": "string"}, {"name": "departments", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/private/category/get-own-category-list": {"get": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "category_status", "in": "query", "type": "string"}, {"name": "category_use", "in": "query", "type": "string"}, {"name": "parent_id", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/private/category/get-all-own-category-list": {"get": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "category_use", "in": "query", "type": "string"}, {"name": "parent_id", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/private/category/get-category/{category_id}": {"get": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "category_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/private/category/update-category-item-order/": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"category_id": {"example": "any"}, "order": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}}}}, "/private/category/update-item-order": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"item_list": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}}}}, "/private/category/move-category": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"to_move_id": {"type": "integer", "example": "any"}, "from_move_id": {"type": "integer", "example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}}}, "/private/category/delete-category": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"category_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "description": "Array of category IDs"}}, "required": ["category_ids"]}, "description": "Request body containing an array of category IDs"}], "responses": {"200": {"description": "OK"}}}}, "/private/category/copy-category": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"category_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "description": "Array of category IDs"}, "from_copy_id": {"type": "integer", "example": 1}}, "required": ["category_ids", "from_copy_id"]}, "description": "Request body containing an array of category IDs"}], "responses": {"200": {"description": "OK"}}}}, "/private/category/get-user-statistics-list": {"get": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "category_id", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "search_category_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/category/track-category": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"category_id": {"type": "integer", "example": 1}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}}}, "/private/category/restore-track-category": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"category_id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 1}, "search_category_id": {"type": "integer", "example": 1}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}}}, "/private/category/get-branch-category/{branch_id}": {"get": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "branch_id", "in": "path", "required": true, "type": "string"}, {"name": "search", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/private/category/add-category-branch": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"category_id": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3], "description": "Array of category IDs"}, "branch_id": {"type": "integer", "example": 1}}, "required": ["category_ids", "branch_id"]}, "description": "Request body containing an array of category IDs"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/category/all-restore-track-category": {"post": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"branches": {"type": "string", "example": "1,2,3,4"}, "departments": {"type": "string", "example": "1,2,3,4"}, "category_ids": {"type": "string", "example": "1,2,3,4"}, "user_ids": {"type": "string", "example": "1,2,3,4"}}}, "description": "Request body containing an array of category IDs"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}}}, "/private/category/get-category-track-activity/": {"get": {"tags": ["Document Center"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search_category_id", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "user_id", "in": "query", "type": "string"}, {"name": "category_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/changeRequest/send-change-request/{change_request_id?}": {"post": {"tags": ["Change Request"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "change_request_id?", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"change_request_subject": {"example": "any"}, "old_data": {"example": "any"}, "new_data": {"example": "any"}}}}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/changeRequest/get-change-request-list": {"get": {"tags": ["Change Request"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "change_request_status", "in": "query", "type": "string"}, {"name": "change_request_date", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/changeRequest/get-own-change-request-list": {"get": {"tags": ["Change Request"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/changeRequest/update-change-request/{change_request_id}": {"put": {"tags": ["Change Request"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "change_request_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"change_request_subject": {"example": "any"}, "old_data": {"example": "any"}, "new_data": {"example": "any"}}}}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/changeRequest/get-change-request-by-id/{change_request_id}": {"get": {"tags": ["Change Request"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "change_request_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/changeRequest/approve-reject-change-request/{change_request_id}": {"post": {"tags": ["Change Request"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "change_request_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"change_request_status": {"example": "any"}, "change_request_remark": {"example": "any"}}}}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/changeRequest/get-change-request-history/{change_request_id}": {"get": {"tags": ["Change Request"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "change_request_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/changeRequest/delete-change-request-by-id/{change_request_id}": {"delete": {"tags": ["Change Request"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "change_request_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/create": {"post": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the file.", "example": "Sample File"}, "type": {"type": "string", "enum": ["department", "general"], "description": "Type of the file, either `DEPT` or `GENERAL`.", "example": "general"}, "content": {"type": "string", "description": "Content of the file.", "example": "This is the file content."}, "remark": {"type": "string", "nullable": true, "description": "Optional remark about the file.", "example": "This is a remark."}, "department_id": {"type": "integer", "nullable": true, "description": "Required if `type` is `DEPT`; otherwise, optional.", "example": 101}, "template_id": {"type": "integer", "nullable": true, "description": "Template ID associated with the file.", "example": 1}}, "required": ["name", "type", "content"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/category": {"get": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/templates": {"get": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "startDate", "in": "query", "type": "string"}, {"name": "endDate", "in": "query", "type": "string"}, {"name": "user_id", "in": "query", "type": "string"}, {"name": "category_id", "in": "query", "type": "string"}, {"name": "department_id", "in": "query", "type": "string"}, {"name": "type", "in": "query", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "status", "in": "query", "type": "string"}, {"name": "template_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}, "delete": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"template_ids": {"example": "any"}, "status": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/forms": {"put": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "template_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/copy/{id}": {"put": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/move/{id}": {"put": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"category_id": {"example": "any"}}, "required": ["category_id"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/template/versions": {"get": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "startDate", "in": "query", "type": "string"}, {"name": "endDate", "in": "query", "type": "string"}, {"name": "user_id", "in": "query", "type": "string"}, {"name": "template_id", "in": "query", "type": "string"}, {"name": "versionNumber", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/forms/versions": {"get": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "user_id", "in": "query", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "status", "in": "query", "type": "string"}, {"name": "startDate", "in": "query", "type": "string"}, {"name": "endDate", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/contract-type/{id?}": {"post": {"tags": ["Contract"], "description": "Create or update a contract type. If an ID is provided, it updates the record; otherwise, it creates a new one.", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "type": "string", "required": false, "description": "ID of the contract type (optional)"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the contract type.", "example": "Full-Time Contract"}, "duration_type": {"type": "string", "enum": ["month", "week"], "description": "Duration type of the contract, either `MONTHLY` or `WEEKLY`.", "example": "MONTHLY"}, "wage_type": {"type": "string", "enum": ["fixed", "hours"], "description": "Wage type of the contract, either `FIXED` or `HOURS`.", "example": "FIXED"}, "working_hours": {"type": "number", "description": "Number of working hours in the contract.", "example": 40}, "wage_per_hour": {"type": "number", "description": "Wage per hour for the contract.", "example": 25.5}, "fixed_types": {"type": "string", "nullable": true, "description": "Optional fixed type for the contract.", "example": "Monthly Salary"}, "remark": {"type": "string", "nullable": true, "description": "Optional remarks about the contract.", "example": "This is a remark."}, "status": {"type": "string", "nullable": true, "description": "Status of the contract.", "example": "Active"}}, "required": ["name", "duration_type", "wage_type", "working_hours", "wage_per_hour"]}}}}, "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/contract-types": {"get": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "status", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/contract-type/{id}": {"delete": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/job-role/{id?}": {"post": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "id?", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"name": {"example": "any"}, "status": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/job-roles": {"get": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "status", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/job-role/{id}": {"delete": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/contract/update-user-contract": {"put": {"tags": ["Contract"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"general_template": {"example": "any"}, "department_template": {"example": "any"}, "expire_date": {"example": "any"}, "start_date": {"example": "any"}, "other": {"example": "any"}, "expire_duration": {"example": "any"}, "wages_hours": {"example": "any"}, "leave_policy_id": {"example": "any"}, "user_id": {"example": "any"}, "other_template": {"example": "any"}, "tips_grade": {"example": "any"}, "fixed_types": {"example": "any"}, "probation_length": {"example": "any"}, "working_hours": {"example": "any"}, "duration_type": {"example": "any"}, "wage_type": {"example": "any"}, "contract_remark": {"example": "any"}, "contract_name": {"example": "any"}, "leave_type_id": {"example": "any"}, "leave_days": {"example": "any"}, "leave_remark": {"example": "any"}, "leave_duration_type": {"example": "any"}, "place_of_work": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/add": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"dsr_date": {"type": "string", "format": "date", "example": "2024-12-17"}, "branch_id": {"type": "integer", "example": 18}, "current_datetime": {"type": "string", "format": "date-time", "example": "2024-12-17 14:17:47"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "payment_type_title": {"type": "string", "example": "Cash"}, "payment_type_usage": {"type": "string", "example": "income"}, "has_include_amount": {"type": "boolean", "example": true}, "has_field_currency": {"type": "boolean", "example": true}, "payment_type_category": {"type": "array", "items": {"type": "object", "properties": {"payment_type_category_id": {"type": "integer", "example": 1}, "payment_type_category_title": {"type": "string", "example": "Cash amounts"}, "payment_type_category_status": {"type": "string", "example": "active"}, "payment_type_category_pattern": {"type": "string", "example": "single"}, "payment_type_category_order": {"type": "integer", "example": 4}, "payment_type_category_branch_id": {"type": "integer", "example": 470}, "categoryBranchValue": {"type": "array", "items": {"type": "object", "properties": {"reference_id": {"type": "integer", "example": 501}, "first_field_value": {"type": "string", "example": "visa"}, "dsr_amount": {"type": "string", "example": "500"}}}}, "dsr_amount": {"type": "string", "example": "500"}}}}, "payment_type_remark": {"type": "string", "example": null}}}}, "dsr_amount_total": {"type": "object", "properties": {"NoneVat": {"type": "string", "example": "500"}, "VAT1": {"type": "string", "example": "900.00"}, "VAT2": {"type": "string", "example": "100"}, "AmountVAT1": {"type": "string", "example": "16.67"}, "AmountVAT2": {"type": "string", "example": "1483.33"}, "diff1": {"type": "string", "example": "1000"}, "diff2": {"type": "string", "example": "983.33"}, "TotalIncome": {"type": "integer", "example": 1500}}}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/update/{dsr_detail_id}": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "dsr_detail_id", "in": "path", "required": true, "type": "string"}, {"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"dsr_date": {"type": "string", "format": "date", "example": "2023-12-17"}, "current_datetime": {"type": "string", "format": "date-time", "example": "2024-12-17 14:23:28"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "payment_type_title": {"type": "string", "example": "Cash"}, "payment_type_usage": {"type": "string", "example": "income"}, "has_include_amount": {"type": "boolean", "example": true}, "has_field_currency": {"type": "boolean", "example": true}, "payment_type_remark": {"type": ["string", "null"], "example": null}, "payment_type_category": {"type": "array", "items": {"type": "object", "properties": {"payment_type_category_id": {"type": "integer", "example": 1}, "payment_type_category_title": {"type": "string", "example": "Cash amounts"}, "payment_type_category_status": {"type": "string", "example": "active"}, "payment_type_category_pattern": {"type": "string", "example": "single"}, "payment_type_category_order": {"type": "integer", "example": 4}, "payment_type_category_branch_id": {"type": "integer", "example": 470}, "dsr_amount": {"type": ["string", "number"], "example": "500"}, "categoryBranchValue": {"type": "array", "items": {"type": "object", "properties": {"reference_id": {"type": "integer", "example": 501}, "first_field_value": {"type": "string", "example": "visa"}, "dsr_amount": {"type": "string", "example": "1000"}, "dsr_item_id": {"type": "integer", "example": 833}}}}}}}}}}, "dsr_amount_total": {"type": "object", "properties": {"NoneVat": {"type": "string", "example": "500"}, "VAT1": {"type": "string", "example": "500.00"}, "VAT2": {"type": "string", "example": "1000"}, "AmountVAT1": {"type": "number", "example": 166.67}, "AmountVAT2": {"type": "string", "example": "1833.33"}, "diff1": {"type": "string", "example": "1000"}, "diff2": {"type": "number", "example": 833.33}, "TotalIncome": {"type": "integer", "example": 2000}}}, "old_dsr_amount_total": {"type": "object", "properties": {"NoneVat": {"type": "string", "example": "500"}, "VAT1": {"type": "string", "example": "900.00"}, "VAT2": {"type": "string", "example": "100"}, "AmountVAT1": {"type": "number", "example": 16.67}, "AmountVAT2": {"type": "string", "example": "1483.33"}, "diff1": {"type": "string", "example": "1000"}, "diff2": {"type": "number", "example": 983.33}, "TotalIncome": {"type": "integer", "example": 1500}}}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-dsr-by-id/{dsr_detail_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "dsr_detail_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-dsr-list": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "string"}, {"name": "submitted_date", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/delete/{dsr_detail_id}": {"delete": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "dsr_detail_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-dsr-request-list": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "string"}, {"name": "dsr_id", "in": "query", "type": "string"}, {"name": "submitted_date", "in": "query", "type": "string"}, {"name": "request_status", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-dsr-request-by-id/{dsr_request_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "dsr_request_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/approve-reject-request": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"dsr_request_id": {"type": "integer", "example": 1}, "request_status": {"type": "string", "example": "approved", "enum": ["pending", "approved", "rejected", "deleted"]}, "request_remark": {"example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-dsr-report": {"post": {"tags": ["Logs Book"], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"branch_id": {"type": "string", "example": "1,2,3"}, "dsr_payment_type_category": {"type": "string", "example": "1,2"}, "date_filter": {"type": "string", "example": "current_month"}, "time_period": {"type": "string", "example": "daily"}, "start_date": {"type": "string", "format": "date", "example": "2024-12-02"}, "end_date": {"type": "string", "format": "date", "example": "2024-12-02"}, "report_type": {"type": "string", "example": "general"}, "columns_group": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "content": {"type": "string", "example": "Branch"}, "parent_id": {"type": ["integer", "null"], "example": null}, "type": {"type": "string", "example": "text"}, "key": {"type": "string", "example": "col1"}, "has_field_currency": {"type": "integer", "example": 0}, "children": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 2}, "content": {"type": "string", "example": "Cash amounts"}, "parent_id": {"type": ["integer", "null"], "example": 34}, "type": {"type": "string", "example": "text"}, "key": {"type": "string", "example": "col2"}, "has_field_currency": {"type": "integer", "example": 1}, "chosen": {"type": "boolean", "example": false}, "width": {"type": "integer", "example": 2}, "selected": {"type": "boolean", "example": false}}}}}}}}}}], "description": "", "security": [{"BearerAuth": []}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/check-dsr-exist": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"branch_id": {"type": "integer", "example": 1}, "dsr_date": {"type": "string", "example": "2024-02-21"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-dsr-activity/{dsr_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "dsr_id", "in": "path", "required": true, "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/download-pdf-excel": {"post": {"tags": ["Logs Book"], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"branch_id": {"type": "string", "example": "1,2,3"}, "dsr_payment_type_category": {"type": "string", "example": "1,2"}, "date_filter": {"type": "string", "example": "current_month"}, "time_period": {"type": "string", "example": "daily"}, "start_date": {"type": "string", "format": "date", "example": "2024-12-02"}, "end_date": {"type": "string", "format": "date", "example": "2024-12-02"}, "report_type": {"type": "string", "example": "general"}, "columns_group": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "content": {"type": "string", "example": "Branch"}, "parent_id": {"type": ["integer", "null"], "example": null}, "type": {"type": "string", "example": "text"}, "key": {"type": "string", "example": "col1"}, "has_field_currency": {"type": "integer", "example": 0}, "children": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 2}, "content": {"type": "string", "example": "Cash amounts"}, "parent_id": {"type": ["integer", "null"], "example": 34}, "type": {"type": "string", "example": "text"}, "key": {"type": "string", "example": "col2"}, "has_field_currency": {"type": "integer", "example": 1}, "chosen": {"type": "boolean", "example": false}, "width": {"type": "integer", "example": 2}, "selected": {"type": "boolean", "example": false}}}}}}}, "file_type": {"type": "string", "example": "pdf,excel,csv", "required": true, "enum": ["excel", "pdf", "csv"]}}}}], "description": "", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "OK"}}}}, "/private/dsr/create-payment-type": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"payment_type_title": {"type": "string", "example": "Cash"}, "payment_type_status": {"type": "string", "example": "active"}, "payment_type_usage": {"type": "string", "example": "income"}, "has_weekly_use": {"type": "boolean", "example": true}, "has_include_amount": {"type": "boolean", "example": true}, "has_field_currency": {"type": "boolean", "example": false}}, "required": ["payment_type_title", "payment_type_status", "payment_type_usage", "has_weekly_use", "has_include_amount", "has_field_currency"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/update-payment-type/{payment_type_id}": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"payment_type_title": {"type": "string", "example": "Cash"}, "payment_type_status": {"type": "string", "example": "active"}, "payment_type_usage": {"type": "string", "example": "income"}, "has_weekly_use": {"type": "boolean", "example": true}, "has_include_amount": {"type": "boolean", "example": true}, "has_field_currency": {"type": "boolean", "example": false}}, "required": ["payment_type_title", "payment_type_status", "payment_type_usage", "has_weekly_use", "has_include_amount", "has_field_currency"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-one-payment-type/{payment_type_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-all-payment-type": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_status", "in": "query", "type": "string", "example": "active,inactive,deleted"}, {"name": "search", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/update-payment-type-order": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"payment_type_id": {"type": "integer", "example": 1}, "order": {"type": "integer", "example": 1}}}}], "responses": {"200": {"description": "OK"}}}}, "/private/dsr/create-payment-type-category/{payment_type_id}": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"payment_type_category_title": {"type": "string", "example": "Cash"}, "payment_type_category_status": {"type": "string", "example": "active"}, "payment_type_category_pattern": {"type": "string", "example": "single,multiple"}, "payment_type_category_data_type": {"type": "boolean", "example": true}, "payment_type_category_fields": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string", "example": "Amount"}, "field_type": {"type": "string", "example": "string,integer"}, "field_limit": {"type": "integer", "example": 100}}}}}, "required": ["payment_type_category_title", "payment_type_category_status", "payment_type_category_pattern", "payment_type_category_data_type", "payment_type_category_fields"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/update-payment-type-category/{payment_type_category_id}": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_category_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"payment_type_category_title": {"type": "string", "example": "Cash"}, "payment_type_category_status": {"type": "string", "example": "active"}, "payment_type_category_data_type": {"type": "boolean", "example": true}, "payment_type_category_fields": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string", "example": "Amount"}, "field_type": {"type": "string", "example": "string,integer"}, "field_limit": {"type": "integer", "example": 100}, "field_id": {"type": "integer", "example": 1}}}}}, "required": ["payment_type_category_title", "payment_type_category_status", "payment_type_category_pattern", "payment_type_category_data_type", "payment_type_category_fields"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-one-payment-type-category/{payment_type_category_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_category_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-all-payment-type-category/{payment_type_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_id", "in": "path", "required": true, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "string"}, {"name": "size", "in": "query", "required": true, "type": "string"}, {"name": "payment_type_category_status", "in": "query", "required": true, "type": "string"}, {"name": "search", "in": "query", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/update-payment-type-category-order": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"payment_type_category_id": {"type": "integer", "example": 1}, "order": {"type": "integer", "example": 1}}, "required": ["payment_type_category_id", "order"]}}], "responses": {"200": {"description": "OK"}}}}, "/private/dsr/get-all-payment-type-child": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}, {"name": "search", "in": "query", "type": "integer"}, {"name": "payment_type_status", "in": "query", "type": "string"}, {"name": "only_active", "in": "query", "type": "boolean", "default": false}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/delete-payment-type/{payment_type_id}": {"delete": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/delete-payment-type-category/{payment_category_type_id}": {"delete": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_category_type_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/add-field-value/{branch_id}": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "branch_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"payment_type_category_id": {"type": "number", "example": 1}, "payment_type_category_value": {"type": "array", "minItems": 1, "items": {"type": "object", "properties": {"payment_type_category_field_id": {"type": "number", "example": 10}, "field_value": {"oneOf": [{"type": "string", "example": "Sample value"}, {"type": "number", "example": 1234}]}}, "required": ["payment_type_category_field_id", "field_value"]}}}, "required": ["payment_type_category_id", "payment_type_category_value"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/update-field-value": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"payment_type_category_value": {"type": "array", "items": {"type": "object", "properties": {"category_value_id": {"type": "number", "nullable": true, "example": null}, "field_value": {"oneOf": [{"type": "string", "example": "Some value"}, {"type": "number", "example": 100}]}, "payment_type_category_field_id": {"type": "number", "example": 1}, "payment_type_category_branch_id": {"type": "number", "example": 18}}, "required": ["field_value", "payment_type_category_field_id", "payment_type_category_branch_id"]}, "minItems": 1, "example": [{"category_value_id": null, "field_value": "Some value", "payment_type_category_field_id": 1, "payment_type_category_branch_id": 18}, {"category_value_id": 123, "field_value": 500, "payment_type_category_field_id": 2, "payment_type_category_branch_id": 19}]}}, "required": ["payment_type_category_value"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-field-value-by-id/{payment_type_category_branch_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_category_branch_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-all-payment-type-child-branch/{branch_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "branch_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/make-category-default-active": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"checked_category_ids": {"type": "array", "items": {"type": "number", "example": 1}}, "branch_id": {"type": "number", "example": 1}}, "required": ["checked_category_ids", "branch_id"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/update-category-value-order": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"payment_type_category_branch_id": {"type": "number", "example": 18}, "order": {"type": "number", "example": 3}, "payment_type_category_id": {"type": "number", "example": 1}}, "required": ["payment_type_category_branch_id", "order", "payment_type_category_id"]}}], "responses": {"200": {"description": "OK"}}}}, "/private/dsr/delete-category-branch-value/{payment_type_category_branch_id}": {"delete": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "payment_type_category_branch_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/private/dsr/add-wsr": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"wsr_start_date": {"type": "string", "format": "date", "example": "2024-12-17"}, "wsr_end_date": {"type": "string", "format": "date", "example": "2024-12-17"}, "branch_id": {"type": "integer", "example": 18}, "current_datetime": {"type": "string", "format": "date-time", "example": "2024-12-17 14:17:47"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "payment_type_title": {"type": "string", "example": "Cash"}, "payment_type_usage": {"type": "string", "example": "income"}, "has_include_amount": {"type": "boolean", "example": true}, "has_field_currency": {"type": "boolean", "example": true}, "payment_type_category": {"type": "array", "items": {"type": "object", "properties": {"payment_type_category_id": {"type": "integer", "example": 1}, "payment_type_category_title": {"type": "string", "example": "Cash amounts"}, "payment_type_category_status": {"type": "string", "example": "active"}, "payment_type_category_pattern": {"type": "string", "example": "single"}, "payment_type_category_order": {"type": "integer", "example": 4}, "payment_type_category_branch_id": {"type": "integer", "example": 470}, "categoryBranchValue": {"type": "array", "items": {"type": "object", "properties": {"reference_id": {"type": "integer", "example": 501}, "first_field_value": {"type": "string", "example": "visa"}, "wsr_amount": {"type": "string", "example": "500"}}}}, "wsr_amount": {"type": "string", "example": "500"}}}}, "payment_type_remark": {"type": "string", "example": null}}}}, "wsr_amount_total": {"type": "object", "properties": {"NoneVat": {"type": "string", "example": "500"}, "VAT1": {"type": "string", "example": "900.00"}, "VAT2": {"type": "string", "example": "100"}, "AmountVAT1": {"type": "string", "example": "16.67"}, "AmountVAT2": {"type": "string", "example": "1483.33"}, "diff1": {"type": "string", "example": "1000"}, "diff2": {"type": "string", "example": "983.33"}, "TotalIncome": {"type": "integer", "example": 1500}}}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/update-wsr/{wsr_detail_id}": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "wsr_detail_id", "in": "path", "required": true, "type": "string"}, {"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"wsr_start_date": {"type": "string", "format": "date", "example": "2023-12-17"}, "dsr_end_date": {"type": "string", "format": "date", "example": "2023-12-17"}, "current_datetime": {"type": "string", "format": "date-time", "example": "2024-12-17 14:23:28"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "payment_type_title": {"type": "string", "example": "Cash"}, "payment_type_usage": {"type": "string", "example": "income"}, "has_include_amount": {"type": "boolean", "example": true}, "has_field_currency": {"type": "boolean", "example": true}, "payment_type_remark": {"type": ["string", "null"], "example": null}, "payment_type_category": {"type": "array", "items": {"type": "object", "properties": {"payment_type_category_id": {"type": "integer", "example": 1}, "payment_type_category_title": {"type": "string", "example": "Cash amounts"}, "payment_type_category_status": {"type": "string", "example": "active"}, "payment_type_category_pattern": {"type": "string", "example": "single"}, "payment_type_category_order": {"type": "integer", "example": 4}, "payment_type_category_branch_id": {"type": "integer", "example": 470}, "wsr_amount": {"type": ["string", "number"], "example": "500"}, "categoryBranchValue": {"type": "array", "items": {"type": "object", "properties": {"reference_id": {"type": "integer", "example": 501}, "first_field_value": {"type": "string", "example": "visa"}, "wsr_amount": {"type": "string", "example": "1000"}, "wsr_item_id": {"type": "integer", "example": 833}}}}}}}}}}, "wsr_amount_total": {"type": "object", "properties": {"NoneVat": {"type": "string", "example": "500"}, "VAT1": {"type": "string", "example": "500.00"}, "VAT2": {"type": "string", "example": "1000"}, "AmountVAT1": {"type": "number", "example": 166.67}, "AmountVAT2": {"type": "string", "example": "1833.33"}, "diff1": {"type": "string", "example": "1000"}, "diff2": {"type": "number", "example": 833.33}, "TotalIncome": {"type": "integer", "example": 2000}}}, "old_wsr_amount_total": {"type": "object", "properties": {"NoneVat": {"type": "string", "example": "500"}, "VAT1": {"type": "string", "example": "900.00"}, "VAT2": {"type": "string", "example": "100"}, "AmountVAT1": {"type": "number", "example": 16.67}, "AmountVAT2": {"type": "string", "example": "1483.33"}, "diff1": {"type": "string", "example": "1000"}, "diff2": {"type": "number", "example": 983.33}, "TotalIncome": {"type": "integer", "example": 1500}}}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-wsr-by-id/{wsr_detail_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "wsr_detail_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-wsr-list": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": 4}, {"name": "wsr_start_date", "in": "query", "type": "2024-05-21"}, {"name": "wsr_end_date", "in": "query", "type": "2024-05-21"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/delete-wsr/{wsr_detail_id}": {"delete": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "wsr_detail_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-wsr-request-list": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "string"}, {"name": "wsr_id", "in": "query", "type": "string"}, {"name": "wsr_start_date", "in": "query", "type": "string"}, {"name": "wsr_end_date", "in": "query", "type": "string"}, {"name": "request_status", "in": "query", "type": "pending, approved, rejected, deleted"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-wsr-request-by-id/{wsr_request_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "wsr_request_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/approve-reject-wsr-request": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"wsr_request_id": {"type": "integer", "example": 1}, "request_status": {"type": "string", "example": "approved", "enum": ["pending", "approved", "rejected", "deleted"]}, "request_remark": {"type": "string", "example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/check-wsr-exist": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"branch_id": {"type": "integer", "example": 1}, "wsr_start_date": {"type": "string", "example": "2024-02-21"}, "wsr_end_date": {"type": "string", "example": "2024-02-21"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dsr/get-wsr-activity/{wsr_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "wsr_id", "in": "path", "required": true, "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/add": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"expense_month": {"type": "string", "example": "12", "required": true}, "expense_year": {"type": "string", "example": "2024", "required": true}, "branch_id": {"type": "integer", "example": 18, "required": true}, "current_datetime": {"type": "string", "format": "date-time", "example": "2024-12-17 14:17:47"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "payment_type_title": {"type": "string", "example": "Cash"}, "payment_type_usage": {"type": "string", "example": "income"}, "has_include_amount": {"type": "boolean", "example": true}, "has_field_currency": {"type": "boolean", "example": true}, "payment_type_category": {"type": "array", "items": {"type": "object", "properties": {"payment_type_category_id": {"type": "integer", "example": 1}, "payment_type_category_title": {"type": "string", "example": "Cash amounts"}, "payment_type_category_status": {"type": "string", "example": "active"}, "payment_type_category_pattern": {"type": "string", "example": "single"}, "payment_type_category_order": {"type": "integer", "example": 4}, "payment_type_category_branch_id": {"type": "integer", "example": 470}, "categoryBranchValue": {"type": "array", "items": {"type": "object", "properties": {"reference_id": {"type": "integer", "example": 501}, "first_field_value": {"type": "string", "example": "visa"}, "expense_amount": {"type": "string", "example": "500"}}}}, "expense_amount": {"type": "string", "example": "500"}}}}, "payment_type_remark": {"type": "string", "example": null}}}}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/update/{expense_detail_id}": {"put": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "expense_detail_id", "in": "path", "required": true, "type": "string"}, {"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"expense_month": {"type": "string", "example": "12", "required": true}, "expense_year": {"type": "string", "example": "2024", "required": true}, "current_datetime": {"type": "string", "format": "date-time", "example": "2024-12-17 14:17:47"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "payment_type_title": {"type": "string", "example": "Cash"}, "payment_type_usage": {"type": "string", "example": "income"}, "has_include_amount": {"type": "boolean", "example": true}, "has_field_currency": {"type": "boolean", "example": true}, "payment_type_category": {"type": "array", "items": {"type": "object", "properties": {"payment_type_category_id": {"type": "integer", "example": 1}, "payment_type_category_title": {"type": "string", "example": "Cash amounts"}, "payment_type_category_status": {"type": "string", "example": "active"}, "payment_type_category_pattern": {"type": "string", "example": "single"}, "payment_type_category_order": {"type": "integer", "example": 4}, "payment_type_category_branch_id": {"type": "integer", "example": 470}, "categoryBranchValue": {"type": "array", "items": {"type": "object", "properties": {"reference_id": {"type": "integer", "example": 501}, "first_field_value": {"type": "string", "example": "visa"}, "expense_amount": {"type": "string", "example": "500"}}}}, "expense_amount": {"type": "string", "example": "500"}}}}, "payment_type_remark": {"type": "string", "example": null}}}}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/get-expense-by-id/{expense_detail_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "expense_detail_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/get-expense-list": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}, {"name": "search", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "integer"}, {"name": "expense_year", "in": "query"}, {"name": "expense_month", "in": "query"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/delete/{expense_detail_id}": {"delete": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "expense_detail_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/get-expense-request-list": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}, {"name": "search", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "integer"}, {"name": "expense_year", "in": "query"}, {"name": "expense_month", "in": "query"}, {"name": "expense_id", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/get-expense-request-by-id/{expense_request_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "expense_request_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/get-expense-report": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "start_date", "in": "query", "type": "integer"}, {"name": "end_date", "in": "query", "type": "integer"}, {"name": "expense_payment_type", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "integer"}, {"name": "type", "in": "query"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/check-expense-exist": {"post": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"branch_id": {"type": "integer", "example": 1}, "expense_month": {"type": "string", "example": "1"}, "expense_year": {"type": "string", "example": "2024"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/expense/get-expense-activity/{expense_id}": {"get": {"tags": ["Logs Book"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "expense_id", "in": "path", "required": true, "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/report/filter-list": {"get": {"tags": ["Logs Book Report"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "report_filter_type", "in": "query", "type": "string", "required": true, "example": "day,general,timeperiod"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/report/filter-category-list": {"get": {"tags": ["Logs Book Report"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "include_inactive", "in": "query", "type": "string"}, {"name": "report_filter_type", "in": "query", "type": "string", "required": true, "example": "today,yesterday,last_7_days", "description": "This field data you get in filter list api"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/report/save-filter": {"post": {"tags": ["Logs Book Report"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"filter_name": {"Type": "string", "example": "any"}, "filter_value": {"Type": "string", "example": "[]"}, "group_value": {"Type": "string", "example": "[]"}, "user_filter_type": {"Type": "string", "example": "day, general"}}, "required": ["filter_name", "user_filter_type"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/report/update-filter/{filter_id}": {"put": {"tags": ["Logs Book Report"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "filter_id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"filter_name": {"Type": "string", "example": "any"}, "filter_value": {"Type": "string", "example": "[]"}, "group_value": {"Type": "string", "example": "[]"}, "user_filter_type": {"Type": "string", "example": "day, general"}}, "required": ["filter_name", "user_filter_type"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/report/get-user-filter-by-id/{filter_id}": {"get": {"tags": ["Logs Book Report"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "filter_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/report/get-user-filter-list/{user_id}": {"get": {"tags": ["Logs Book Report"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}, {"name": "user_filter_type", "in": "query", "required": true, "type": "string", "example": "day, general"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/report/remove-user-filter/{filter_id}": {"delete": {"tags": ["Logs Book Report"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "filter_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/report/get-filter-column": {"post": {"tags": ["Logs Book Report"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"filter_id": {"type": "number", "example": 1}}, "required": ["filter_id"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/app-dashboard": {"get": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "size", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/admin-dashboard": {"get": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/get-dashboard-model": {"get": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/get-dashboard-model-tab": {"get": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/create-dashboard": {"post": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"dashboard_name": {"type": "string", "example": "Swagger dashboard"}, "dashboard_filter": {"type": "string", "example": "{\"branch_id\":\"1,2\",\"end_date\":\"2024-12-18\",\"start_date\":\"2024-12-18\",\"date_filter\":\"today\",\"filter_time_period\":\"monthly\"}"}, "model_list": {"type": "array", "items": {"type": "object", "properties": {"model_order": {"type": "integer", "example": 1}, "model_title": {"type": "string", "example": "dsr line chart"}, "model_type": {"type": "string", "example": "line_chart", "description": "This key data you get from model_type list api"}, "xaxis_list": {"type": "string", "example": "time"}, "yaxis_list": {"type": "string", "example": "dsr"}, "xaxis_value": {"type": "string", "example": "monday, tuesday, wednesday"}, "yaxis_value": {"type": "string", "example": "1,2,3"}}}}}, "required": ["dashboard_name", "dashboard_filter", "model_list"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/update-dashboard/{id}": {"put": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"dashboard_name": {"type": "string", "example": "Swagger dashboard"}, "dashboard_filter": {"type": "string", "example": "{\"branch_id\":\"1,2\",\"end_date\":\"2024-12-18\",\"start_date\":\"2024-12-18\",\"date_filter\":\"today\",\"filter_time_period\":\"monthly\"}"}, "model_list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "model_order": {"type": "integer", "example": 1}, "model_title": {"type": "string", "example": "dsr line chart"}, "model_type": {"type": "string", "example": "line_chart", "description": "This key data you get from model_type list api"}, "xaxis_list": {"type": "string", "example": "time"}, "yaxis_list": {"type": "string", "example": "dsr"}, "xaxis_value": {"type": "string", "example": "monday, tuesday, wednesday"}, "yaxis_value": {"type": "string", "example": "1,2,3"}}}}}, "required": ["dashboard_name", "dashboard_filter", "model_list"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/get-dashboard-list": {"get": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/mark-dashboard-default/{id}": {"put": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/delete-dashboard/{id}": {"delete": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/dashboard/get-dashboard-by-id/{id}": {"get": {"tags": ["Dashboard"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "branch_id", "in": "query", "type": "string"}, {"name": "date_filter", "in": "query", "type": "string", "example": "this_month"}, {"name": "filter_time_period", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/notification/send-notification": {"post": {"tags": ["Notification"], "description": "", "security": [{"BearerAuth": []}], "consumes": ["multipart/form-data"], "parameters": [{"name": "platform-type", "in": "header", "type": "string", "required": true}, {"name": "notification_subject", "in": "formData", "type": "string", "required": true, "description": "The subject of the notification", "example": "any"}, {"name": "notification_content", "in": "formData", "type": "string", "required": true, "description": "The content of the notification", "example": "any"}, {"name": "notification_type", "in": "formData", "type": "string", "description": "The type of the notification", "example": "any"}, {"name": "user_ids", "in": "formData", "type": "string", "description": "Comma-separated user IDs", "example": "any"}, {"name": "branch_ids", "in": "formData", "type": "string", "description": "Comma-separated branch IDs", "example": "any"}, {"name": "department_ids", "in": "formData", "type": "string", "description": "Comma-separated department IDs", "example": "any"}, {"name": "role_ids", "in": "formData", "type": "string", "description": "Comma-separated role IDs", "example": "any"}, {"name": "notification_image", "in": "formData", "type": "file", "description": "Upload an image for the notification"}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/notification/get-staff-notification": {"get": {"tags": ["Notification"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "string"}, {"name": "size", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/notification/get-own-notification": {"get": {"tags": ["Notification"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}, {"name": "tab", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/notification/mark-as-read/{id}": {"get": {"tags": ["Notification"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/apply-leave": {"post": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"subject": {"type": "string", "example": "any"}, "request_reason": {"type": "string", "example": "any"}, "leave_days": {"type": "integer", "example": 12}, "leave_type": {"type": "string", "example": "casual,emergency,onbording,delete,resign"}, "start_date": {"type": "string", "format": "date-time", "example": "any"}, "end_date": {"type": "string", "format": "date-time", "example": "any"}, "role_id": {"type": "integer", "example": 1}, "leave_request_type": {"type": "integer", "example": 1, "description": "In this key you have to pass request type id"}, "has_unlimited": {"type": "string", "example": "any"}, "duration_type": {"type": "string", "example": "any"}}}}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/request/approve-reject-request": {"post": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"action": {"type": "string", "example": "any", "required": true}, "remark": {"type": "string", "example": "approved,pending,rejected"}, "request_id": {"type": "integer", "example": 12, "required": true}, "leave_deduction_type": {"type": "string", "example": "paid", "description": "paid/unpaid", "required": false}}}}], "responses": {"400": {"description": "Bad Request"}}}}, "/private/request/get-leave-list": {"get": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "tab", "in": "query", "type": "string", "example": "ongoing,past"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/get-staff-leave-list": {"get": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "size", "in": "query", "type": "integer"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "search", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "integer"}, {"name": "department_id", "in": "query", "type": "integer"}, {"name": "status", "in": "query", "type": "string", "example": "approved,pending,rejected"}, {"name": "role_id", "in": "query", "type": "integer"}, {"name": "start_date", "in": "query", "type": "string", "format": "date-time", "example": "2024-09-06 00:00:00"}, {"name": "end_date", "in": "query", "type": "string", "format": "date-time", "example": "2024-09-06 00:00:00"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/leave-type/{id?}": {"post": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"name": {"type": "string", "example": "any", "required": true}, "remark": {"type": "string", "example": "any"}, "status": {"type": "string", "example": "any"}, "has_annual_leave": {"type": "boolean", "example": "any"}, "leave_deduction_type": {"type": "string", "example": "paid", "description": "paid/unpaid"}, "user_probation_status": {"type": "boolean", "example": "true"}, "leave_type_color": {"type": "string", "example": "39596e"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/leave-types": {"get": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "status", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}, {"name": "id", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/leave-type/{id}": {"delete": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/leave-policy/{id?}": {"post": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "type": "integer", "description": "The ID of the leave policy to be updated", "example": 1}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"name": {"type": "string", "description": "The updated name of the leave policy", "example": "Annual Leave"}, "remark": {"type": "string", "description": "Additional remarks for the leave policy", "example": "Updated remark"}, "leave_types": {"type": "array", "description": "List of updated leave types", "items": {"type": "object", "properties": {"type_id": {"type": "integer", "description": "The ID of the leave type", "example": 1}, "days": {"type": "number", "description": "The number of days allocated for this leave type", "example": 5}, "duration_type": {"type": "string", "description": "The duration type for the leave", "enum": ["Hours", "Days"], "example": "Days"}, "has_unlimited": {"type": "boolean", "description": "Indicates whether the leave type has unlimited allocation", "example": false}}, "required": ["type_id", "duration_type"]}}, "status": {"type": "string", "description": "The updated status of the leave policy", "example": "active, inactive, draft, deleted"}}, "required": ["name", "leave_types"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/leave-policies": {"get": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "status", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}, {"name": "id", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/leave-policy/{id}": {"delete": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/private/request/get-user-leave-policies/{user_id}": {"get": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}, {"name": "duration_type", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/get-timewise-leave-list": {"get": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/cancel-leave-request": {"post": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"remark": {"type": "string", "example": "any", "required": true}, "request_id": {"type": "string", "example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/get-user-leave-count/{user_id}": {"get": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "user_id", "in": "path", "type": "integer", "required": true}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/get-calender-wise-leave": {"get": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "months", "in": "query", "type": "string", "example": "1,2"}, {"name": "year", "in": "query", "type": "string", "example": "2025"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/request/get-leave-by-id/{leave_id}": {"get": {"tags": ["Leave"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "leave_id", "in": "path", "type": "string", "required": true}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/resignation/send-resignation": {"post": {"tags": ["Resignation"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"resignation_subject": {"type": "string", "example": "any", "required": true}, "resignation_reason": {"type": "string", "example": "any", "required": true}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/resignation/get-resignation-list": {"get": {"tags": ["Resignation"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "size", "in": "query", "type": "integer"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "search", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "integer"}, {"name": "department_id", "in": "query", "type": "integer"}, {"name": "status", "in": "query", "type": "string"}, {"name": "role_id", "in": "query", "type": "integer"}, {"name": "applied_date", "in": "query", "type": "string", "format": "date-time"}, {"name": "last_serving_date", "in": "query", "type": "string", "format": "date-time"}, {"name": "updated_date", "in": "query", "type": "string", "format": "date-time"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/resignation/get-Leaving-checklist/{resignation_id}": {"get": {"tags": ["Resignation"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "resignation_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/resignation/get-resignation": {"get": {"tags": ["Resignation"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "resignation_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/resignation/update-resignation-request/{resignation_id}": {"put": {"tags": ["Resignation"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "required": true, "type": "string", "description": "Specifies the platform making the request (e.g., web, mobile, etc.)", "example": "web"}, {"name": "resignation_id", "in": "path", "required": true, "type": "string", "description": "The unique identifier of the resignation request", "example": "12345"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"resignation_status": {"type": "string", "description": "The status of the resignation request", "enum": ["in-discussion", "accepted", "rejected", "cancelled"], "example": "accepted"}, "remarks": {"type": "string", "description": "Remarks for the resignation status. Required if the status is 'in-discussion'.", "nullable": true, "example": "Pending further discussion"}, "last_serving_date": {"type": "string", "format": "date", "description": "Last serving date. Required if the status is 'accepted'.", "nullable": true, "example": "2024-12-31"}}, "required": ["resignation_status"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/resignation/verify-Leaving-checklist/{resignation_id}": {"put": {"tags": ["Resignation"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "resignation_id", "in": "path", "required": true, "type": "string"}, {"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"verified_checklist": {"type": "array", "description": "An array of checklist IDs that have been verified. Must include at least one ID.", "items": {"type": "number", "description": "The ID of a verified checklist item", "example": 1}, "minItems": 1, "example": [1, 2, 3]}}, "required": ["verified_checklist"]}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/setting/branch-setting": {"post": {"tags": ["Setting"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"employer_sign": {"type": "string", "example": "any"}, "employer_name": {"type": "string", "example": "any"}, "branch_id": {"type": "integer", "example": "any"}, "branch_heading_employer_name": {"type": "string", "example": "any"}, "branch_heading_name": {"type": "string", "example": "any"}, "branch_work_place": {"type": "string", "example": "any"}, "branch_heading_work_place": {"type": "string", "example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/setting/get-branch-setting/{branch_id}": {"get": {"tags": ["Setting"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "branch_id", "in": "path", "required": true, "type": "string"}, {"name": "platform-type", "in": "header", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/setting/get-health-safety-category": {"get": {"tags": ["Setting"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "branch_id", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/setting/get-branch-playlist/{branch_id}": {"get": {"tags": ["Setting"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "branch_id", "in": "path", "required": true, "type": "string"}, {"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/setting/add-playlist-category": {"post": {"tags": ["Setting"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"playlist_id": {"type": "array", "description": "An array of playlist IDs that have been verified. Must include at least one ID.", "items": {"type": "number", "description": "The ID of a verified playlist item", "example": 1}, "minItems": 1, "example": [1, 2, 3]}, "health_safety_category_id": {"type": "integer", "example": "any"}, "branch_id": {"type": "integer", "example": "any"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/setting/get-branch-activity/{branch_id}": {"get": {"tags": ["Setting"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "branch_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/userVerification/user-verification": {"post": {"tags": ["User Verification"], "description": "", "security": [{"BearerAuth": []}], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/add": {"post": {"tags": ["Forecast"], "description": "Add forecast data for a given year and branch(es).", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string", "description": "Type of platform from which the request is made."}, {"name": "body", "in": "body", "schema": {"type": "object", "required": ["forecast_year", "branch_id", "forecast_data"], "properties": {"forecast_year": {"type": "string", "description": "The forecast year range in the format YYYY-YYYY.", "example": "2020-2021"}, "branch_id": {"type": "array", "description": "An array of branch IDs for which the forecast is created.", "items": {"type": "integer"}, "example": [1, 2, 3]}, "forecast_data": {"type": "array", "description": "An array of forecast data objects categorized by type.", "items": {"type": "object", "required": ["forecast_category_type", "forecast_category_data"], "properties": {"forecast_category_type": {"type": "string", "description": "The type of forecast category (e.g., income, expense, other).", "example": "income"}, "forecast_category_data": {"type": "array", "description": "An array of data for the specific forecast category.", "items": {"type": "object", "required": ["payment_type_category_id", "payment_type_id", "bugdet_target_amount", "forecast_type"], "properties": {"payment_type_category_id": {"type": "string", "description": "A comma-separated string of payment type category IDs.", "example": "1,2"}, "payment_type_id": {"type": "integer", "description": "The ID of the payment type.", "example": 1}, "bugdet_target_amount": {"type": "number", "description": "The target amount for the budget in the forecast.", "example": 1250}, "forecast_type": {"type": "string", "description": "The type of forecast (e.g., budget, comparison).", "example": "budget"}}}}}}}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/get-single-forecast/{forecast_id}": {"get": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "forecast_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/get-forecast-list": {"get": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "forecast_year", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "integer"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/get-forecast-category": {"get": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "branch_id", "in": "query", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/update-forecast/{forecast_id}": {"put": {"tags": ["Forecast"], "description": "Update forecast data for a given forecast id.", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string", "description": "Type of platform from which the request is made."}, {"name": "forecast_id", "in": "path", "required": true, "type": "integer"}, {"name": "body", "in": "body", "schema": {"type": "object", "required": ["forecast_data"], "properties": {"forecast_data": {"type": "array", "description": "An array of forecast data objects categorized by type.", "items": {"type": "object", "required": ["forecast_category_type", "forecast_category_data"], "properties": {"forecast_category_type": {"type": "string", "description": "The type of forecast category (e.g., income, expense, other).", "example": "income"}, "forecast_category_data": {"type": "array", "description": "An array of data for the specific forecast category.", "items": {"type": "object", "required": ["payment_type_category_id", "payment_type_id", "bugdet_target_amount", "forecast_type"], "properties": {"payment_type_category_id": {"type": "string", "description": "A comma-separated string of payment type category IDs.", "example": "1,2"}, "payment_type_id": {"type": "integer", "description": "The ID of the payment type.", "example": 1}, "bugdet_target_amount": {"type": "number", "description": "The target amount for the budget in the forecast.", "example": 1250}, "forecast_type": {"type": "string", "description": "The type of forecast (e.g., budget, comparison).", "example": "budget"}}}}}}}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/lock-forecast/{forecast_id}": {"put": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "forecast_id", "in": "path", "type": "string"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/get-forecast-budget-details/{forecast_id}": {"get": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "forecast_id", "in": "path", "type": "string"}, {"name": "format_type", "in": "query", "type": "string"}, {"name": "forecast_category_type", "in": "query", "type": "string"}, {"name": "payment_type_id", "in": "path", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/get-forecast-chart-list": {"get": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "search", "in": "query", "type": "string"}, {"name": "forecast_year", "in": "query", "type": "string"}, {"name": "branch_id", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/download-forecast-report": {"get": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "forecast_id", "in": "query", "type": "string"}, {"name": "file_type", "in": "query", "type": "string", "example": "excel,csv"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/add-forecast-bugdet": {"post": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "required": ["forecast_id", "forecast_budget_data", "forecast_budget_status"], "properties": {"forecast_id": {"type": "integer", "example": 1}, "forecast_budget_status": {"type": "string", "description": "An array of branch IDs for which the forecast is created.", "example": "active"}, "forecast_budget_data": {"type": "array", "description": "An array of forecast data objects categorized by type.", "items": {"type": "object", "required": ["payment_type_id", "forecast_category_type", "budget_target_amount"], "properties": {"payment_type_id": {"type": "integer", "description": "The ID of the payment type.", "example": 1}, "forecast_category_type": {"type": "string", "description": "The type of forecast category (e.g., income, other).", "example": "income"}, "january_amount": {"type": "number", "description": "Forecasted amount for January.", "example": 1000}, "february_amount": {"type": "number", "description": "Forecasted amount for February.", "example": 10}, "april_amount": {"type": "number", "description": "Forecasted amount for April.", "example": 0}, "may_amount": {"type": "number", "description": "Forecasted amount for May.", "example": 0}, "june_amount": {"type": "number", "description": "Forecasted amount for June.", "example": 1202}, "july_amount": {"type": "number", "description": "Forecasted amount for July.", "example": 4512}, "august_amount": {"type": "number", "description": "Forecasted amount for August.", "example": 4125}, "september_amount": {"type": "number", "description": "Forecasted amount for September.", "example": 45132}, "october_amount": {"type": "number", "description": "Forecasted amount for October.", "example": 16326}, "november_amount": {"type": "number", "description": "Forecasted amount for November.", "example": 4521}, "december_amount": {"type": "number", "description": "Forecasted amount for December.", "example": 1240}, "bugdet_target_amount": {"type": "number", "description": "The target amount for the budget.", "example": 10}}}}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/get-forecast-history-list": {"get": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "forecast_id", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/forecast/get-forecast-history-details/{forecast_history_id}": {"get": {"tags": ["Forecast"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "forecast_history_id", "in": "query", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/create-holiday-type": {"post": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "required": ["holiday_type_name", "holiday_type_description", "has_holiday_type_default"], "properties": {"holiday_type_name": {"type": "string", "example": "test"}, "holiday_type_description": {"type": "string", "description": "detail of holiday type", "example": "detail of holiday type"}, "has_holiday_type_default": {"type": "boolean", "example": "true"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/update-holiday-type/{holiday_type_id}": {"put": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "holiday_type_id", "in": "path", "required": true, "type": "number"}, {"name": "body", "in": "body", "schema": {"type": "object", "required": ["holiday_type_name", "holiday_type_description", "has_holiday_type_default"], "properties": {"holiday_type_name": {"type": "string", "example": "test"}, "holiday_type_description": {"type": "string", "description": "detail of holiday type", "example": "detail of holiday type"}, "has_holiday_type_default": {"type": "boolean", "example": "true"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/get-holiday-type/{holiday_type_id}": {"get": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "holiday_type_id", "in": "path", "required": true, "type": "number"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/get-holiday-type-list": {"get": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "page", "in": "query", "type": "integer"}, {"name": "size", "in": "query", "type": "integer"}, {"name": "search", "in": "query", "type": "string"}, {"name": "tab", "in": "query", "type": "string", "example": "active,deleted"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/delete-holiday-type": {"delete": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "holiday_type_id", "in": "path", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/add-holiday-policy/{holiday_type_id}": {"post": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "holiday_type_id", "in": "path", "type": "integer"}, {"name": "body", "in": "body", "schema": {"type": "object", "required": ["holiday_policy_name", "holiday_policy_start_date", "holiday_policy_end_date"], "properties": {"holiday_policy_name": {"type": "string", "example": "test"}, "holiday_policy_description": {"type": "string", "description": "detail of holiday policy", "example": "detail of holiday policy"}, "holiday_policy_colour": {"type": "string", "example": "#000000"}, "holiday_policy_start_date": {"type": "string", "format": "date", "example": "2025-01-01"}, "holiday_policy_end_date": {"type": "string", "format": "date", "example": "2025-01-01"}, "has_leave_reprocess": {"type": "boolean", "example": "true"}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/update-holiday-policy/{holiday_policy_id}": {"put": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "holiday_policy_id", "in": "path", "type": "integer"}, {"name": "body", "in": "body", "schema": {"type": "object", "required": ["holiday_policy_name", "holiday_policy_start_date", "holiday_policy_end_date", "holiday_type_id"], "properties": {"holiday_policy_name": {"type": "string", "example": "test"}, "holiday_policy_description": {"type": "string", "description": "detail of holiday policy", "example": "detail of holiday policy"}, "holiday_policy_colour": {"type": "string", "example": "#000000"}, "holiday_policy_start_date": {"type": "string", "format": "date", "example": "2025-01-01"}, "holiday_policy_end_date": {"type": "string", "format": "date", "example": "2025-01-01"}, "has_leave_reprocess": {"type": "boolean", "example": "true"}, "holiday_type_id": {"type": "integer", "example": 1}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/get-holiday-policy/{holiday_policy_id}": {"get": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "holiday_policy_id", "in": "path", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/delete-holiday-policy/{holiday_policy_id}": {"delete": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "holiday_policy_id", "in": "path", "type": "integer"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/holiday/import-holiday-policy/{holiday_type_id}": {"post": {"tags": ["Holiday"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "holiday_type_id", "in": "path", "type": "integer"}, {"name": "holiday_xlsx", "in": "formData", "type": "file", "description": "XLSX file containing holiday policy data"}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}, "/private/user/assign-policy": {"post": {"tags": ["Assign Policy"], "description": "", "security": [{"BearerAuth": []}], "parameters": [{"name": "platform-type", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "required": ["branch_ids", "department_ids", "user_ids", "role_ids", "holiday_policy_id", "leave_policy_id", "assign_list_type"], "properties": {"branch_ids": {"type": "array", "example": [1, 2]}, "department_ids": {"type": "array", "example": [1, 2]}, "user_ids": {"type": "array", "example": [1, 2]}, "role_ids": {"type": "array", "example": [1, 2]}, "holiday_policy_id": {"type": "array", "example": [1, 2]}, "leave_policy_id": {"type": "array", "example": [1, 2]}, "assign_list_type": {"type": "string", "example": "branch", "enum": ["all"]}}}}], "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequestResponse"}, "500": {"$ref": "#/components/responses/InternalServerErrorResponse"}}}}}}