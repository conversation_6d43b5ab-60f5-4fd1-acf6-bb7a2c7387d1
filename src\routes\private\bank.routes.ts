import { Router } from "express";
import bankController from "../../controller/bank.controller";
import bankValidator from "../../validators/bank.validator";
const router: Router = Router();

// add bank 
router.post("/add-bank",bankValidator.addBank() ,bankController.addBank);

// update bank
router.put("/update-bank/:bank_id",bankValidator.updateBank() ,bankController.updateBank);

// get bank
router.get("/get-bank-list" ,bankController.getAlLBank);

// get bank
router.get("/get-one-bank/:bank_id" ,bankController.getBankById);

// delete  bank
router.delete("/delete-bank/:bank_id" ,bankController.deleteBank);

// get bank by branch
router.get("/get-bank-by-branch/:branch_id" ,bankController.getBankByBranch);

// update bank order
router.put("/update-bank-order/:branch_id" ,bankController.updateBankOrder);


export default router;
