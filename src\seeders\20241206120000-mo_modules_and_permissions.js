'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create mo_modules table
    await queryInterface.createTable('mo_modules', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      module: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      module_name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      created_by: {
        type: Sequelize.INTEGER
      },
      updated_by: {
        type: Sequelize.INTEGER
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Create mo_permissions table
    await queryInterface.createTable('mo_permissions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      role_id: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      module_id: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      permission: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      partial: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      platform: {
        type: Sequelize.INTEGER
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive'),
        allowNull: false,
        defaultValue: 'active'
      },
      created_by: {
        type: Sequelize.INTEGER
      },
      updated_by: {
        type: Sequelize.INTEGER
      },
      organization_id: {
        type: Sequelize.STRING
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Insert default modules
    const defaultModules = [
      { module: 'dashboard', module_name: 'Dashboard' },
      { module: 'user', module_name: 'User Management' },
      { module: 'branch', module_name: 'Branch Management' },
      { module: 'department', module_name: 'Department Management' },
      { module: 'notification', module_name: 'Notifications' },
      { module: 'setting', module_name: 'Settings' },
      { module: 'staff', module_name: 'Staff Management' },
      { module: 'leave_center', module_name: 'Leave Center' },
      { module: 'resignation', module_name: 'Resignation' },
      { module: 'category', module_name: 'Category Management' },
      { module: 'media', module_name: 'Media Management' },
      { module: 'playlist', module_name: 'Playlist Management' },
      { module: 'activity_log', module_name: 'Activity Log' },
      { module: 'branch_card', module_name: 'Branch Card' },
      { module: 'branch_bank', module_name: 'Branch Bank' },
      { module: 'dsr', module_name: 'DSR' },
      { module: 'dsr_report', module_name: 'DSR Report' },
      { module: 'change_request', module_name: 'Change Request' },
      { module: 'user_invitation', module_name: 'User Invitation' },
      { module: 'user_verification', module_name: 'User Verification' },
      { module: 'employee_contract', module_name: 'Employee Contract' },
      { module: 'forecast', module_name: 'Forecast' },
      { module: 'forecast_budget', module_name: 'Forecast Budget' },
      { module: 'leave_setting', module_name: 'Leave Setting' },
      { module: 'leave_report', module_name: 'Leave Report' },
      { module: 'side_letter', module_name: 'Side Letter' },
      { module: 'setup', module_name: 'Setup' }
    ];

    await queryInterface.bulkInsert('mo_modules', defaultModules.map(module => ({
      ...module,
      createdAt: new Date(),
      updatedAt: new Date()
    })));
  },

  async down(queryInterface, Sequelize) {
    // Drop tables in reverse order due to foreign key constraints
    await queryInterface.dropTable('mo_permissions');
    await queryInterface.dropTable('mo_modules');
  }
};
