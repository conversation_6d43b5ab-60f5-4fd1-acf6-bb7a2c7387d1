"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { DocumentCategory } from "./DocumentCategory";

interface healthSafetyCategoryItemAttributes {
    category_id: number;
    branch_id: number;
    status: string;
    created_by: number;
    updated_by: number;
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
}


export class HealthSafetyCategoryItem
    extends Model<healthSafetyCategoryItemAttributes, never>
    implements healthSafetyCategoryItemAttributes {
    category_id!: number;
    branch_id!: number;
    status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

HealthSafetyCategoryItem.init(
    {
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        branch_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_health_safety_category_item",
        modelName: "HealthSafetyCategoryItem",
    },
);

HealthSafetyCategoryItem.removeAttribute("id");
DocumentCategory.hasMany(HealthSafetyCategoryItem, {
    foreignKey: "category_id",
    as: "document_category_item",
});
HealthSafetyCategoryItem.belongsTo(DocumentCategory, {
    foreignKey: "category_id",
    as: "health_safety_category_item",
});


// Define hooks for HealthSafety model
DocumentCategory.addHook("afterUpdate", async (documentCategory: any) => {
    await addActivity("HealthSafetyCategoryItem", "updated", documentCategory);
});

DocumentCategory.addHook("afterCreate", async (documentCategory: DocumentCategory) => {
    await addActivity("HealthSafetyCategoryItem", "created", documentCategory);
});

DocumentCategory.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});


