"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { EmpContractCategory } from "./EmployeeContractCategory";

interface contractAttributes {
    id: number;
    name: string;
    content: string;
    category_id: number;
    status: string;
    remark: string;
    created_by: number;
    updated_by: number;
}

export class EmpContractTemplate
    extends Model<contractAttributes, never>
    implements contractAttributes {
    id!: number;
    name!: string;
    content!: string;
    category_id!: number;
    status!: string;
    remark!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    DELETED = "deleted"
}

EmpContractTemplate.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        name: {
            type: DataTypes.STRING
        },
        category_id: {
            type: DataTypes.INTEGER
        },
        content: {
            type: DataTypes.TEXT('long'),
        },
        remark: {
            type: DataTypes.TEXT('long'),
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
            defaultValue: status.ACTIVE,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_contract_template",
        modelName: "EmpContractTemplate",
    },
);

EmpContractTemplate.belongsTo(EmpContractCategory, { foreignKey: "category_id", as: "emp_contract_category" })
EmpContractCategory.hasMany(EmpContractTemplate, { foreignKey: "category_id", as: "emp_contract_templates" })

// Define hooks for HRMC model
EmpContractTemplate.addHook("afterUpdate", async (empContractTemplate: any) => {
    await addActivity("EmpContractTemplate", "updated", empContractTemplate);
});

EmpContractTemplate.addHook("afterCreate", async (empContractTemplate: EmpContractTemplate) => {
    await addActivity("EmpContractTemplate", "created", empContractTemplate);
});

EmpContractTemplate.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});


