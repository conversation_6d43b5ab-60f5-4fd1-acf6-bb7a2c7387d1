"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

export enum holiday_between_leave_period {
    DONT_COUNT = "dont_count",
    COUNT_AS_LEAVE = "count_as_leave",
}

export enum weekend_between_leave_period {
    DONT_COUNT = "dont_count",
    COUNT_AS_LEAVE = "count_as_leave",
}


interface leaveHolidayWeekendPolicyAttributes {
    leave_accural_policy_id: number;
    holiday_between_leave_period: string;
    holiday_between_leave_period_data: string;
    weekend_between_leave_period: string;
    weekend_between_leave_period_data: string;
}

export class LeaveHolidayWeekendPolicy
    extends Model<leaveHolidayWeekendPolicyAttributes, never>
    implements leaveHolidayWeekendPolicyAttributes {
    leave_accural_policy_id!: number;
    holiday_between_leave_period!: string;
    holiday_between_leave_period_data!: string;
    weekend_between_leave_period!: string;
    weekend_between_leave_period_data!: string;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

LeaveHolidayWeekendPolicy.init(
    {
        leave_accural_policy_id: {
            type: DataTypes.INTEGER
        },
        holiday_between_leave_period: {
            type: DataTypes.ENUM,
            values: Object.values(holiday_between_leave_period),
            allowNull: true,
        },
        holiday_between_leave_period_data: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        weekend_between_leave_period: {
            type: DataTypes.ENUM,
            values: Object.values(weekend_between_leave_period),
            allowNull: true,
        },
        weekend_between_leave_period_data: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_leave_holiday_weekend_policy",
        modelName: "LeaveHolidayWeekendPolicy",
    },
);

LeaveHolidayWeekendPolicy.removeAttribute("id");


LeaveHolidayWeekendPolicy.addHook("afterUpdate", async (LeaveHolidayWeekendPolicy: any) => {
    await addActivity("LeaveHolidayWeekendPolicy", "updated", LeaveHolidayWeekendPolicy);
});

LeaveHolidayWeekendPolicy.addHook("afterCreate", async (LeaveHolidayWeekendPolicy: LeaveHolidayWeekendPolicy) => {
    await addActivity("LeaveHolidayWeekendPolicy", "created", LeaveHolidayWeekendPolicy);
});

LeaveHolidayWeekendPolicy.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

