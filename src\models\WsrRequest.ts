"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { WsrDetail } from "./WsrDetail";

interface wsrRequestAttributes {
  id: number;
  wsr_detail_id: number;
  user_id: number;
  wsr_request_status: string;
  wsr_amount_total: string;
  request_remark: string;
  old_wsr_amount_total: string;
  created_by: number;
  updated_by: number;
}

export enum wsr_request_status {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = 'rejected',
  DELETED = 'deleted'
}

export class WsrRequest
  extends Model<wsrRequestAttributes, never>
  implements wsrRequestAttributes {
  id!: number;
  wsr_detail_id!: number;
  user_id!: number;
  wsr_request_status!: string;
  wsr_amount_total!: string;
  request_remark!: string;
  old_wsr_amount_total!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

WsrRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    wsr_detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    wsr_request_status: {
      type: DataTypes.ENUM,
      values: Object.values(wsr_request_status),
      defaultValue: wsr_request_status.PENDING,
    },
    wsr_amount_total: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    request_remark: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    old_wsr_amount_total: {
      type: DataTypes.TEXT('long'),
      allowNull: true
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_wsr_requests",
    modelName: "WsrRequest",
  },
);


// WsrRequest.belongsTo(User, { foreignKey: "user_id", as: "wsr_request_user" });
// User.hasMany(WsrRequest, { foreignKey: "user_id", as: "user_request" });

WsrRequest.belongsTo(WsrDetail, { foreignKey: "wsr_detail_id", as: "wsr_detail" });
WsrDetail.hasMany(WsrRequest, { foreignKey: "wsr_detail_id", as: "user" });

// Define hooks for Card model
WsrRequest.addHook("afterUpdate", async (wsrRequest: any) => {
  await addActivity("WsrRequest", "updated", wsrRequest);
});

WsrRequest.addHook("afterCreate", async (wsrRequest: WsrRequest) => {
  await addActivity("WsrRequest", "created", wsrRequest);
});

WsrRequest.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

