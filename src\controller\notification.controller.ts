import { Request, Response } from "express";
import { Op, QueryTypes } from "sequelize";
import { createNotification, getSuperAdminUserId, permissionForAdmin } from "../helper/common";
import { StatusCodes } from "http-status-codes";
import { User, user_status } from "../models/User";
import { sequelize } from "../models";
import { FILE_UPLOAD_CONSTANT, ROLE_CONSTANT } from "../helper/constant";
import { Role } from "../models/Role";
import { getPaginatedItems, getPagination } from "../helper/utils";
import notificationValidator from "../validators/notification.validator";
import { Branch } from "../models/Branch";
import { Department } from "../models/Department";
import { UserBranch } from "../models/UserBranch";
import { moveFileInBucket } from "../helper/upload.service";

/**
 *  Create new  branch. Only admin/director/hr can create a new branch.
 * @param req
 * @param res
 * @returns
 */

const sendNotification = async (req: any, res: Response) => {
  try {
    const { error } = await notificationValidator.sendNotification.validate(
      req.body,
    );
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    const {
      notification_subject,
      notification_content,
      notification_type = "all",
    }: any = req.body;

    const user_ids =
      req.body.user_ids && req.body.user_ids != "null"
        ? req.body.user_ids.split(",").map(Number)
        : [];
    const branch_ids =
      req.body.branch_ids && req.body.branch_ids != "null"
        ? req.body.branch_ids.split(",").map(Number)
        : [];
    const department_ids =
      req.body.department_ids && req.body.department_ids != "null"
        ? req.body.department_ids.split(",").map(Number)
        : [];
    const role_ids =
      req.body.role_ids && req.body.role_ids != "null"
        ? req.body.role_ids.split(",").map(Number)
        : [];

    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const findUserRole: any = await Role.findOne({
      attributes: ['id', 'role_name'],
      where: { id: req.headers["platform-type"] == "web" ? getUserDetail.web_user_active_role_id : getUserDetail.user_active_role_id }, raw: true
    });

    const notificationSendPermission = await permissionForAdmin(
      req.headers["platform-type"] == "web"
        ? getUserDetail.web_user_active_role_id
        : getUserDetail.user_active_role_id,
    );

    if (!notificationSendPermission) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    if (req.files && req.files.length > 0 && req.files[0].isMovable) {
      await moveFileInBucket(
        req.files[0].bucket,
        req.files[0].path,
        FILE_UPLOAD_CONSTANT.NOTIFICATION_FILES.destinationPath(
          req.user.organization_id,
          req.files[0].filename,
        ),
        req.files[0].item_id,
        true,
      );
    }

    // Fetch user IDs based on branch IDs, department IDs, role IDs, and user IDs
    const findUserObj: any = {
      user_status: {
        [Op.not]: [
          user_status.CANCELLED, user_status.DELETED
        ],
      },
      organization_id: req.user.organization_id
    }

    if (branch_ids.length > 0) {
      const condition = {
        branch_id: {
          [Op.in]: branch_ids,
        },
      };
      if (findUserObj[Op.or] !== undefined) {
        findUserObj[Op.or].push(condition);
      } else {
        findUserObj[Op.or] = [condition];
      }
    }

    if (department_ids.length > 0) {
      const condition = {
        department_id: {
          [Op.in]: department_ids,
        },
      };
      if (findUserObj[Op.or] !== undefined) {
        findUserObj[Op.or].push(condition);
      } else {
        findUserObj[Op.or] = [condition];
      }
    }
    const getUserId = await getSuperAdminUserId(req.user.organization_id)
    if (notification_type == 'all') {
      findUserObj.id = {
        [Op.not]: [req.user.id, ...getUserId],
      }
      findUserObj[Op.and] = [
        sequelize.literal(
          `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1,2))) `,
        ),
      ];

      if (
        (findUserRole &&
          findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER) ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
        (findUserRole.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER &&
          getUserDetail.branch_id)
      ) {
        findUserObj.branch_id = getUserDetail.branch_id;
        // Construct recursive query to find child roles
        const getChildRolesQuery = `
          WITH RECURSIVE ChildRoles AS (
            SELECT id, role_name, parent_role_id
            FROM nv_roles
            WHERE id = :activeRoleId
            UNION ALL
            SELECT r.id, r.role_name, r.parent_role_id
            FROM nv_roles r
            INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
          )
          SELECT id
          FROM ChildRoles
          WHERE id != :activeRoleId `;

        // Execute recursive query to find child roles
        const getChildRoles = await sequelize.query(getChildRolesQuery, {
          replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
          type: QueryTypes.SELECT,
        });

        // Build WHERE clause for user roles based on child roles
        let whereStr = "";
        getChildRoles.forEach((child_role: any, index: number) => {
          if (index > 0) {
            whereStr += " OR ";
          }
          whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = User.id)) > 0`;
        });

        if (whereStr) {
          findUserObj[Op.and].push(sequelize.literal(`(${whereStr})`));
        }
      }
    } else {
      if (user_ids.length > 0) {
        const condition = {
          id: {
            [Op.in]: user_ids,
          },
        };
        if (findUserObj[Op.or] !== undefined) {
          findUserObj[Op.or].push(condition);
        } else {
          findUserObj[Op.or] = [condition];
        }
      }
    }

    if (role_ids.length > 0) {
      const condition = {
        id: {
          [Op.in]: [
            sequelize.literal(
              `select nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id In (${role_ids})`,
            ),
          ],
        },
      };
      if (findUserObj[Op.or] !== undefined) {
        findUserObj[Op.or].push(condition);
      } else {
        findUserObj[Op.or] = [condition];
      }
    }
    const getToUser = await User.findAll({
      attributes: ["id", "webAppToken", "appToken"],
      where: findUserObj,
      raw: true,
      nest: true,
    });

    if (getToUser.length > 0) {
      /** raw query to insert notification_meta */
      const insertQuery = `
      INSERT INTO notification_meta (
        notification_content,
        notification_subject,
        notification_image,
        user_id,
        role_id,
        branch_id,
        department_id,
        notification_type,
        notification_status,
        from_user_id,
        created_by,
        updated_by,
        createdAt,
        updatedAt
      ) VALUES (
        :notification_content,
        :notification_subject,
        :notification_image,
        :user_ids,
        :role_ids,
        :branch_ids,
        :department_ids,
        :notification_type,
        :notification_status,
        :from_user_id,
        :created_by,
        :updated_by,
        :createdAt,
        :updatedAt
      )`;
      const createSendNotification = await sequelize.query(insertQuery, {
        replacements: {
          notification_content,
          notification_subject,
          notification_image: req.files?.length > 0 ? req.files[0].item_id : null,
          user_ids: req.body.user_ids ? req.body.user_ids.split(',').map(Number).join(',') : null,
          role_ids: req.body.role_ids ? req.body.role_ids.split(',').map(Number).join(',') : null,
          branch_ids: req.body.branch_ids ? req.body.branch_ids.split(',').map(Number).join(',') : null,
          department_ids: req.body.department_ids ? req.body.department_ids.split(',').map(Number).join(',') : null,
          notification_type,
          notification_status: 'sent',
          from_user_id: req.user.id,
          created_by: req.user.id,
          updated_by: req.user.id,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        type: QueryTypes.INSERT
      });
      await createNotification(getToUser, req, notification_type, notification_content, notification_subject, 'general_notification', null, null, createSendNotification ? createSendNotification[0] : null)
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_NOTIFICATION_SENT") });
    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("SELECT_RECIPIENT") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Get staff leave list
 * @param req
 * @param res
 * @returns
 */

const getNotificationList = async (req: Request, res: Response) => {
  try {
    const { page, size }: any = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));

    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id'],
      where: {
        id: req.user.id,
        organization_id: req.user.organization_id,
      },
      raw: true,
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__('ERROR_USER_NOT_FOUND') });
    }

    const notificationSendPermission = await permissionForAdmin(
      req.headers['platform-type'] == 'web'
        ? getUserDetail.web_user_active_role_id
        : getUserDetail.user_active_role_id,
    );
    if (!notificationSendPermission) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__('PERMISSION_DENIED') });
    }

    const findUserRole: any = await Role.findOne({
      attributes: ['id', 'role_name'],
      where: {
        id:
          req.headers['platform-type'] == 'web'
            ? getUserDetail.web_user_active_role_id
            : getUserDetail.user_active_role_id,
      },
      raw: true,
    });

    const findUserBranch = await UserBranch.findAll({
      where: { user_id: req.user.id },
      raw: true,
    });

    // --- Build WHERE clauses & replacements for raw SQL ---
    const whereClauses: string[] = [];
    const replacements: any = {
      status: 'sent',
      apiBase: global.config.API_BASE_URL,
      orgId: req.user.organization_id,
      userId: req.user.id,
    };

    // always filter by status
    whereClauses.push(`nm.notification_status = :status`);

    // area‐manager branch filter
    if (
      findUserRole.role_name === ROLE_CONSTANT.AREA_MANAGER &&
      findUserBranch.length > 0
    ) {
      const branchIds = findUserBranch.map((b) => b.branch_id);
      whereClauses.push(`nm.branch_id IN (:branchIds)`);
      replacements.branchIds = branchIds;
    }

    // branch/hotel‐manager can only see their own sent notifications
    if (
      findUserRole.role_name === ROLE_CONSTANT.BRANCH_MANAGER ||
      findUserRole.role_name === ROLE_CONSTANT.HOTEL_MANAGER
    ) {
      whereClauses.push(`nm.from_user_id = :userId`);
    }

    // enforce same organization on from_user join
    whereClauses.push(`fu.organization_id = :orgId`);

    const whereSQL = whereClauses.join(' AND ');

    // 1) COUNT
    const countQuery = `
      SELECT COUNT(*) AS count
      FROM notification_meta nm
      JOIN nv_users fu ON fu.id = nm.from_user_id
      WHERE ${whereSQL}
    `;
    const countResult: any[] = await sequelize.query(countQuery, {
      replacements,
      type: QueryTypes.SELECT,
    });
    const count = parseInt(countResult[0].count, 10);

    // 2) DATA
    const dataQuery = `
      SELECT
        nm.id,
        nm.notification_subject,
        nm.notification_content,
        nm.notification_type,
        nm.user_id,
        nm.branch_id,
        nm.role_id,
        nm.department_id,
        nm.from_user_id,
        (
          SELECT CASE
            WHEN item_location IS NOT NULL AND item_location != ''
            THEN CASE item_external_location
              WHEN 'yes' THEN item_location 
              ELSE CONCAT(:apiBase, item_location)
            END
            ELSE ''
          END
          FROM nv_items WHERE id = nm.notification_image
        ) AS notification_image_url,
        nm.createdAt,
        fu.id AS \`from_user.id\`,
        CONCAT(fu.user_first_name, ' ', fu.user_last_name) AS \`from_user.user_full_name\`,
        fu.employment_number AS \`from_user.employment_number\`
      FROM notification_meta nm
      JOIN nv_users fu ON fu.id = nm.from_user_id
      WHERE ${whereSQL}
      ORDER BY nm.id DESC
      ${page && size ? 'LIMIT :limit OFFSET :offset' : ''}`;
    if (page && size) {
      replacements.limit = limit;
      replacements.offset = offset;
    }
    const getNotificationList: any[] = await sequelize.query(dataQuery, {
      replacements,
      type: QueryTypes.SELECT,
      nest: true,
      raw: true,
    });

    // --- enrich each notification exactly as before ---
    for (const notification of getNotificationList) {
      notification.to_branch_name = [];
      if (notification.branch_id) {
        const branch_ids =
          notification.branch_id != "null"
            ? notification.branch_id.split(",").map(Number)
            : [];
        notification.to_branch_name = await Branch.findAll({ attributes: ['id', 'branch_name'], where: { id: { [Op.in]: branch_ids }, organization_id: req.user.organization_id }, raw: true })
      }
      notification.to_department_name = [];
      if (notification.department_id) {
        const department_ids =
          notification.department_id.department_ids != "null"
            ? notification.department_id.split(",").map(Number)
            : [];
        notification.to_department_name = await Department.findAll({ attributes: ['id', 'department_name'], where: { id: { [Op.in]: department_ids }, organization_id: req.user.organization_id }, raw: true })
      }
      notification.to_role_name = [];
      if (notification.role_id) {
        const role_ids =
          notification.role_id.role_ids != "null"
            ? notification.role_id.split(",").map(Number)
            : [];
        notification.to_role_name = await Role.findAll({
          attributes: ["id", "role_name"],
          where: { id: { [Op.in]: role_ids } },
          raw: true,
        });
      }
      notification.to_user_name = [];
      if (notification.user_id) {
        const user_ids =
          notification.user_id.user_ids != "null"
            ? notification.user_id.split(",").map(Number)
            : [];
        notification.to_user_name = await User.findAll({
          attributes: [
            "id",
            "user_email",
            [
              sequelize.fn(
                "concat",
                sequelize.col("user_first_name"),
                " ",
                sequelize.col("user_last_name"),
              ),
              "user_full_name",
            ],
          ],
          where: { id: { [Op.in]: user_ids } },
        });
      }
    }
    if (getNotificationList.length > 0) {
      const { total_pages } = getPaginatedItems(size, page, count || 0);
      return res.status(StatusCodes.OK).json({
        status: true,
        notificationList: getNotificationList,
        message: res.__("SUCCESS_FETCHED"),
        count: count || 0,
        page: parseInt(page),
        size: parseInt(size),
        total_pages,
      });
    } else {
      return res.status(StatusCodes.OK).json({
        message: res.__("FAIL_DATA_FETCHED"),
        status: true,
        notificationList: [],
        count: 0,
        page: 0,
        size: 0,
        total_pages: 0,
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};


/**
 *  get Own Notification List
 * @param req
 * @param res
 * @returns
 */

const getOwnNotificationList = async (req: Request, res: Response) => {
  try {
    const { page, size, tab }: any = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));
    const userId = req.user.id;
    const deletedStatus = 'deleted';
    const types = ['individual', 'admin', 'push'];

    // Count Query
    const countQuery = `
      SELECT COUNT(*) AS count
      FROM notifications
      WHERE notification_status != :deletedStatus
        AND to_user_id = :userId
        ${tab == 1 ? "AND notification_type NOT IN (:types)" : ""}
        ${tab == 2 ? "AND notification_type IN (:types)" : ""}`;

    const countResult = await sequelize.query(countQuery, {
      replacements: { deletedStatus, userId, types },
      type: QueryTypes.SELECT,
    });
    const count = parseInt(countResult[0].count);

    // Data Query
    let dataQuery = `
      SELECT
        id,
        notification_subject,
        notification_content,
        notification_status,
        notification_type,
        reference_id,
        redirection_object,
        redirection_type,
        CASE
          WHEN redirection_type = 'media' THEN CONCAT(:apiBase, '/media/', (
            SELECT media_name FROM nv_media WHERE nv_media.id = reference_id LIMIT 1
          ))
          ELSE CASE 
            WHEN notification_image IS NOT NULL THEN (
              SELECT CASE
                WHEN item_location IS NOT NULL AND item_location != ''
                THEN CASE item_external_location
                  WHEN 'yes' THEN item_location 
                  ELSE CONCAT(:apiBase, item_location)
                END
                ELSE ''
              END
              FROM nv_items WHERE id = notification_image
            )
            ELSE NULL
          END
        END AS notification_image_url,
        createdAt
      FROM notifications
      WHERE notification_status != :deletedStatus
        AND to_user_id = :userId
        ${tab == 1 ? "AND notification_type NOT IN (:types)" : ""}
        ${tab == 2 ? "AND notification_type IN (:types)" : ""}
      ORDER BY createdAt DESC`;

    // only add pagination if page & size are present
    if (page && size) {
      dataQuery += ` LIMIT :limit OFFSET :offset`
    }

    const getNotificationList = await sequelize.query(dataQuery, {
      replacements: {
        deletedStatus,
        userId,
        types,
        ...(page && size ? { limit, offset } : {}),
        apiBase: global.config.API_BASE_URL,
      },
      type: QueryTypes.SELECT,
    });

    // Parse redirection_object if present
    for (const notification of getNotificationList) {
      if (notification.redirection_object) {
        try {
          notification.redirection_object = JSON.parse(notification.redirection_object);
        } catch (e) {
          notification.redirection_object = null;
        }
      }
    }

    if (getNotificationList.length > 0) {
      const { total_pages } = getPaginatedItems(size, page, count || 0);
      return res.status(StatusCodes.OK).json({
        status: true,
        notificationList: getNotificationList,
        message: res.__("SUCCESS_FETCHED"),
        count: count || 0,
        page: parseInt(page),
        size: parseInt(size),
        total_pages,
      });
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        notificationList: [],
        message: res.__("FAIL_DATA_FETCHED"),
        count: 0,
        page: 0,
        size: 0,
        total_pages: 0,
      });
    }
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Mark As Read
 * @param req
 * @param res
 * @returns
 */

const markAsRead = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateQuery = `UPDATE notifications SET notification_status = '${'read'}' WHERE id = ${id}`;
    const updateNotification = await sequelize.query(updateQuery, { type: QueryTypes.UPDATE })

    if (updateNotification.length > 0) {
      const selectQuery = `SELECT COUNT(*) as notification_count FROM notifications WHERE to_user_id = ${req.user.id} AND notification_status = 'sent'`;
      const findNotificationCount = await sequelize.query(selectQuery, { type: sequelize.QueryTypes.SELECT })

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_NOTIFICATION_READING"),
        data: {
          pending_notifications: findNotificationCount[0]?.notification_count || 0
        }
      });
    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_NOTIFICATION_READING") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getUserAllNotification = async (req: Request, res: Response) => {
  try {
    const { id }: any = req.params;
    const { page, size }: any = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));

    let limitQuery = ``;
    if (page && size) {
      limitQuery = `LIMIT ${limit} OFFSET ${offset}`;
    }

    const selectQuery = `SELECT id, notification_subject, notification_content, redirection_type, notification_type, createdAt , notification_status
      FROM notifications 
      WHERE to_user_id = ${id} 
      ORDER BY createdAt DESC
      ${limitQuery}`;

    const getUserAllNotificationData: any = await sequelize.query(selectQuery, {
      type: QueryTypes.SELECT,
    });

    const selectCountQuery = `SELECT COUNT(id) as total_count
      FROM notifications 
      WHERE to_user_id = ${id} 
      ORDER BY createdAt DESC`;

    const getNotificationCount: any = await sequelize.query(selectCountQuery, {
      type: QueryTypes.SELECT,
    });

    const count = getNotificationCount[0].total_count;
    const { total_pages } = getPaginatedItems(size, page, count);
    return res.status(StatusCodes.OK).json({
      status: true,
      notificationList: getUserAllNotificationData,
      message: res.__("SUCCESS_FETCHED"),
      count,
      page,
      size,
      total_pages,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

export default {
  sendNotification,
  getNotificationList,
  getOwnNotificationList,
  markAsRead,
  getUserAllNotification,
};
