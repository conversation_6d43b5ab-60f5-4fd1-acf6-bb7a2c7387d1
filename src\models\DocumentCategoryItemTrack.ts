"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { DocumentCategory } from "./DocumentCategory";

interface documentCategoryItemTrackAttributes {
    category_id: number;
    user_id: number;
    document_category_item_track_status: string;
    created_by: number;
    updated_by: number;
}

/** playlist media track enum  for status*/
export enum document_category_item_track_status {
    ONGOING = "ongoing",
    PENDING = "pending",
    COMPLETED = "completed",
}


export class DocumentCategoryItemTrack
    extends Model<documentCategoryItemTrackAttributes, never>
    implements documentCategoryItemTrackAttributes {
    category_id!: number;
    user_id!: number;
    document_category_item_track_status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

DocumentCategoryItemTrack.init(
    {
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        document_category_item_track_status: {
            type: DataTypes.ENUM,
            values: Object.values(document_category_item_track_status),
            allowNull: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_document_category_item_track",
        modelName: "DocumentCategoryItemTrack",
    },
);

DocumentCategoryItemTrack.removeAttribute("id");
DocumentCategory.hasMany(DocumentCategoryItemTrack, { foreignKey: "category_id" });
DocumentCategoryItemTrack.belongsTo(DocumentCategory, { foreignKey: "category_id" });

DocumentCategoryItemTrack.addHook("afterUpdate", async (documentCategoryBranch: any) => {
    await addActivity("documentCategoryItemTrack", "updated", documentCategoryBranch);
});

DocumentCategoryItemTrack.addHook(
    "afterCreate",
    async (documentCategoryItemTrack: DocumentCategoryItemTrack) => {
        await addActivity("documentCategoryItemTrack", "created", documentCategoryItemTrack);
    },
);

DocumentCategoryItemTrack.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
})