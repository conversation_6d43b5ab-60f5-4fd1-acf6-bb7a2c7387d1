import { Response } from "express";

import { StatusCodes } from "http-status-codes";
import {
  CheckList,
  checklist_status,
  checklist_type,
} from "../models/CheckList";
import { UserCheckList, check_list_status } from "../models/UserCheckList";
import { RightToWorkCheckList } from "../models/RightToWorkCheckList";
import { StarterForm } from "../models/StarterForm";
import { HrmcForm } from "../models/HrmcForm";
import { HealthSafetyForm, status } from "../models/HealthSafetyForm";
import { HealthSafetyRelation } from "../models/HealthSafetyRelation";
import {
  deleteFiles,
  fetchCategoriesRecursively,
  formatUserAgentData,
  // generateEmployeeContract,
  getAdminStaffs,
  getBranchSettingObj,
  getGeneralSettingObj,
  moveFile,
  roleName,
  findAllChildrenWithType,
  regenerateEmploymentContractFuncation,
  getGeoDetails,
  sendEmailNotification,
  getOrganizationLogo,
  generateS3EmployeeContract,
} from "../helper/common";
import onbordingValidator from "../validators/onbording.validator";
import { User, user_status } from "../models/User";
import { Op, Sequelize } from "sequelize";
import {
  CHECKLISTTYPE,
  EMAIL_ADDRESS,
  FORMCONSTANT,
  ROLE_CONSTANT,
} from "../helper/constant";
import path from "path";
import fs from "fs";
import { HealthSafety } from "../models/HealthSafetyList";
import { Role } from "../models/Role";
import _ from "lodash";
import { sequelize } from "../models";
import { Branch } from "../models/Branch";
import moment from "moment";
import { Activity, activity_action, activity_type } from "../models/Activity";
import { RightToWorkFormData } from "../models/RightToWorkFormData";
import { RightToWorkFormOption } from "../models/RightToWorkFormOption";
import JSZip from "jszip";
import { HealthSafetyCategory } from "../models/HealthSafetyCategory";
import { HealthSafetyPlaylist } from "../models/HealthSafetyPlaylist";
import { Playlist } from "../models/Playlist";
import {
  UserEmploymentContract,
  contract_status,
} from "../models/UserEmployementContract";
import { UserMeta, wageType } from "../models/UserMeta";
import { EmpContractTemplateVersion } from "../models/EmployeeContractTemplateVersion";
import {
  document_category_item_track_status,
  DocumentCategoryItemTrack,
} from "../models/DocumentCategoryItemTrack";
import {
  HealthSafetyCategoryItem,
  status as HealthSafetyCategoryItemStatus,
} from "../models/HealthSafetyCategoryItem";
import {
  category_status,
  category_type,
  category_use,
} from "../models/DocumentCategory";
import {
  document_category_branch_status,
  DocumentCategoryBranch,
} from "../models/DocumentCategoryBranch";
import { EmpContractTemplate } from "../models/EmployeeContractTemplate";
import { ContractNameModel } from "../models/ContractNameModel";
import { Item, item_status } from "../models/Item";
import { moveFileInBucket } from "../helper/upload.service";
import { FILE_UPLOAD_CONSTANT } from "../helper/constant";

/**
 *  Create form
 * @param req
 * @param res
 * @returns
 */

const createForm = async (req: any, res: Response) => {
  try {
    if (
      !fs.existsSync(
        path.resolve(__dirname, "..", "uploads", "onbording_documents"),
      )
    ) {
      fs.mkdirSync(
        path.resolve(__dirname, "..", "uploads", "onbording_documents"),
        { recursive: true },
      );
    }
    const user_id = req.user.id;
    const { checklist_id }: any = req.query;
    const {
      medical_disability = null,
      medical_disability_detail = null,
      kin1_name = null,
      kin1_relation = null,
      kin1_address = null,
      kin1_mobile_number = null,
      kin2_name = null,
      kin2_relation = null,
      kin2_address = null,
      kin2_mobile_number = null,
      professional1_name_contact = null,
      professional1_role_description = null,
      professional2_name_contact = null,
      professional2_role_description = null,
      professional1_start_date = null,
      professional1_end_date = null,
      professional2_start_date = null,
      professional2_end_date = null,
      passport_no = null,
      issued_date = null,
      permit_type = null,
      permit_type_other,
      validity = null,
      bank_account_name = null,
      bank_account_number = null,
      bank_sort_code = null,
      bank_society_name = null,
      bank_address = null,
      insurance_number = null,
      postgraduate_loan = null,
      statement_apply = null,
      is_current_information = null,
      another_job = null,
      private_pension = null,
      payment_from = null,
      load_guidance,
      statementA,
      statementB,
      statementC,
      // healthSafetyList = [],
      is_confirm_upload = false,
      // health_safety_complete = false,
      is_confirm_sign = false,
    } = req.body;
    const is_uk_citizen =
      req.body.is_uk_citizen == "false"
        ? false
        : req.body.is_uk_citizen == "true"
          ? true
          : req.body.is_uk_citizen;
    const has_right_to_work_in_uk =
      req.body.has_right_to_work_in_uk == "false"
        ? false
        : req.body.has_right_to_work_in_uk == "true"
          ? true
          : req.body.has_right_to_work_in_uk;
    const has_student_or_pg_loan =
      req.body.has_student_or_pg_loan == "false"
        ? false
        : req.body.has_student_or_pg_loan == "true"
          ? true
          : req.body.has_student_or_pg_loan;
    const has_p45_form =
      req.body.has_p45_form == "false"
        ? false
        : req.body.has_p45_form == "true"
          ? true
          : req.body.has_p45_form;

    let check_date = req.body.check_date;
    if (check_date == "") {
      check_date = null;
    }
    const { error } = await onbordingValidator.createForm.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    if (check_date == "") {
      check_date = null;
    }
    if (req.user.user_status == user_status.PENDING) {
      await deleteFiles(req.files);
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }
    const isCheckListExist = await CheckList.findOne({
      where: {
        id: checklist_id,
        type: Sequelize.literal(
          `(type & ${CHECKLISTTYPE.USER}) = ${CHECKLISTTYPE.USER}`,
        ),
        checklist_status: checklist_status.ACTIVE,
      },
    });
    if (!isCheckListExist) {
      await deleteFiles(req.files);
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_CHECKLIST_NOT_FOUND"),
      });
    }

    const isFormExist = await UserCheckList.findOne({
      where: {
        checklist_id: isCheckListExist.id,
        from_user_id: user_id,
      },
    });

    let isCreated: string = "false";
    if (isCheckListExist.prefix == "RTWC") {
      const findWorkCheckList: any = await RightToWorkCheckList.findOne({
        where: { checklist_id: isCheckListExist.id, user_id },
      });

      if (
        isFormExist &&
        isFormExist.status == check_list_status.COMPLETED &&
        findWorkCheckList
      ) {
        await deleteFiles(req.files);
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_FORM_ALREADY_EXIST"),
        });
      }

      if (req.files || is_confirm_upload) {
        // Check if the user has the right to work in the UK
        if (
          has_right_to_work_in_uk == false ||
          has_right_to_work_in_uk == "false"
        ) {
          return res.status(StatusCodes.EXPECTATION_FAILED).json({
            status: false,
            message: res.__("CONTACT_TO_ADMIN"),
          });
        }

        // Get item_ids from files or previous records
        const passport_front_id =
          req.files &&
            req.files.passport_front &&
            req.files.passport_front.length > 0
            ? req.files.passport_front[0].item_id
            : findWorkCheckList && findWorkCheckList.passport_front
              ? findWorkCheckList.passport_front
              : null;

        const passport_back_id =
          req.files &&
            req.files.passport_back &&
            req.files.passport_back.length > 0
            ? req.files.passport_back[0].item_id
            : findWorkCheckList && findWorkCheckList.passport_back
              ? findWorkCheckList.passport_back
              : null;

        const cv_id =
          req.files && req.files.cv && req.files.cv.length > 0
            ? req.files.cv[0].item_id
            : findWorkCheckList && findWorkCheckList.cv
              ? findWorkCheckList.cv
              : null;

        const share_code_id =
          req.files && req.files.share_code && req.files.share_code.length > 0
            ? req.files.share_code[0].item_id
            : findWorkCheckList && findWorkCheckList.share_code
              ? findWorkCheckList.share_code
              : null;

        const brp_front_id =
          req.files && req.files.brp_front && req.files.brp_front.length > 0
            ? req.files.brp_front[0].item_id
            : findWorkCheckList && findWorkCheckList.brp_front
              ? findWorkCheckList.brp_front
              : null;

        const brp_back_id =
          req.files && req.files.brp_back && req.files.brp_back.length > 0
            ? req.files.brp_back[0].item_id
            : findWorkCheckList && findWorkCheckList.brp_back
              ? findWorkCheckList.brp_back
              : null;

        const p45_id =
          req.files && req.files.p45 && req.files.p45.length > 0
            ? req.files.p45[0].item_id
            : findWorkCheckList && findWorkCheckList.p45
              ? findWorkCheckList.p45
              : null;

        const ni_letter_id =
          req.files && req.files.ni_letter && req.files.ni_letter.length > 0
            ? req.files.ni_letter[0].item_id
            : findWorkCheckList && findWorkCheckList.ni_letter
              ? findWorkCheckList.ni_letter
              : null;

        const student_letter_id =
          req.files &&
            req.files.student_letter &&
            req.files.student_letter.length > 0
            ? req.files.student_letter[0].item_id
            : findWorkCheckList && findWorkCheckList.student_letter
              ? findWorkCheckList.student_letter
              : null;

        const statements_dl_utility_id =
          req.files &&
            req.files.statements_dl_utility &&
            req.files.statements_dl_utility.length > 0
            ? req.files.statements_dl_utility[0].item_id
            : findWorkCheckList && findWorkCheckList.statements_dl_utility
              ? findWorkCheckList.statements_dl_utility
              : null;

        const photoID_id =
          req.files && req.files.photoID && req.files.photoID.length > 0
            ? req.files.photoID[0].item_id
            : findWorkCheckList?.photoID;

        // Move files to permanent locations if needed
        if (req.files) {
          // For each file type, move to the appropriate folder if isMovable is true
          const moveS3Files = async (field: string) => {
            if (
              req.files &&
              req.files[field] &&
              req.files[field].length > 0 &&
              req.files[field][0].isMovable
            ) {
              const file = req.files[field][0];
              const destinationKey =
                FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                  req.user.organization_id,
                  req.user.id.toString(),
                  file.filename,
                );

              await moveFileInBucket(
                file.bucket,
                file.path,
                destinationKey,
                file.item_id,
              );
            }
          };

          // Process each file type
          await moveS3Files("passport_front");
          await moveS3Files("passport_back");
          await moveS3Files("cv");
          await moveS3Files("share_code");
          await moveS3Files("brp_front");
          await moveS3Files("brp_back");
          await moveS3Files("p45");
          await moveS3Files("ni_letter");
          await moveS3Files("student_letter");
          await moveS3Files("statements_dl_utility");
          await moveS3Files("photoID");
        }

        const rightToWorkObj: any = {
          checklist_id: isCheckListExist.id,
          user_id,
          is_confirm_upload: is_confirm_upload,
          has_right_to_work_in_uk: has_right_to_work_in_uk,
          is_uk_citizen: is_uk_citizen,
          status: status.ACTIVE,
          updated_by: user_id,
        };

        if (is_confirm_upload) {
          if (
            has_right_to_work_in_uk &&
            (is_uk_citizen == "true" || is_uk_citizen == true)
          ) {
            if (!photoID_id || !ni_letter_id || !statements_dl_utility_id) {
              return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("RIGHT_TO_WORK_REQUIRED"),
              });
            }
            rightToWorkObj.photoID = photoID_id;
            rightToWorkObj.ni_letter = ni_letter_id;
            rightToWorkObj.statements_dl_utility = statements_dl_utility_id;
            rightToWorkObj.cv = cv_id;
            rightToWorkObj.p45 = p45_id;
            rightToWorkObj.is_confirm_upload = true;
          } else if (
            has_right_to_work_in_uk &&
            (is_uk_citizen == "false" || is_uk_citizen == false)
          ) {
            rightToWorkObj.share_code = share_code_id;
            rightToWorkObj.passport_front = passport_front_id;
            rightToWorkObj.passport_back = passport_back_id;
            rightToWorkObj.cv = cv_id;
            rightToWorkObj.brp_front = brp_front_id;
            rightToWorkObj.brp_back = brp_back_id;
            rightToWorkObj.p45 = p45_id;
            rightToWorkObj.ni_letter = ni_letter_id;
            rightToWorkObj.student_letter = student_letter_id;
            rightToWorkObj.statements_dl_utility = statements_dl_utility_id;
            rightToWorkObj.is_confirm_upload = true;
          }
        }
        // if (is_confirm_form) {
        //   rightToWorkObj.step1_listA = step1_listA,
        //     rightToWorkObj.step1_listB_group1 = step1_listB_group1,
        //     rightToWorkObj.step1_listB_group2 = step1_listB_group2,
        //     rightToWorkObj.step2_check = step2_check,
        //     rightToWorkObj.step3_copy = step3_copy,
        //     rightToWorkObj.know_type_of_excuse = know_type_of_excuse,
        //     rightToWorkObj.check_date = check_date,
        //     rightToWorkObj.check_type = check_type
        //   rightToWorkObj.is_confirm_form = true
        // }
        if (!isFormExist && findWorkCheckList) {
          const updateRightToWorkFormDetail =
            await RightToWorkCheckList.setHeaders(req).update(rightToWorkObj, {
              where: { id: findWorkCheckList.id },
            });
          if (updateRightToWorkFormDetail.length > 0) {
            isCreated = "true";
          } else {
            await deleteFiles(req.files);
          }
        } else {
          if (
            isFormExist?.status == check_list_status.PENDING &&
            findWorkCheckList
          ) {
            const updateRightToWorkFormDetail =
              await RightToWorkCheckList.setHeaders(req).update(
                rightToWorkObj,
                { where: { id: findWorkCheckList.id } },
              );
            if (updateRightToWorkFormDetail.length > 0) {
              isCreated = "true";
            } else {
              await deleteFiles(req.files);
            }
          } else {
            rightToWorkObj.created_by = user_id;
            const addRightToWorkFormDetail =
              await RightToWorkCheckList.setHeaders(req).create(rightToWorkObj);
            if (addRightToWorkFormDetail) {
              isCreated = "true";
            } else {
              await deleteFiles(req.files);
            }
          }
        }
      }
    }

    if (isCheckListExist.prefix == "NS&HMRCF") {
      const findStarterForm = await StarterForm.findOne({
        where: { checklist_id: isCheckListExist.id, user_id },
      });
      const findHrmcForm = await HrmcForm.findOne({
        where: { checklist_id: isCheckListExist.id, user_id },
      });
      if (
        isFormExist &&
        isFormExist.status == check_list_status.COMPLETED &&
        findStarterForm &&
        findHrmcForm
      ) {
        await deleteFiles(req.files);
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_FORM_ALREADY_EXIST"),
        });
      }
      const newStarterFormObj: any = {
        checklist_id: isCheckListExist.id,
        user_id,
        medical_disability,
        medical_disability_detail,
        kin1_name,
        kin1_relation,
        kin1_address,
        kin1_mobile_number,
        kin2_name,
        kin2_relation,
        kin2_address,
        kin2_mobile_number,
        professional1_name_contact,
        professional1_role_description,
        professional2_name_contact,
        professional2_role_description,
        professional1_start_date,
        professional1_end_date,
        professional2_start_date,
        professional2_end_date,
        passport_no,
        issued_date,
        permit_type,
        permit_type_other,
        validity,
        bank_account_name,
        bank_account_number,
        bank_sort_code,
        bank_society_name,
        bank_address,
        has_student_or_pg_loan,
        has_p45_form,
        status: status.ACTIVE,
        updated_by: user_id,
      };
      let hmrc_p45_form_id;
      if (!has_student_or_pg_loan && has_p45_form) {
        if (req.files) {
          hmrc_p45_form_id =
            req.files &&
              req.files.hmrc_p45_form &&
              req.files.hmrc_p45_form.length > 0
              ? req.files.hmrc_p45_form[0].item_id
              : findStarterForm && findStarterForm.hmrc_p45_form
                ? findStarterForm.hmrc_p45_form
                : null;

          // Move file to permanent location if needed
          if (
            req.files &&
            req.files.hmrc_p45_form &&
            req.files.hmrc_p45_form.length > 0 &&
            req.files.hmrc_p45_form[0].isMovable
          ) {
            const file = req.files.hmrc_p45_form[0];
            const destinationKey =
              FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                req.user.organization_id,
                req.user.id.toString(),
                file.filename,
              );

            await moveFileInBucket(
              file.bucket,
              file.path,
              destinationKey,
              file.item_id,
            );
          }

          newStarterFormObj.hmrc_p45_form = hmrc_p45_form_id;
        }
      }
      const newHrmcFormObj: any = {
        checklist_id: isCheckListExist.id,
        user_id,
        insurance_number: insurance_number,
        postgraduate_loan,
        statement_apply,
        is_current_information,
        another_job,
        private_pension,
        status: status.ACTIVE,
        payment_from,
        load_guidance,
        statementA,
        statementB,
        statementC,
        updated_by: user_id,
      };
      if (!isFormExist && findStarterForm && findHrmcForm) {
        const updateNewStarterFrom = await StarterForm.setHeaders(req).update(
          newStarterFormObj,
          { where: { id: findStarterForm.id } },
        );
        const updateHrmcFrom = await HrmcForm.setHeaders(req).update(
          newHrmcFormObj,
          {
            where: { id: findHrmcForm.id },
          },
        );
        if (updateNewStarterFrom.length > 0 && updateHrmcFrom.length > 0) {
          isCreated = "true";
        } else {
          await deleteFiles(req.files);
        }
      } else {
        if (
          isFormExist &&
          isFormExist.status == check_list_status.PENDING &&
          findStarterForm &&
          findHrmcForm
        ) {
          const updateNewStarterFrom = await StarterForm.setHeaders(req).update(
            newStarterFormObj,
            { where: { id: findStarterForm.id } },
          );
          const updateHrmcFrom = await HrmcForm.setHeaders(req).update(
            newHrmcFormObj,
            {
              where: { id: findHrmcForm.id },
            },
          );
          if (updateNewStarterFrom.length > 0 && updateHrmcFrom.length > 0) {
            isCreated = "true";
          } else {
            await deleteFiles(req.files);
          }
        } else {
          newStarterFormObj.created_by = user_id;
          newHrmcFormObj.created_by = user_id;
          const addNewStarterFrom =
            await StarterForm.setHeaders(req).create(newStarterFormObj);
          const addNewHrmcFrom =
            await HrmcForm.setHeaders(req).create(newHrmcFormObj);
          if (addNewStarterFrom && addNewHrmcFrom) {
            isCreated = "true";
          } else {
            await deleteFiles(req.files);
          }
        }
      }
    }
    if (isCheckListExist.prefix == "HSI") {
      if (isFormExist && isFormExist.status == check_list_status.COMPLETED) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_FORM_ALREADY_EXIST"),
        });
      }
      const findUser = await User.findOne({
        where: { id: req.user.id, organization_id: req.user.organization_id },
        raw: true,
      });
      const whereObj: any = { status: HealthSafetyCategoryItemStatus.ACTIVE };
      if (findUser && findUser.branch_id) {
        whereObj.branch_id = findUser?.branch_id;
      }
      const findBranchWiseCategory = await HealthSafetyCategoryItem.findAll({
        where: whereObj,
        raw: true,
      });
      if (findBranchWiseCategory.length > 0) {
        const category_Ids = findBranchWiseCategory.map((category) => {
          return category.category_id;
        });

        let allChildIds = await findAllChildrenWithType(
          category_Ids,
          true,
          true,
        );
        allChildIds = allChildIds
          .filter((child) => child.category_type === category_type.FILE)
          .map((child) => {
            return child.id;
          });
        const findHealthSafetyBranch = await DocumentCategoryBranch.findAll({
          where: {
            category_id: { [Op.in]: allChildIds },
            branch_id: findUser?.branch_id,
            document_category_branch_status:
              document_category_branch_status.ACTIVE,
          },
        });
        allChildIds = findHealthSafetyBranch.map((category) => {
          return category.category_id;
        });
        const findUserTrack = await DocumentCategoryItemTrack.findAll({
          where: {
            user_id: req.user.id,
            category_id: { [Op.in]: allChildIds },
            document_category_item_track_status:
              document_category_item_track_status.COMPLETED,
          },
        });
        if (
          findUserTrack.length > 0 &&
          allChildIds.length > 0 &&
          findUserTrack.length == allChildIds.length
        ) {
          isCreated = "true";
        } else {
          return res.status(StatusCodes.EXPECTATION_FAILED).json({
            status: false,
            message: res.__("PLEASE_COMPLETE_YOUR_TRAINING_CATEGORY"),
          });
        }
      } else {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("ITEM_NOT_FOUND"),
        });
      }
    }
    let fileName: any = "", itemId = null;
    if (isCheckListExist.prefix == "EC") {
      isCreated = "true";
      const findUserData: any = await User.findOne({
        where: { id: user_id, organization_id: req.user.organization_id },
        include: {
          model: Branch,
          as: "branch",
          attributes: ["branch_name"],
        },
        raw: true,
        nest: true,
      });

      if (!isNaN(findUserData?.user_signature)) {
        const findItem = await Item.findOne({ where: { id: findUserData?.user_signature } })
        if (findItem) {
          findUserData.user_signature = findItem?.item_location
        }
      } else {
        if (findUserData.user_signature) {
          findUserData.user_signature = `/signatures/` + findUserData.user_signature
        }
      }

      const findEmploymentContract = await UserEmploymentContract.findOne({
        where: { user_id: req.user.id },
        order: [["id", "desc"]],
      });

      const findUserMeta: any = await UserMeta.findOne({
        where: { user_id: findUserData.id },
      });

      let findLatestGeneral,
        findLatestDept,
        findLatestAdditiTemplate,
        findLatestAdditiTemplateIds;
      let additionalDuties = "";
      if (findUserMeta?.general_template) {
        findLatestGeneral = await EmpContractTemplateVersion.findOne({
          where: { emp_contract_template_id: findUserMeta?.general_template },
          order: [["id", "desc"]],
        });
      }
      let jobTitle: any;

      if (findUserMeta?.department_template) {
        findLatestDept = await EmpContractTemplateVersion.findOne({
          where: {
            emp_contract_template_id: findUserMeta?.department_template,
          },
          order: [["id", "desc"]],
        });
        const findContractName = await EmpContractTemplate.findOne({
          where: { id: findUserMeta?.department_template },
        });
        if (findContractName) {
          jobTitle = findContractName.name ? findContractName.name : "";
        }
      }

      if (findUserMeta?.additional_template) {
        const findAdditionalTemplate = findUserMeta?.additional_template
          .split(",")
          .map(Number);
        const findLatestAdditionalArr: any = [];

        for (let i = 0; i < findAdditionalTemplate.length; i++) {
          const empContract = findAdditionalTemplate[i];
          findLatestAdditiTemplate = await EmpContractTemplateVersion.findOne({
            where: { emp_contract_template_id: empContract },
            order: [["id", "desc"]],
          });

          // Add the ID to the array
          findLatestAdditionalArr.push(findLatestAdditiTemplate?.id);
          additionalDuties +=
            (additionalDuties ? " <br> " : "") +
            findLatestAdditiTemplate?.content; // Append subsequent content
        }
        findLatestAdditiTemplateIds = findLatestAdditionalArr.toString();
      }

      const isTemplateUpdated =
        findLatestGeneral?.id != findEmploymentContract?.general_template ||
        findLatestDept?.id != findEmploymentContract?.department_template ||
        findLatestAdditiTemplateIds !=
        findEmploymentContract?.additional_template ||
        findUserMeta?.other != findEmploymentContract?.other ||
        findEmploymentContract?.wages_hours != findUserMeta?.wages_hours ||
        findEmploymentContract?.fixed_types != findUserMeta?.fixed_types ||
        findEmploymentContract?.leave_policy_id !=
        findUserMeta?.leave_policy_id ||
        findEmploymentContract?.probation_length !=
        findUserMeta?.probation_length ||
        findEmploymentContract?.working_hours != findUserMeta?.working_hours ||
        findEmploymentContract?.duration_type != findUserMeta?.duration_type ||
        findEmploymentContract?.wage_type != findUserMeta?.wage_type ||
        findEmploymentContract?.contract_remark !=
        findUserMeta?.contract_remark ||
        findEmploymentContract?.contract_name != findUserMeta?.contract_name ||
        findEmploymentContract?.leave_type_id != findUserMeta?.leave_type_id ||
        findEmploymentContract?.leave_days != findUserMeta?.leave_days ||
        findEmploymentContract?.leave_remark != findUserMeta?.leave_remark ||
        findEmploymentContract?.leave_duration_type !=
        findUserMeta?.leave_duration_type ||
        findEmploymentContract?.place_of_work != findUserMeta?.place_of_work ||
        findEmploymentContract?.contract_name_id !=
        findUserMeta?.contract_name_id ||
        findEmploymentContract?.has_holiday_entitlement !=
        findUserMeta?.has_holiday_entitlement ||
        findEmploymentContract?.holiday_entitlement_remark !=
        findUserMeta?.holiday_entitlement_remark;

      const isFormExist = await UserCheckList.findOne({
        where: {
          checklist_id: isCheckListExist.id,
          from_user_id: user_id,
        },
      });

      if (isFormExist && isFormExist.status == check_list_status.COMPLETED) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_FORM_ALREADY_EXIST"),
        });
      }

      if (
        (is_confirm_sign == true || is_confirm_sign == "true") &&
        !findUserData.user_signature
      ) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_USER_SIGNATURE_MISSING"),
        });
      }

      const getHrmcDetail: any = await HrmcForm.findOne({
        where: { checklist_id: 3, user_id: user_id },
      });
      fileName =
        isTemplateUpdated &&
          (!findEmploymentContract?.contract_with_sign ||
            findEmploymentContract?.contract_with_sign == "")
          ? `emp_contract_${findUserData?.user_first_name}_${findUserData?.user_last_name}_${findUserData.id}_${moment().format("YYYY-MM-DD_HH-mm-sss")}.pdf`
          : findEmploymentContract?.contract_with_sign
            ? findEmploymentContract?.contract_with_sign
            : `emp_contract_${findUserData?.user_first_name}_${findUserData?.user_last_name}_${findUserData.id}_${moment().format("YYYY-MM-DD_HH-mm-sss")}.pdf`;

      // const destination_path = path.resolve(
      //   __dirname,
      //   "..",
      //   "uploads",
      //   "onbording_documents",
      //   `${findUserData.id}`,
      //   fileName,
      // );
      // if (
      //   !fs.existsSync(
      //     path.resolve(
      //       __dirname,
      //       "..",
      //       "uploads",
      //       "onbording_documents",
      //       `${findUserData.id}`,
      //     ),
      //   )
      // ) {
      //   fs.mkdirSync(
      //     path.resolve(
      //       __dirname,
      //       "..",
      //       "uploads",
      //       "onbording_documents",
      //       `${findUserData.id}`,
      //     ),
      //     { recursive: true },
      //   );
      // }
      const addressParts = [];

      if (findUserData?.address_line1) {
        addressParts.push(findUserData.address_line1);
      }
      if (findUserData?.address_line2) {
        addressParts.push(findUserData.address_line2);
      }
      if (findUserData?.pin_code) {
        addressParts.push(findUserData.pin_code);
      }
      if (findUserData?.geo_city) {
        addressParts.push(
          (await getGeoDetails(findUserData.geo_city)).place_name,
        );
      }
      if (findUserData?.geo_state) {
        addressParts.push(
          (await getGeoDetails(findUserData.geo_state)).place_name,
        );
      }
      if (findUserData?.geo_country) {
        addressParts.push(
          (await getGeoDetails(findUserData.geo_country)).place_name,
        );
      }

      const employeeName = [];

      if (findUserData?.user_first_name) {
        employeeName.push(findUserData.user_first_name);
      }
      if (findUserData?.user_middle_name) {
        employeeName.push(findUserData.user_middle_name);
      }
      if (findUserData?.user_last_name) {
        employeeName.push(findUserData.user_last_name);
      }
      const findGeneralSetting: any = await getGeneralSettingObj(
        req.user.organization_id,
      );
      let findBranchSetting: any;
      if (findUserData.branch_id) {
        findBranchSetting = await getBranchSettingObj(findUserData.branch_id);
      }

      // let leavePolicy
      // if (findUserMeta?.leave_policy_id) {
      //   leavePolicy = await LeavePolicyModel.findOne({ where: { id: findUserMeta?.leave_policy_id }, raw: true, nest: true });
      // }
      let namaste_logo: any = `${global.config.API_BASE_URL} + "/email_logo/logo.png",`
      if (findGeneralSetting && findGeneralSetting.brand_logo_link) {
        namaste_logo = findGeneralSetting.brand_logo_link;
      }
      const leaveTypeObj: any = {
        durationType: "Days",
        days:
          findUserMeta && findUserMeta?.leave_days
            ? findUserMeta?.leave_days
            : 0,
      };

      let getContractName: any;
      if (findUserMeta?.contract_name_id) {
        getContractName =
          (await ContractNameModel.findOne({
            where: { id: findUserMeta?.contract_name_id },
          })) || "";
      }
      const compileData = {
        NAMASTE_LOGO: namaste_logo,
        branch_heading_employer_name: findBranchSetting && findBranchSetting.branch_employer_name ? findBranchSetting.branch_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "MicrOffice",
        employer_name: findBranchSetting && findBranchSetting.branch_heading_employer_name ? findBranchSetting.branch_heading_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "MicrOffice",
        branch_heading_name: findBranchSetting && findBranchSetting.branch_heading_name ? findBranchSetting.branch_heading_name : "MicrOffice",
        branch_heading_work_place: findBranchSetting && findBranchSetting.branch_heading_work_place ? findBranchSetting.branch_heading_work_place : null,
        work_place: findUserMeta && findUserMeta?.place_of_work ? findUserMeta.place_of_work : findBranchSetting && findBranchSetting.branch_work_place ? findBranchSetting.branch_work_place : null,
        employee_name: employeeName.join(" "),
        employee_address: addressParts.join(", "),
        employee_sign:
          is_confirm_sign == true || is_confirm_sign == "true"
            ? findUserData.user_signature && findUserData.user_signature != ""
              ? `${global.config.API_BASE_URL}${findUserData.user_signature}`
              : null
            : null,
        employer_sign:
          findBranchSetting && findBranchSetting.branch_sign
            ? `${findBranchSetting.branch_sign}`
            : findGeneralSetting.employer_sign &&
              findGeneralSetting.employer_sign != ""
              ? findGeneralSetting.employer_sign
              : null,
        insurance_number: getHrmcDetail?.insurance_number,
        job_title: jobTitle,
        joining_date: findUserData?.user_joining_date
          ? moment(findUserData?.user_joining_date).format("DD/MM/YYYY")
          : "",
        date: moment().format("DD/MM/YYYY"),
        generalContent: findLatestGeneral?.content,
        deptContent: findLatestDept?.content,
        addittionalContent: additionalDuties ? additionalDuties : "",
        otherContent: findUserMeta?.other,
        expire_date: findUserMeta?.expire_date
          ? moment(findUserMeta?.expire_date).format("DD/MM/YYYY")
          : "",
        // start_date: findUserMeta?.start_date ? moment(findUserMeta?.start_date).format("DD/MM/YYYY") : "",
        expire_duration: findUserMeta?.expire_duration,
        contract_type: JSON.parse(JSON.stringify(findUserMeta)),
        wages_per_hours: findUserMeta?.wages_hours,
        wages_type: findUserMeta?.wage_type,
        tips_grade: findUserMeta?.tips_grade,
        probation_period: findUserMeta?.probation_length,
        fixed_types:
          findUserMeta?.wage_type &&
            findUserMeta.wage_type === wageType.FIXED &&
            findUserMeta?.fixed_types
            ? findUserMeta?.fixed_types
            : findUserMeta?.fixed_types,
        annual_holiday: findUserMeta?.has_holiday_entitlement
          ? leaveTypeObj
            ? leaveTypeObj
            : null
          : null,
        contract_type_name: getContractName?.contract_name
          ? getContractName?.contract_name
          : "",
        nationality: findUserData?.country ? findUserData?.country : "",
        registration_number:
          findBranchSetting && findBranchSetting?.registration_number
            ? findBranchSetting?.registration_number
            : null,
      };
      const s3Result = await generateS3EmployeeContract(
        fileName,
        compileData,
        FORMCONSTANT.EMPLOYE_CONTRACT.template,
        findUserData.organization_id,
        findUserData.id
      );
      if (s3Result.success) {
        itemId = s3Result.path;
        await User.setHeaders(req).update(
          { employment_contract: fileName },
          { where: { id: findUserData.id } },
        );

        if (findEmploymentContract) {
          if (isTemplateUpdated) {
            await UserEmploymentContract.update(
              {
                contract_status: contract_status.INACTIVE,
                updated_by: req.user.id,
              },
              { where: { user_id: findUserData?.id } },
            );

            await UserEmploymentContract.create({
              user_id: req.user.id,
              contract_with_sign: s3Result.item_id?.toString(),
              is_confirm_sign:
                is_confirm_sign == true || is_confirm_sign == "true"
                  ? true
                  : false,
              contract_status: contract_status.ACTIVE,
              updated_by: req.user.id,
              created_by: req.user.id,
              general_template: findLatestGeneral?.id,
              department_template: findLatestDept?.id,
              additional_template: findLatestAdditiTemplateIds,
              other: findUserMeta?.other,
              expire_date: findUserMeta?.expire_date,
              start_date: findUserData?.user_joining_date,
              fixed_types: findUserMeta?.fixed_types,
              wages_hours: findUserMeta?.wages_hours,
              leave_policy_id: findUserMeta?.leave_policy_id,
              probation_length: findUserMeta?.probation_length,
              working_hours: findUserMeta?.working_hours,
              duration_type: findUserMeta?.duration_type,
              wage_type: findUserMeta?.wage_type,
              contract_remark: findUserMeta?.contract_remark,
              contract_name: findUserMeta?.contract_name,
              leave_type_id: findUserMeta?.leave_type_id,
              leave_days: findUserMeta?.leave_days,
              leave_remark: findUserMeta?.leave_remark,
              leave_duration_type: findUserMeta?.leave_duration_type,
              place_of_work: findUserMeta?.place_of_work,
              working_hour_per_day: findGeneralSetting?.working_hours_per_day
                ? findGeneralSetting?.working_hours_per_day
                : null,
              max_limit_per_week: findGeneralSetting?.max_limit_per_week
                ? findGeneralSetting?.max_limit_per_week
                : null,
              contract_name_id: findUserMeta?.contract_name_id,
              has_holiday_entitlement: findUserMeta?.has_holiday_entitlement,
              holiday_entitlement_remark:
                findUserMeta?.holiday_entitlement_remark,
            } as any);
          } else {
            await UserEmploymentContract.update(
              {
                contract_with_sign: fileName,
                is_confirm_sign:
                  is_confirm_sign == true || is_confirm_sign == "true"
                    ? true
                    : false,
                contract_status: contract_status.ACTIVE,
                updated_by: req.user.id,
              },
              { where: { id: findEmploymentContract?.id } },
            );
          }
        } else {
          await UserEmploymentContract.create({
            user_id: req.user.id,
            contract_with_sign: s3Result.item_id?.toString(),
            is_confirm_sign:
              is_confirm_sign == true || is_confirm_sign == "true" ? true : false,
            contract_status: contract_status.ACTIVE,
            updated_by: req.user.id,
            created_by: req.user.id,
            general_template: findLatestGeneral?.id,
            department_template: findLatestDept?.id,
            additional_template: findLatestAdditiTemplateIds,
            other: findUserMeta?.other,
            expire_date: findUserMeta?.expire_date,
            start_date: findUserData?.user_joining_date,
            fixed_types: findUserMeta?.fixed_types,
            contract_type: findUserMeta?.contract_type,
            wages_hours: findUserMeta?.wages_hours,
            leave_policy_id: findUserMeta?.leave_policy_id,
            probation_length: findUserMeta?.probation_length,
            working_hours: findUserMeta?.working_hours,
            duration_type: findUserMeta?.duration_type,
            wage_type: findUserMeta?.wage_type,
            contract_remark: findUserMeta?.contract_remark,
            contract_name: findUserMeta?.contract_name,
            leave_type_id: findUserMeta?.leave_type_id,
            leave_days: findUserMeta?.leave_days,
            leave_remark: findUserMeta?.leave_remark,
            leave_duration_type: findUserMeta?.leave_duration_type,
            place_of_work: findUserMeta?.place_of_work,
            working_hour_per_day: findGeneralSetting?.working_hours_per_day
              ? findGeneralSetting?.working_hours_per_day
              : null,
            max_limit_per_week: findGeneralSetting?.max_limit_per_week
              ? findGeneralSetting?.max_limit_per_week
              : null,
            contract_name_id: findUserMeta?.contract_name_id,
            has_holiday_entitlement: findUserMeta?.has_holiday_entitlement,
            holiday_entitlement_remark: findUserMeta?.holiday_entitlement_remark,
          } as any);
        }
      }
    }
    if (isCreated) {
      if (isFormExist && isFormExist.status == check_list_status.PENDING) {
        if (isCheckListExist.prefix == "EC") {
          if (is_confirm_sign == true || is_confirm_sign == "true") {
            await UserCheckList.setHeaders(req).update(
              {
                checklist_id: isCheckListExist.id,
                from_user_id: user_id,
                to_user_id: user_id,
                status: check_list_status.COMPLETED,
                is_last_rejected: false,
                created_by: user_id,
                updated_by: user_id,
              } as any,
              {
                where: {
                  checklist_id: isCheckListExist.id,
                  from_user_id: user_id,
                  to_user_id: user_id,
                },
              },
            );
          }
        } else {
          await UserCheckList.setHeaders(req).update(
            {
              checklist_id: isCheckListExist.id,
              from_user_id: user_id,
              to_user_id: user_id,
              status:
                isCheckListExist.prefix == "RTWC" && is_confirm_upload == false
                  ? check_list_status.PENDING
                  : check_list_status.COMPLETED,
              is_last_rejected: false,
              created_by: user_id,
              updated_by: user_id,
            } as any,
            {
              where: {
                checklist_id: isCheckListExist.id,
                from_user_id: user_id,
                to_user_id: user_id,
              },
            },
          );
        }
      } else {
        if (isCheckListExist.prefix == "EC") {
          if (is_confirm_sign == true || is_confirm_sign == "true") {
            await UserCheckList.setHeaders(req).create({
              checklist_id: isCheckListExist.id,
              from_user_id: user_id,
              to_user_id: user_id,
              status: check_list_status.COMPLETED,
              is_last_rejected: false,
              created_by: user_id,
              updated_by: user_id,
            } as any);
          }
        } else {
          await UserCheckList.setHeaders(req).create({
            checklist_id: isCheckListExist.id,
            from_user_id: user_id,
            to_user_id: user_id,
            status:
              isCheckListExist.prefix == "RTWC" && is_confirm_upload == false
                ? check_list_status.PENDING
                : check_list_status.COMPLETED,
            is_last_rejected: false,
            created_by: user_id,
            updated_by: user_id,
          } as any);
        }
      }
      if (isCheckListExist.prefix != "HSI") {
        if (isCheckListExist.prefix == "EC") {
          if (is_confirm_sign == true || is_confirm_sign == "true") {
            await User.setHeaders(req).update(
              { user_status: user_status.ONGOING, updated_by: user_id },
              { where: { id: user_id } },
            );
          }
        } else {
          await User.setHeaders(req).update(
            { user_status: user_status.ONGOING, updated_by: user_id },
            { where: { id: user_id } },
          );
        }
      }
      const responseObj: any = {
        status: true,
        message: res.__("SUCCESS_FORM_CREATED"),
      };
      if (isCheckListExist.prefix == "EC") {
        if (itemId) {
          responseObj.employment_contract = `${global.config.API_BASE_URL}${itemId}`;
        } else {
          responseObj.employment_contract = `${global.config.API_BASE_URL}onbording_documents/${req.user.id}/${fileName}`;
        }
        responseObj.is_confirm_sign = is_confirm_sign;
        setTimeout(() => {
          return res.status(StatusCodes.OK).json(responseObj);
        }, 2000);
      } else {
        return res.status(StatusCodes.OK).json(responseObj);
      }
    } else {
      await deleteFiles(req.files);
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_FORM_CREATION"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const requestForm = async (req: any, res: Response) => {
  try {
    const user_id = req.user.id;
    const findUserDetail = await User.findOne({
      attributes: [
        "user_first_name",
        "user_email",
        "organization_id",
        "branch_id",
      ],
      where: { id: user_id, organization_id: req.user.organization_id },
      raw: true,
    });
    if (!findUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    if (req.user.user_status != user_status.ONGOING) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }
    const totalCheckList = await CheckList.count({
      where: {
        type: Sequelize.literal(
          `(type & ${CHECKLISTTYPE.USER}) = ${CHECKLISTTYPE.USER}`,
        ),
        checklist_status: checklist_status.ACTIVE,
        prefix: { [Op.not]: "HSI" },
      },
    });
    const checkFormExist = await UserCheckList.findAll({
      attributes: ["checklist_id"],
      where: {
        from_user_id: user_id,
        to_user_id: user_id,
        status: check_list_status.COMPLETED,
        checklist_id: {
          [Op.not]: sequelize.literal(
            `(SELECT id FROM nv_checklist WHERE nv_checklist.prefix = 'HSI')`,
          ),
        },
      },
      raw: true,
    });
    if (checkFormExist.length != totalCheckList) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("ERROR_ONBORDING_PENDING"),
      });
    } else {
      await User.setHeaders(req).update(
        { user_status: user_status.COMPLETED, updated_by: user_id },
        { where: { id: user_id } },
      );
      const templateData: any = {
        name: req.user.user_first_name,
        to_email: req.user.user_email,
        mail_type: "onboarding_completed",
        ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
        ADDRESS: EMAIL_ADDRESS.ADDRESS,
        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
        EMAIL: EMAIL_ADDRESS.EMAIL,
        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
        smtpConfig: "INFO",
      };
      const getAdmins = await getAdminStaffs(findUserDetail?.branch_id, user_id, null, req.user.organization_id);
      if (getAdmins && getAdmins.length) {
        templateData.email = getAdmins
          .map((admins: any) => admins.user_email)
          .join(",");
        await sendEmailNotification(templateData);
      }
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FORM_REQUEST_SENDED"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const updateForm = async (req: any, res: Response) => {
  try {
    if (
      !fs.existsSync(
        path.resolve(__dirname, "..", "uploads", "onbording_documents"),
      )
    ) {
      fs.mkdirSync(
        path.resolve(__dirname, "..", "uploads", "onbording_documents"),
        { recursive: true },
      );
    }
    const user_id = req.user.id;
    const { checklist_id, form_user_id }: any = req.query;
    const {
      user_gender,
      user_gender_other,
      marital_status,
      marital_status_other,
      user_first_name,
      user_middle_name,
      user_last_name,
      user_phone_number,
      emergency_contact,
      user_email,
      birth_country,
      date_of_birth,
      user_joining_date,
      pin_code,
      address_line1,
      user_designation,
      address_line2,
      medical_disability = null,
      medical_disability_detail = null,
      kin1_name = null,
      kin1_relation = null,
      kin1_address = null,
      kin1_mobile_number = null,
      kin2_name = null,
      kin2_relation = null,
      kin2_address = null,
      kin2_mobile_number = null,
      professional1_name_contact = null,
      professional1_role_description = null,
      professional2_name_contact = null,
      professional2_role_description = null,
      professional1_start_date = null,
      professional1_end_date = null,
      professional2_start_date = null,
      professional2_end_date = null,
      passport_no = null,
      issued_date = null,
      permit_type = null,
      permit_type_other,
      validity = null,
      bank_account_name = null,
      bank_account_number = null,
      bank_sort_code = null,
      bank_society_name = null,
      bank_address = null,
      insurance_number = null,
      postgraduate_loan = null,
      statement_apply = null,
      is_current_information = null,
      another_job = null,
      private_pension = null,
      payment_from = null,
      load_guidance = null,
      statementA = null,
      statementB = null,
      statementC = null,
      // healthSafetyList = [],
      is_confirm_upload,
      // health_safety_complete = false,
      is_confirm_sign = false,
    } = req.body;
    const is_uk_citizen =
      req.body.is_uk_citizen == "false"
        ? false
        : req.body.is_uk_citizen == "true"
          ? true
          : req.body.is_uk_citizen;
    const has_right_to_work_in_uk =
      req.body.has_right_to_work_in_uk == "false"
        ? false
        : req.body.has_right_to_work_in_uk == "true"
          ? true
          : req.body.has_right_to_work_in_uk;
    const has_student_or_pg_loan =
      req.body.has_student_or_pg_loan == "false"
        ? false
        : req.body.has_student_or_pg_loan == "true"
          ? true
          : req.body.has_student_or_pg_loan;
    const has_p45_form =
      req.body.has_p45_form == "false"
        ? false
        : req.body.has_p45_form == "true"
          ? true
          : req.body.has_p45_form;
    const { error } = await onbordingValidator.updateForm.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    let check_date = req.body.check_date;

    if (check_date == "") {
      check_date = null;
    }
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id, organization_id: req.user.organization_id },
      raw: true,
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    // const findFormUser: any = await User.findOne({ where: { id: form_user_id, user_status: { [Op.notIn]: [user_status.DELETED, user_status.CANCELLED] } } })

    const userRoleName = await roleName([
      req.headers["platform-type"] == "web"
        ? getUserDetail.web_user_active_role_id
        : getUserDetail.user_active_role_id,
    ]);
    if (
      !(
        userRoleName.includes(ROLE_CONSTANT.ADMIN) ||
        userRoleName.includes(ROLE_CONSTANT.SUPER_ADMIN) ||
        userRoleName.includes(ROLE_CONSTANT.HR) ||
        userRoleName.includes(ROLE_CONSTANT.DIRECTOR) ||
        userRoleName.includes(ROLE_CONSTANT.BRANCH_MANAGER) ||
        userRoleName.includes(ROLE_CONSTANT.HOTEL_MANAGER)
      )
    ) {
      await deleteFiles(req.files);
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }
    const isCheckListExist: any = await CheckList.findOne({
      where: {
        id: checklist_id,
        type: Sequelize.literal(
          `(type & ${CHECKLISTTYPE.USER}) = ${CHECKLISTTYPE.USER}`,
        ),
        checklist_status: checklist_status.ACTIVE,
      },
      raw: true,
    });
    if (!isCheckListExist) {
      await deleteFiles(req.files);
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_CHECKLIST_NOT_FOUND"),
      });
    }
    const isFormExist = await UserCheckList.findOne({
      where: {
        checklist_id: isCheckListExist.id,
        from_user_id: form_user_id,
        status: check_list_status.COMPLETED,
      },
    });

    const isFormPending = await UserCheckList.findOne({
      where: {
        checklist_id: isCheckListExist.id,
        from_user_id: form_user_id,
        status: check_list_status.PENDING,
      },
    });
    await User.setHeaders(req).update(
      {
        user_gender,
        user_gender_other,
        marital_status,
        marital_status_other,
        user_first_name,
        user_middle_name,
        user_last_name,
        user_phone_number,
        emergency_contact,
        user_email,
        country: birth_country,
        date_of_birth,
        user_joining_date,
        pin_code,
        address_line1,
        user_designation,
        updated_by: req.user.id,
        address_line2,
      },
      { where: { id: form_user_id } },
    );
    let isUpdated: string = "false";
    if (isCheckListExist.prefix == "RTWC") {
      if (req.files || is_confirm_upload) {
        const findWorkCheckList: any = await RightToWorkCheckList.findOne({
          where: { checklist_id: isCheckListExist.id, user_id: form_user_id },
        });
        // Check if the user has the right to work in the UK
        if (
          has_right_to_work_in_uk == false ||
          has_right_to_work_in_uk == "false"
        ) {
          if (
            (findWorkCheckList &&
              findWorkCheckList?.has_right_to_work_in_uk == true) ||
            findWorkCheckList?.has_right_to_work_in_uk == "true"
          ) {
            await RightToWorkCheckList.setHeaders(req).update(
              {
                share_code: null,
                passport_front: null,
                passport_back: null,
                cv: null,
                brp_front: null,
                brp_back: null,
                p45: null,
                ni_letter: null,
                student_letter: null,
                statements_dl_utility: null,
                photoID: null,
                is_uk_citizen: null,
                has_right_to_work_in_uk: false,
              } as any,
              { where: { id: findWorkCheckList.id } },
            );
            await UserCheckList.setHeaders(req).update(
              { status: check_list_status.PENDING },
              {
                where: {
                  checklist_id: findWorkCheckList.id,
                  to_user_id: form_user_id,
                  from_user_id: form_user_id,
                },
              },
            );
            return res.status(StatusCodes.OK).json({
              status: true,
              message: res.__("SUCCESS_FORM_UPDATED"),
            });
          } else {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
              status: false,
              message: res.__("CONTACT_TO_ADMIN"),
            });
          }
        }

        // if ((req.files &&
        //   req.files.share_code &&
        //   req.files.share_code.length > 0) && (req.user.web_user_active_role_id >= findFormUser?.web_user_active_role_id)) {

        //   return res.status(StatusCodes.BAD_REQUEST).json({
        //     status: false,
        //     message: res.__("PERMISSION_DENIED"),
        //   });
        // }

        const destination_path = path.resolve(
          __dirname,
          "..",
          "uploads",
          "onbording_documents",
          `${form_user_id}`,
        );
        const passport_front =
          req.files &&
            req.files.passport_front &&
            req.files.passport_front.length > 0
            ? req.files.passport_front[0].item_id
            : findWorkCheckList?.passport_front;
        req.files &&
          req.files.passport_front &&
          req.files.passport_front.length > 0 &&
          req.files.passport_front[0].path
          ? await moveFile(
            req.files.passport_front[0].path,
            destination_path,
            passport_front,
          )
          : "";
        const passport_back =
          req.files &&
            req.files.passport_back &&
            req.files.passport_back.length > 0
            ? req.files.passport_back[0].item_id
            : findWorkCheckList?.passport_back;

        const cv =
          req.files && req.files.cv && req.files.cv.length > 0
            ? req.files.cv[0].item_id
            : findWorkCheckList?.is_uk_citizen != is_uk_citizen
              ? null
              : findWorkCheckList?.cv;
        const share_code =
          req.files && req.files.share_code && req.files.share_code.length > 0
            ? req.files.share_code[0].item_id
            : findWorkCheckList?.share_code;
        const brp_front =
          req.files && req.files.brp_front && req.files.brp_front.length > 0
            ? req.files.brp_front[0].item_id
            : findWorkCheckList?.brp_front;
        const brp_back =
          req.files && req.files.brp_back && req.files.brp_back.length > 0
            ? req.files.brp_back[0].item_id
            : findWorkCheckList?.brp_back;
        const p45 =
          req.files && req.files.p45 && req.files.p45.length > 0
            ? req.files.p45[0].item_id
            : findWorkCheckList?.is_uk_citizen != is_uk_citizen
              ? null
              : findWorkCheckList?.p45;
        const ni_letter =
          req.files && req.files.ni_letter && req.files.ni_letter.length > 0
            ? req.files.ni_letter[0].item_id
            : findWorkCheckList?.is_uk_citizen != is_uk_citizen
              ? null
              : findWorkCheckList?.ni_letter;
        const student_letter =
          req.files &&
            req.files.student_letter &&
            req.files.student_letter.length > 0
            ? req.files.student_letter[0].item_id
            : findWorkCheckList?.student_letter;
        const statements_dl_utility =
          req.files &&
            req.files.statements_dl_utility &&
            req.files.statements_dl_utility.length > 0
            ? req.files.statements_dl_utility[0].item_id
            : findWorkCheckList?.is_uk_citizen != is_uk_citizen
              ? null
              : findWorkCheckList?.statements_dl_utility;

        const photoID =
          req.files && req.files.photoID && req.files.photoID.length > 0
            ? req.files.photoID[0].item_id
            : findWorkCheckList?.photoID;

        if (req.files) {
          // For each file type, move to the appropriate folder if isMovable is true
          const moveS3Files = async (field: string) => {
            if (
              req.files &&
              req.files[field] &&
              req.files[field].length > 0 &&
              req.files[field][0].isMovable
            ) {
              const file = req.files[field][0];
              const destinationKey =
                FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                  req.user.organization_id,
                  req.user.id.toString(),
                  file.filename,
                );

              await moveFileInBucket(
                file.bucket,
                file.path,
                destinationKey,
                file.item_id,
              );
            }
          };

          // Process each file type
          await moveS3Files("passport_front");
          await moveS3Files("passport_back");
          await moveS3Files("cv");
          await moveS3Files("share_code");
          await moveS3Files("brp_front");
          await moveS3Files("brp_back");
          await moveS3Files("p45");
          await moveS3Files("ni_letter");
          await moveS3Files("student_letter");
          await moveS3Files("statements_dl_utility");
          await moveS3Files("photoID");
        }





        const rightToWorkObj: any = {
          checklist_id: isCheckListExist.id,
          user_id: form_user_id,
          updated_by: user_id,
          status: status.ACTIVE,
          has_right_to_work_in_uk: has_right_to_work_in_uk,
          is_uk_citizen: is_uk_citizen,
          share_code: share_code,
          is_confirm_upload: false,
        };
        if (is_confirm_upload) {
          // Check if there was a change in the is_uk_citizen value
          if (
            findWorkCheckList &&
            !findWorkCheckList?.is_uk_citizen &&
            (is_uk_citizen == "true" || is_uk_citizen == true)
          ) {
            // Nullify 'no' side fields when changing from "no" (false) to "yes" (true)
            await RightToWorkCheckList.update(
              {
                share_code: null,
                passport_front: null,
                passport_back: null,
                cv: null,
                brp_front: null,
                brp_back: null,
                p45: null,
                ni_letter: null,
                student_letter: null,
                statements_dl_utility: null,
                is_uk_citizen: true,
              } as any,
              { where: { id: findWorkCheckList.id } },
            );
          } else if (
            findWorkCheckList &&
            findWorkCheckList?.is_uk_citizen &&
            (is_uk_citizen == "false" || is_uk_citizen == false)
          ) {
            await RightToWorkCheckList.update(
              {
                photoID: null,
                cv: null,
                p45: null,
                ni_letter: null,
                statements_dl_utility: null,
                is_uk_citizen: false,
              } as any,
              { where: { id: findWorkCheckList.id } },
            );
          }

          if (
            has_right_to_work_in_uk &&
            (is_uk_citizen == "true" || is_uk_citizen == true)
          ) {
            // if(!photoID || !ni_letter || !statements_dl_utility){
            //   return res.status(StatusCodes.EXPECTATION_FAILED).json({
            //     status: false,
            //     message: res.__("RIGHT_TO_WORK_REQUIRED"),
            //   });
            // }
            rightToWorkObj.photoID = photoID;
            rightToWorkObj.ni_letter = ni_letter;
            rightToWorkObj.statements_dl_utility = statements_dl_utility;
            rightToWorkObj.cv = cv;
            rightToWorkObj.p45 = p45;
            rightToWorkObj.is_confirm_upload = true;
          } else if (
            has_right_to_work_in_uk &&
            (is_uk_citizen == "false" || is_uk_citizen == false)
          ) {
            (rightToWorkObj.share_code = share_code),
              (rightToWorkObj.passport_front = passport_front),
              (rightToWorkObj.passport_back = passport_back),
              (rightToWorkObj.cv = cv),
              (rightToWorkObj.brp_front = brp_front),
              (rightToWorkObj.brp_back = brp_back),
              (rightToWorkObj.p45 = p45),
              (rightToWorkObj.ni_letter = ni_letter),
              (rightToWorkObj.student_letter = student_letter),
              (rightToWorkObj.statements_dl_utility = statements_dl_utility),
              (rightToWorkObj.is_confirm_upload = true);
          }
        }

        if (!isFormExist && !findWorkCheckList) {
          rightToWorkObj.created_by = form_user_id;
          const creteRightToWorkFormDetail =
            await RightToWorkCheckList.setHeaders(req).create(rightToWorkObj);
          if (creteRightToWorkFormDetail) {
            isUpdated = "true";
          }
          await UserCheckList.setHeaders(req).create({
            checklist_id: isCheckListExist.id,
            from_user_id: form_user_id,
            to_user_id: form_user_id,
            status:
              is_confirm_upload == true
                ? check_list_status.COMPLETED
                : check_list_status.PENDING,
            is_last_rejected: false,
            created_by: user_id,
            updated_by: user_id,
          } as any);
        } else {
          const updateRightToWorkFormDetail =
            await RightToWorkCheckList.setHeaders(req).update(rightToWorkObj, {
              where: { id: findWorkCheckList.id },
            });
          if (updateRightToWorkFormDetail.length > 0) {
            isUpdated = "true";
            if (
              isFormPending &&
              (is_confirm_upload == true || is_confirm_upload == "true")
            ) {
              await UserCheckList.setHeaders(req).update(
                {
                  checklist_id: isCheckListExist.id,
                  from_user_id: form_user_id,
                  to_user_id: form_user_id,
                  status: check_list_status.COMPLETED,
                  is_last_rejected: false,
                  created_by: user_id,
                  updated_by: user_id,
                } as any,
                {
                  where: {
                    checklist_id: isCheckListExist.id,
                    from_user_id: form_user_id,
                    to_user_id: form_user_id,
                  },
                },
              );
            }
          }
        }
      }
    }
    if (isCheckListExist.prefix == "NS&HMRCF") {
      const findStarterForm: any = await StarterForm.findOne({
        where: { checklist_id: isCheckListExist.id, user_id: form_user_id },
      });
      const findHrmcForm: any = await HrmcForm.findOne({
        where: { checklist_id: isCheckListExist.id, user_id: form_user_id },
      });
      const newStarterFormObj: any = {
        checklist_id: isCheckListExist.id,
        user_id: form_user_id,
        medical_disability,
        medical_disability_detail,
        kin1_name,
        kin1_relation,
        kin1_address,
        kin1_mobile_number,
        kin2_name,
        kin2_relation,
        kin2_address,
        kin2_mobile_number,
        professional1_name_contact,
        professional1_role_description,
        professional2_name_contact,
        professional2_role_description,
        professional1_start_date,
        professional1_end_date,
        professional2_start_date,
        professional2_end_date,
        passport_no,
        issued_date,
        permit_type,
        permit_type_other,
        validity,
        bank_account_name,
        bank_account_number,
        bank_sort_code,
        bank_society_name,
        bank_address,
        has_student_or_pg_loan,
        has_p45_form,
        updated_by: user_id,
        status: status.ACTIVE,
      };
      const newHrmcFormObj: any = {
        checklist_id: isCheckListExist.id,
        user_id: form_user_id,
        insurance_number: insurance_number,
        postgraduate_loan,
        statement_apply,
        is_current_information,
        another_job,
        private_pension,
        status: status.ACTIVE,
        payment_from,
        load_guidance,
        statementA,
        statementB,
        statementC,
        updated_by: user_id,
      };

      let hmrc_p45_form_id;
      if (!has_student_or_pg_loan && has_p45_form) {
        if (req.files) {
          hmrc_p45_form_id =
            req.files &&
              req.files.hmrc_p45_form &&
              req.files.hmrc_p45_form.length > 0
              ? req.files.hmrc_p45_form[0].item_id
              : findStarterForm && findStarterForm.hmrc_p45_form
                ? findStarterForm.hmrc_p45_form
                : null;

          // Move file to permanent location if needed
          if (
            req.files &&
            req.files.hmrc_p45_form &&
            req.files.hmrc_p45_form.length > 0 &&
            req.files.hmrc_p45_form[0].isMovable
          ) {
            const file = req.files.hmrc_p45_form[0];
            const destinationKey =
              FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                req.user.organization_id,
                req.user.id.toString(),
                file.filename,
              );

            await moveFileInBucket(
              file.bucket,
              file.path,
              destinationKey,
              file.item_id,
            );
          }

          newStarterFormObj.hmrc_p45_form = hmrc_p45_form_id;
        }
      }

      if (!isFormExist && !findStarterForm && !findHrmcForm) {
        newStarterFormObj.created_by = form_user_id;
        newHrmcFormObj.user_id = form_user_id;
        const createNewStarterFrom =
          await StarterForm.setHeaders(req).create(newStarterFormObj);
        const createHrmcFrom =
          await HrmcForm.setHeaders(req).create(newHrmcFormObj);
        if (createNewStarterFrom && createHrmcFrom) {
          isUpdated = "true";
        }
        await UserCheckList.setHeaders(req).create({
          checklist_id: isCheckListExist.id,
          from_user_id: form_user_id,
          to_user_id: form_user_id,
          status: check_list_status.COMPLETED,
          is_last_rejected: false,
          created_by: user_id,
          updated_by: user_id,
        } as any);
      } else {
        if (!has_student_or_pg_loan && has_p45_form) {
          await StarterForm.setHeaders(req).update(
            {
              medical_disability: 0,
              medical_disability_detail: null,
              kin1_name: null,
              kin1_relation: null,
              kin1_address: null,
              kin1_mobile_number: null,
              kin2_name: null,
              kin2_relation: null,
              kin2_address: null,
              kin2_mobile_number: null,
              professional1_name_contact: null,
              professional1_role_description: null,
              professional2_name_contact: null,
              professional2_role_description: null,
              passport_no: null,
              issued_date: null,
              permit_type: null,
              validity: null,
              bank_account_name: null,
              bank_account_number: null,
              bank_sort_code: null,
              bank_society_name: null,
              bank_address: null,
              professional1_start_date: null,
              professional1_end_date: null,
              professional2_start_date: null,
              professional2_end_date: null,
            } as any,
            {
              where: {
                checklist_id: isCheckListExist.id,
                user_id: form_user_id,
              },
            },
          );

          await HrmcForm.setHeaders(req).update(
            {
              insurance_number: null,
              postgraduate_loan: null,
              statement_apply: null,
              is_current_information: null,
              another_job: null,
              country: null,
              private_pension: null,
              payment_from: null,
              load_guidance: null,
              statementA: null,
              statementB: null,
              statementC: null,
            } as any,
            {
              where: {
                checklist_id: isCheckListExist.id,
                user_id: form_user_id,
              },
            },
          );
        } else if (has_student_or_pg_loan || !has_p45_form) {
          await StarterForm.setHeaders(req).update(
            { hmrc_p45_form: null } as any,
            {
              where: {
                checklist_id: isCheckListExist.id,
                user_id: form_user_id,
              },
            },
          );
        }
        const updateNewStarterFrom = await StarterForm.setHeaders(req).update(
          newStarterFormObj,
          {
            where: { id: findStarterForm.id },
          },
        );
        const updateHmrcFrom = await HrmcForm.setHeaders(req).update(
          newHrmcFormObj,
          {
            where: { id: findHrmcForm.id },
          },
        );
        if (updateNewStarterFrom.length > 0 && updateHmrcFrom.length > 0) {
          isUpdated = "true";
          if (isFormPending) {
            await UserCheckList.setHeaders(req).update(
              {
                checklist_id: isCheckListExist.id,
                from_user_id: form_user_id,
                to_user_id: form_user_id,
                status: check_list_status.COMPLETED,
                is_last_rejected: false,
                created_by: user_id,
                updated_by: user_id,
              } as any,
              {
                where: {
                  checklist_id: isCheckListExist.id,
                  from_user_id: form_user_id,
                  to_user_id: form_user_id,
                },
              },
            );
          }
        }
      }
    }
    // if (isCheckListExist.prefix == "HRMCF") {
    //   const findHrmcForm: any = await HrmcForm.findOne({
    //     where: { checklist_id: isCheckListExist.id, user_id: form_user_id },
    //   });

    //   const newHrmcFormObj: any = {
    //     checklist_id: isCheckListExist.id,
    //     user_id: form_user_id,
    //     insurance_number,
    //     postgraduate_loan,
    //     statement_apply,
    //     is_current_information,
    //     another_job,
    //     private_pension,
    //     payment_from,
    //     load_guidence: load_guidance,
    //     statementA: statementA,
    //     statementb: statementB,
    //     statementc: statementC,
    //     updated_by: user_id,
    //   };
    //   if (!isFormExist && !findHrmcForm) {
    //     newHrmcFormObj.user_id = form_user_id;
    //     const createNewStarterFrom = await HrmcForm.setHeaders(req).create(newHrmcFormObj);
    //     if (createNewStarterFrom) {
    //       isUpdated = "true";
    //     }
    //     await UserCheckList.setHeaders(req).create({
    //       checklist_id: isCheckListExist.id,
    //       from_user_id: form_user_id,
    //       to_user_id: form_user_id,
    //       status: check_list_status.COMPLETED,
    //       created_by: user_id,
    //       updated_by: user_id,
    //     } as any);
    //   } else {
    //     const updateNewStarterFrom = await HrmcForm.setHeaders(req).update(newHrmcFormObj, {
    //       where: { id: findHrmcForm.id },
    //     });
    //     if (updateNewStarterFrom.length > 0) {
    //       isUpdated = "true";
    //     }
    //   }
    // }
    if (isCheckListExist.prefix == "HSI") {
      if (isFormExist && isFormExist.status == check_list_status.COMPLETED) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_FORM_ALREADY_EXIST"),
        });
      }
      const findUser = await User.findOne({
        where: { id: form_user_id, organization_id: req.user.organization_id },
        raw: true,
      });
      const whereObj: any = { status: HealthSafetyCategoryItemStatus.ACTIVE };
      if (findUser && findUser.branch_id) {
        whereObj.branch_id = findUser?.branch_id;
      }
      const findBranchWiseCategory = await HealthSafetyCategoryItem.findAll({
        where: whereObj,
      });
      if (findBranchWiseCategory.length > 0) {
        const category_Ids = findBranchWiseCategory.map((category) => {
          return category.category_id;
        });

        let allChildIds = await findAllChildrenWithType(
          category_Ids,
          true,
          true,
        );
        allChildIds = allChildIds
          .filter((child) => child.category_type === category_type.FILE)
          .map((child) => {
            return child.id;
          });
        const findHealthSafetyBranch = await DocumentCategoryBranch.findAll({
          where: {
            category_id: { [Op.in]: allChildIds },
            branch_id: findUser?.branch_id,
            document_category_branch_status:
              document_category_branch_status.ACTIVE,
          },
        });
        allChildIds = findHealthSafetyBranch.map((category) => {
          return category.category_id;
        });
        const findUserTrack = await DocumentCategoryItemTrack.findAll({
          where: {
            user_id: form_user_id,
            category_id: { [Op.in]: allChildIds },
            document_category_item_track_status:
              document_category_item_track_status.COMPLETED,
          },
        });
        if (
          findUserTrack.length > 0 &&
          allChildIds.length > 0 &&
          findUserTrack.length == allChildIds.length
        ) {
          isUpdated = "true";
        } else {
          return res.status(StatusCodes.EXPECTATION_FAILED).json({
            status: false,
            message: res.__("PLEASE_COMPLETE_YOUR_TRAINING_CATEGORY"),
          });
        }

        if (isUpdated) {
          const isFormPending = await UserCheckList.findOne({
            where: {
              checklist_id: isCheckListExist.id,
              from_user_id: form_user_id,
            },
          });
          if (isFormPending) {
            await UserCheckList.setHeaders(req).update(
              {
                checklist_id: isCheckListExist.id,
                from_user_id: form_user_id,
                to_user_id: form_user_id,
                status: check_list_status.COMPLETED,
                is_last_rejected: false,
                created_by: req.user.id,
                updated_by: req.user.id,
              },
              {
                where: {
                  checklist_id: isCheckListExist.id,
                  from_user_id: form_user_id,
                },
              },
            );
          } else {
            await UserCheckList.setHeaders(req).create({
              checklist_id: isCheckListExist.id,
              from_user_id: form_user_id,
              to_user_id: form_user_id,
              status: check_list_status.COMPLETED,
              is_last_rejected: false,
              created_by: req.user.id,
              updated_by: req.user.id,
            } as any);
          }
        }
      } else {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("ITEM_NOT_FOUND"),
        });
      }
    }
    let fileName: any = "", itemId = null;
    if (isCheckListExist.prefix == "EC") {
      const findEmploymentContract = await UserEmploymentContract.findOne({
        where: { user_id: form_user_id },
        order: [["id", "desc"]],
      });

      const findUserData: any = await User.findOne({
        where: { id: form_user_id, organization_id: req.user.organization_id },
        include: {
          model: Branch,
          as: "branch",
          attributes: ["branch_name"],
        },
        raw: true,
        nest: true,
      });

      if (!isNaN(findUserData?.user_signature)) {
        const findItem = await Item.findOne({ where: { id: findUserData?.user_signature } })
        if (findItem) {
          findUserData.user_signature = findItem?.item_location
        }
      } else {
        if (findUserData.user_signature) {
          findUserData.user_signature = `/signatures/` + findUserData.user_signature
        }
      }

      const findUserMeta: any = await UserMeta.findOne({
        where: { user_id: findUserData.id },
      });

      let findLatestGeneral,
        findLatestDept,
        findLatestAdditiTemplate,
        findLatestAdditiTemplateIds;
      let additionalDuties = "";
      let jobTitle: any;
      if (findUserMeta?.general_template) {
        findLatestGeneral = await EmpContractTemplateVersion.findOne({
          where: { emp_contract_template_id: findUserMeta?.general_template },
          order: [["id", "desc"]],
        });
      }
      if (findUserMeta?.department_template) {
        findLatestDept = await EmpContractTemplateVersion.findOne({
          where: {
            emp_contract_template_id: findUserMeta?.department_template,
          },
          order: [["id", "desc"]],
        });
        const findContractName = await EmpContractTemplate.findOne({
          where: { id: findUserMeta?.department_template },
        });
        if (findContractName) {
          jobTitle = findContractName.name ? findContractName.name : "";
        }
      }

      if (findUserMeta?.additional_template) {
        const findAdditionalTemplate = findUserMeta?.additional_template
          .split(",")
          .map(Number);
        const findLatestAdditionalArr: any = [];
        for (let i = 0; i < findAdditionalTemplate.length; i++) {
          const empContract = findAdditionalTemplate[i];
          findLatestAdditiTemplate = await EmpContractTemplateVersion.findOne({
            where: { emp_contract_template_id: empContract },
            order: [["id", "desc"]],
          });

          // Add the ID to the array
          findLatestAdditionalArr.push(findLatestAdditiTemplate?.id);

          additionalDuties +=
            (additionalDuties ? " <br> " : "") +
            findLatestAdditiTemplate?.content; // Append subsequent content
        }
        findLatestAdditiTemplateIds = findLatestAdditionalArr.toString();
      }

      // let leavePolicy
      // if (findUserMeta?.leave_policy_id) {
      //   leavePolicy = await LeavePolicyModel.findOne({ where: { id: findUserMeta?.leave_policy_id }, raw: true, nest: true });
      // }
      const isTemplateUpdated =
        findLatestGeneral?.id != findEmploymentContract?.general_template ||
        findLatestDept?.id != findEmploymentContract?.department_template ||
        findLatestAdditiTemplateIds !=
        findEmploymentContract?.additional_template ||
        findUserMeta?.other != findEmploymentContract?.other ||
        findEmploymentContract?.wages_hours != findUserMeta?.wages_hours ||
        findEmploymentContract?.fixed_types != findUserMeta?.fixed_types ||
        findEmploymentContract?.leave_policy_id !=
        findUserMeta?.leave_policy_id ||
        findEmploymentContract?.probation_length !=
        findUserMeta?.probation_length ||
        findEmploymentContract?.working_hours != findUserMeta?.working_hours ||
        findEmploymentContract?.duration_type != findUserMeta?.duration_type ||
        findEmploymentContract?.wage_type != findUserMeta?.wage_type ||
        findEmploymentContract?.contract_remark !=
        findUserMeta?.contract_remark ||
        findEmploymentContract?.contract_name != findUserMeta?.contract_name ||
        findEmploymentContract?.leave_type_id != findUserMeta?.leave_type_id ||
        findEmploymentContract?.leave_days != findUserMeta?.leave_days ||
        findEmploymentContract?.leave_remark != findUserMeta?.leave_remark ||
        findEmploymentContract?.leave_duration_type !=
        findUserMeta?.leave_duration_type ||
        findEmploymentContract?.place_of_work != findUserMeta?.place_of_work ||
        findEmploymentContract?.contract_name_id !=
        findUserMeta?.contract_name_id ||
        findEmploymentContract?.has_holiday_entitlement !=
        findUserMeta?.has_holiday_entitlement ||
        findEmploymentContract?.holiday_entitlement_remark !=
        findUserMeta?.holiday_entitlement_remark;

      if (
        (is_confirm_sign == true || is_confirm_sign == "true") &&
        !findUserData.user_signature
      ) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_USER_SIGNATURE_MISSING"),
        });
      }

      const getHrmcDetail: any = await HrmcForm.findOne({
        where: { checklist_id: 3, user_id: form_user_id },
      });
      fileName =
        isTemplateUpdated &&
          (!findEmploymentContract?.contract_with_sign ||
            findEmploymentContract?.contract_with_sign == "")
          ? `emp_contract_${findUserData?.user_first_name}_${findUserData?.user_last_name}_${findUserData.id}_${moment().format("YYYY-MM-DD_HH-mm-sss")}.pdf`
          : findEmploymentContract?.contract_with_sign;

      // const destination_path = path.resolve(
      //   __dirname,
      //   "..",
      //   "uploads",
      //   "onbording_documents",
      //   `${findUserData.id}`,
      //   fileName,
      // );
      // if (
      //   !fs.existsSync(
      //     path.resolve(
      //       __dirname,
      //       "..",
      //       "uploads",
      //       "onbording_documents",
      //       `${findUserData.id}`,
      //     ),
      //   )
      // ) {
      //   fs.mkdirSync(
      //     path.resolve(
      //       __dirname,
      //       "..",
      //       "uploads",
      //       "onbording_documents",
      //       `${findUserData.id}`,
      //     ),
      //     { recursive: true },
      //   );
      // }
      const addressParts = [];

      if (findUserData?.address_line1) {
        addressParts.push(findUserData.address_line1);
      }
      if (findUserData?.address_line2) {
        addressParts.push(findUserData.address_line2);
      }
      if (findUserData?.pin_code) {
        addressParts.push(findUserData.pin_code);
      }
      if (findUserData?.geo_city) {
        addressParts.push(
          (await getGeoDetails(findUserData.geo_city)).place_name,
        );
      }
      if (findUserData?.geo_state) {
        addressParts.push(
          (await getGeoDetails(findUserData.geo_state)).place_name,
        );
      }
      if (findUserData?.geo_country) {
        addressParts.push(
          (await getGeoDetails(findUserData.geo_country)).place_name,
        );
      }

      const employeeName = [];

      if (findUserData?.user_first_name) {
        employeeName.push(findUserData.user_first_name);
      }
      if (findUserData?.user_middle_name) {
        employeeName.push(findUserData.user_middle_name);
      }
      if (findUserData?.user_last_name) {
        employeeName.push(findUserData.user_last_name);
      }
      const findGeneralSetting: any = await getGeneralSettingObj(
        req.user.organization_id,
      );
      let findBranchSetting: any;
      if (findUserData.branch_id) {
        findBranchSetting = await getBranchSettingObj(findUserData.branch_id);
      }

      let namaste_logo: any = `${global.config.API_BASE_URL} + "/email_logo/logo.png",`
      if (findGeneralSetting && findGeneralSetting.brand_logo_link) {
        namaste_logo = findGeneralSetting.brand_logo_link;
      }

      const leaveTypeObj: any = {
        durationType: "Days",
        days:
          findUserMeta && findUserMeta?.leave_days
            ? findUserMeta?.leave_days
            : 0,
      };

      let getContractName: any;
      if (findUserMeta?.contract_name_id) {
        getContractName =
          (await ContractNameModel.findOne({
            where: { id: findUserMeta?.contract_name_id },
          })) || "";
      }
      const compileData = {
        NAMASTE_LOGO: namaste_logo,
        branch_heading_employer_name: findBranchSetting && findBranchSetting.branch_employer_name ? findBranchSetting.branch_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "MicrOffice",
        employer_name: findBranchSetting && findBranchSetting.branch_heading_employer_name ? findBranchSetting.branch_heading_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "MicrOffice",
        branch_heading_name: findBranchSetting && findBranchSetting.branch_heading_name ? findBranchSetting.branch_heading_name : "MicrOffice",
        branch_heading_work_place: findBranchSetting && findBranchSetting.branch_heading_work_place ? findBranchSetting.branch_heading_work_place : null,
        work_place: findUserMeta && findUserMeta?.place_of_work ? findUserMeta.place_of_work : findBranchSetting && findBranchSetting.branch_work_place ? findBranchSetting.branch_work_place : null,
        employee_name: employeeName.join(" "),
        employee_address: addressParts.join(", "),
        employee_sign:
          is_confirm_sign == true || is_confirm_sign == "true"
            ? findUserData.user_signature && findUserData.user_signature != ""
              ? `${global.config.API_BASE_URL}${findUserData.user_signature}`
              : null
            : null,
        employer_sign:
          findBranchSetting && findBranchSetting.branch_sign
            ? `${findBranchSetting.branch_sign}`
            : findGeneralSetting.employer_sign &&
              findGeneralSetting.employer_sign != ""
              ? findGeneralSetting.employer_sign
              : null,
        insurance_number: getHrmcDetail?.insurance_number,
        job_title: jobTitle,
        joining_date: findUserData?.user_joining_date
          ? moment(findUserData?.user_joining_date).format("DD/MM/YYYY")
          : "",
        date: moment().format("DD/MM/YYYY"),
        generalContent: findLatestGeneral?.content,
        deptContent: findLatestDept?.content,
        addittionalContent: additionalDuties ? additionalDuties : "",
        otherContent: findUserMeta?.other,
        expire_date: findUserMeta?.expire_date
          ? moment(findUserMeta?.expire_date).format("DD/MM/YYYY")
          : "",
        start_date: findUserData?.user_joining_date
          ? moment(findUserData?.user_joining_date).format("DD/MM/YYYY")
          : "",
        expire_duration: findUserMeta?.expire_duration,
        contract_type: JSON.parse(JSON.stringify(findUserMeta)),
        wages_per_hours: findUserMeta?.wages_hours,
        wages_type: findUserMeta?.wage_type,
        probation_period: findUserMeta?.probation_length,
        fixed_types:
          findUserMeta?.wage_type &&
            findUserMeta.wage_type === wageType.FIXED &&
            findUserMeta?.fixed_types
            ? findUserMeta?.fixed_types
            : findUserMeta?.fixed_types,
        tips_grade: findUserMeta?.tips_grade,
        annual_holiday: findUserMeta?.has_holiday_entitlement
          ? leaveTypeObj
            ? leaveTypeObj
            : null
          : null,
        contract_type_name: getContractName?.contract_name
          ? getContractName?.contract_name
          : "",
        nationality: findUserData?.country ? findUserData?.country : "",
        registration_number:
          findBranchSetting && findBranchSetting?.registration_number
            ? findBranchSetting?.registration_number
            : null,
      };
      // await generateEmployeeContract(
      //   destination_path,
      //   compileData,
      //   FORMCONSTANT.EMPLOYE_CONTRACT.template,
      // );
      const s3Result = await generateS3EmployeeContract(
        fileName,
        compileData,
        FORMCONSTANT.EMPLOYE_CONTRACT.template,
        findUserData.organization_id,
        findUserData.id
      );

      if (s3Result.success) {
        itemId = s3Result.path;
        await User.setHeaders(req).update(
          { employment_contract: fileName },
          { where: { id: findUserData.id } },
        );

        if (findUserMeta) {
          findUserMeta.isDraft = false;
          await findUserMeta?.save();
        }

        if (findEmploymentContract) {
          if (isTemplateUpdated) {
            await UserEmploymentContract.update(
              {
                contract_status: contract_status.INACTIVE,
                updated_by: req.user.id,
              },
              { where: { user_id: findUserData?.id } },
            );

            await UserEmploymentContract.create({
              user_id: req.user.id,
              contract_with_sign: s3Result.item_id?.toString(),
              is_confirm_sign:
                is_confirm_sign == true || is_confirm_sign == "true"
                  ? true
                  : false,
              contract_status: contract_status.ACTIVE,
              updated_by: req.user.id,
              created_by: req.user.id,
              general_template: findLatestGeneral?.id,
              department_template: findLatestDept?.id,
              additional_template: findLatestAdditiTemplateIds,
              other: findUserMeta?.other,
              expire_date: findUserMeta?.expire_date,
              start_date: findUserData?.user_joining_date,
              fixed_types: findUserMeta?.fixed_types,
              contract_type: findUserMeta?.contract_type,
              wages_hours: findUserMeta?.wages_hours,
              leave_policy_id: findUserMeta?.leave_policy_id,
              probation_length: findUserMeta?.probation_length,
              working_hours: findUserMeta?.working_hours,
              duration_type: findUserMeta?.duration_type,
              wage_type: findUserMeta?.wage_type,
              contract_remark: findUserMeta?.contract_remark,
              contract_name: findUserMeta?.contract_name,
              leave_type_id: findUserMeta?.leave_type_id,
              leave_days: findUserMeta?.leave_days,
              leave_remark: findUserMeta?.leave_remark,
              leave_duration_type: findUserMeta?.leave_duration_type,
              place_of_work: findUserMeta?.place_of_work,
              working_hour_per_day: findGeneralSetting?.working_hours_per_day
                ? findGeneralSetting?.working_hours_per_day
                : null,
              max_limit_per_week: findGeneralSetting?.max_limit_per_week
                ? findGeneralSetting?.max_limit_per_week
                : null,
              contract_name_id: findUserMeta?.contract_name_id,
              has_holiday_entitlement: findUserMeta?.has_holiday_entitlement,
              holiday_entitlement_remark:
                findUserMeta?.holiday_entitlement_remark,
            } as any);
          } else {
            await UserEmploymentContract.update(
              {
                contract_with_sign: s3Result.item_id?.toString(),
                is_confirm_sign:
                  is_confirm_sign == true || is_confirm_sign == "true"
                    ? true
                    : false,
                contract_status: contract_status.ACTIVE,
                updated_by: req.user.id,
              },
              { where: { id: findEmploymentContract?.id } },
            );
          }
        } else {
          await UserEmploymentContract.create({
            user_id: req.user.id,
            contract_with_sign: s3Result.item_id?.toString(),
            is_confirm_sign:
              is_confirm_sign == true || is_confirm_sign == "true" ? true : false,
            contract_status: contract_status.ACTIVE,
            updated_by: req.user.id,
            created_by: req.user.id,
            general_template: findLatestGeneral?.id,
            department_template: findLatestDept?.id,
            additional_template: findLatestAdditiTemplateIds,
            other: findUserMeta?.other,
            expire_date: findUserMeta?.expire_date,
            start_date: findUserData?.user_joining_date,
            fixed_types: findUserMeta?.fixed_types,
            contract_type: findUserMeta?.contract_type,
            wages_hours: findUserMeta?.wages_hours,
            leave_policy_id: findUserMeta?.leave_policy_id,
            probation_length: findUserMeta?.probation_length,
            working_hours: findUserMeta?.working_hours,
            duration_type: findUserMeta?.duration_type,
            wage_type: findUserMeta?.wage_type,
            contract_remark: findUserMeta?.contract_remark,
            contract_name: findUserMeta?.contract_name,
            leave_type_id: findUserMeta?.leave_type_id,
            leave_days: findUserMeta?.leave_days,
            leave_remark: findUserMeta?.leave_remark,
            leave_duration_type: findUserMeta?.leave_duration_type,
            place_of_work: findUserMeta?.place_of_work,
            working_hour_per_day: findGeneralSetting?.working_hours_per_day
              ? findGeneralSetting?.working_hours_per_day
              : null,
            max_limit_per_week: findGeneralSetting?.max_limit_per_week
              ? findGeneralSetting?.max_limit_per_week
              : null,
            contract_name_id: findUserMeta?.contract_name_id,
            has_holiday_entitlement: findUserMeta?.has_holiday_entitlement,
            holiday_entitlement_remark: findUserMeta?.holiday_entitlement_remark,
          } as any);
        }
        if (
          !isFormExist &&
          (is_confirm_sign == true || is_confirm_sign == "true")
        ) {
          await UserCheckList.setHeaders(req).create({
            checklist_id: isCheckListExist.id,
            from_user_id: form_user_id,
            to_user_id: form_user_id,
            status: check_list_status.COMPLETED,
            is_last_rejected: false,
            created_by: user_id,
            updated_by: user_id,
          } as any);
        }
        ("");
        isUpdated = "true";
        if (
          isFormPending &&
          (is_confirm_sign == true || is_confirm_sign == "true")
        ) {
          await UserCheckList.setHeaders(req).update(
            {
              checklist_id: isCheckListExist.id,
              from_user_id: form_user_id,
              to_user_id: form_user_id,
              status: check_list_status.COMPLETED,
              is_last_rejected: false,
              created_by: user_id,
              updated_by: user_id,
            } as any,
            {
              where: {
                checklist_id: isCheckListExist.id,
                from_user_id: form_user_id,
                to_user_id: form_user_id,
              },
            },
          );
        }
      }
    }

    const responseObj: any = {
      status: true,
      message: res.__("SUCCESS_FORM_UPDATED"),
    };
    if (isCheckListExist.prefix == "EC") {
      if (itemId) {
        responseObj.employment_contract = `${global.config.API_BASE_URL}${itemId}`;
      } else {
        responseObj.employment_contract = `${global.config.API_BASE_URL}onbording_documents/${req.user.id}/${fileName}`;
      }
      responseObj.is_confirm_sign = is_confirm_sign;
    }
    if (isUpdated) {
      await UserCheckList.setHeaders(req).update(
        { is_last_rejected: false },
        {
          where: {
            checklist_id: isCheckListExist.id,
            to_user_id: form_user_id,
            from_user_id: form_user_id,
          },
        },
      );
      if (isCheckListExist.prefix == "EC") {
        setTimeout(() => {
          return res.status(StatusCodes.OK).json(responseObj);
        }, 2000);
      } else {
        return res.status(StatusCodes.OK).json(responseObj);
      }
    } else {
      await deleteFiles(req.files);
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_FORM_UPDATION"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const deleteUploadedFiles = async (req: any, res: Response) => {
  try {
    const { field_name, checklist_id, from_user_id } = req.body;

    if (!Array.isArray(field_name) || field_name.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_FIELD_NAME"),
      });
    }

    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id, organization_id: req.user.organization_id },
      raw: true,
    });
    if (!getUserDetail) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("ERROR_USER_NOT_FOUND"),
      });
    }

    // Find records
    const findRightToWork: any = await RightToWorkCheckList.findOne({
      where: { checklist_id, user_id: from_user_id },
    });

    const findStarterForm: any = await StarterForm.findOne({
      where: { checklist_id, user_id: from_user_id },
    });

    const filesToDelete: string[] = [];
    const updatedFields: string[] = [];

    // Fields in RightToWorkCheckList
    const rightToWorkFields = [
      "passport_back",
      "passport_front",
      "cv",
      "share_code",
      "brp_front",
      "brp_back",
      "p45",
      "ni_letter",
      "student_letter",
      "statements_dl_utility",
      "photoID",
    ];

    // Update RightToWorkCheckList
    if (findRightToWork) {
      const updateObj: any = {};

      field_name.forEach((fieldKey) => {
        if (rightToWorkFields.includes(fieldKey) && findRightToWork[fieldKey]) {
          filesToDelete.push(findRightToWork[fieldKey]); // Collect file for deletion
          updateObj[fieldKey] = null; // Nullify field
          updatedFields.push(fieldKey);
        }
      });

      if (Object.keys(updateObj).length > 0) {
        await RightToWorkCheckList.setHeaders(req).update(updateObj, {
          where: { checklist_id, user_id: from_user_id },
        });
      }
    }

    // Update StarterForm
    if (findStarterForm) {
      const updateObj: any = {};

      field_name.forEach((fieldKey) => {
        if (fieldKey === "hmrc_p45_form" && findStarterForm.hmrc_p45_form) {
          filesToDelete.push(findStarterForm.hmrc_p45_form);
          updateObj.hmrc_p45_form = null;
          updatedFields.push("hmrc_p45_form");
        }
      });

      if (Object.keys(updateObj).length > 0) {
        await StarterForm.setHeaders(req).update(updateObj, {
          where: { checklist_id, user_id: from_user_id },
        });
      }
    }

    // Check if files are referenced elsewhere before deletion
    const deletedFiles = [];
    const skippedDeletions = [];

    for (const fileName of filesToDelete) {
      if (!fileName) continue;

      let isReferenced = false;

      // Check RightToWorkCheckList fields
      if (findRightToWork) {
        for (const fieldKey of rightToWorkFields) {
          if (
            !updatedFields.includes(fieldKey) &&
            findRightToWork[fieldKey] === fileName
          ) {
            isReferenced = true;
            break;
          }
        }
      }

      // Check StarterForm if not already referenced
      // if (!isReferenced && findStarterForm?.hmrc_p45_form === fileName) {
      //   isReferenced = true;
      // }

      const deletePath = path.resolve(
        __dirname,
        "../uploads/",
        "onbording_documents",
        `${from_user_id}`,
        `${fileName}`,
      );

      if (!isReferenced && fs.existsSync(deletePath)) {
        try {
          fs.unlinkSync(deletePath);
          deletedFiles.push({ file: fileName });
        } catch (error) {
          skippedDeletions.push({
            file: fileName,
            reason: "File system error",
          });
        }
      } else {
        skippedDeletions.push({
          file: fileName,
          reason: isReferenced ? "Referenced elsewhere" : "File not found",
        });
      }
    }
    console.log({
      updated_fields: updatedFields,
      files_deleted: deletedFiles,
      files_skipped: skippedDeletions,
    });
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("DOCUMENT_REMOVED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getCheckList = async (req: any, res: Response) => {
  try {
    const user_id = req.query.user_id ? req.query.user_id : req.user.id;
    const getCheckList = await CheckList.findAll({
      attributes: [
        "id",
        "checkList_name",
        "order",
        "checklist_type",
        [Sequelize.literal("userCheckList.from_user_id"), "from_user_id"],
        [Sequelize.literal("userCheckList.updatedAt"), "updatedAt"],
        [
          Sequelize.literal("userCheckList.is_last_rejected"),
          "is_last_rejected",
        ],
        [
          Sequelize.literal(`CASE
            WHEN userCheckList.status IS NOT NULL THEN userCheckList.status
            ELSE '${check_list_status.PENDING}'
          END`),
          "status",
        ],
      ],
      include: [
        {
          model: UserCheckList,
          as: "userCheckList",
          attributes: [],
          where: {
            from_user_id: user_id,
          },
          required: false,
        },
      ],
      order: [["order", "ASC"]],
      where: Sequelize.literal(
        `(type & ${CHECKLISTTYPE.USER}) = ${CHECKLISTTYPE.USER} and checklist_status = '${checklist_status.ACTIVE}'`,
      ),
    });

    if (getCheckList.length > 0) {
      const latestUpdatedItem = _.maxBy(
        getCheckList,
        (item: any) => new Date(item.updatedAt || 0),
      );
      const latestUpdatedDate = latestUpdatedItem
        ? latestUpdatedItem.updatedAt
        : null;
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        last_updated: latestUpdatedDate,
        checklist: getCheckList || [],
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_DATA_NOT_FOUND"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getUserFormDetail = async (req: any, res: Response) => {
  try {
    const { checklist_id, from_user_id } = req.query;
    const user_id = from_user_id ? from_user_id : req.user.id;
    const isCheckListExist = await CheckList.findOne({
      where: {
        id: checklist_id,
        type: Sequelize.literal(
          `(type & ${CHECKLISTTYPE.USER}) = ${CHECKLISTTYPE.USER}`,
        ),
        checklist_status: checklist_status.ACTIVE,
      },
      raw: true,
    });
    if (!isCheckListExist) {
      await deleteFiles(req.files);
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_CHECKLIST_NOT_FOUND"),
      });
    }
    const isFormExist = await UserCheckList.findOne({
      where: {
        checklist_id: isCheckListExist.id,
        from_user_id: user_id,
        status: check_list_status.COMPLETED,
      },
    });

    let formData: any;
    if (isCheckListExist.prefix == "RTWC") {
      formData = await RightToWorkCheckList.findOne({
        where: {
          checklist_id: isCheckListExist.id,
          user_id,
          status: status.ACTIVE,
        },
      });
      formData = JSON.parse(JSON.stringify(formData));
      if (!isFormExist && !formData) {
        await deleteFiles(req.files);
        return res.status(StatusCodes.OK).json({
          status: true,
          data: [],
        });
      }

      // formData.has_right_to_work_in_uk = formData.has_right_to_work_in_uk ? true : formData.has_right_to_work_in_uk == false && !typeof formData.has_right_to_work_in_uk == null ? false : null      // formData.is_uk_citizen = formData.is_uk_citizen ? true :  formData.is_uk_citizen == false && !typeof formData.is_uk_citizen == null ? false : null            // Map document item_ids to proper URLs      const documentFields = [        'passport_front', 'passport_back', 'cv', 'share_code',         'brp_front', 'brp_back', 'p45', 'ni_letter',         'student_letter', 'statements_dl_utility', 'photoID'      ];            // Fetch all items at once to improve performance      const itemIds = documentFields        .map(field => formData[field])        .filter(id => id !== null && id !== undefined);              if (itemIds.length > 0) {        const items = await Item.findAll({          where: {             id: { [Op.in]: itemIds },            item_status: item_status.ACTIVE          }        });                // Create a map of item_id to item_location        const itemMap = {};        items.forEach(item => {          itemMap[item.id] = `${global.config.API_BASE_URL}${item.item_location}`;        });                // Set the link fields in formData        documentFields.forEach(field => {          const itemId = formData[field];          if (itemId && itemMap[itemId]) {            formData[`${field}_link`] = itemMap[itemId];          } else {            formData[`${field}_link`] = null;          }        });      } else {        // For backward compatibility with files not yet migrated to S3        documentFields.forEach(field => {          formData[`${field}_link`] = formData[field] ?             `${global.config.API_BASE_URL}/onbording_documents/${formData.user_id}/${formData[field]}` :             null;        });      }
    }

    if (isCheckListExist.prefix == "NS&HMRCF") {
      const getStarterForm = await StarterForm.findOne({
        where: {
          checklist_id: isCheckListExist.id,
          user_id,
          status: status.ACTIVE,
        },
      });
      const getHmrcForm = await HrmcForm.findOne({
        where: {
          checklist_id: isCheckListExist.id,
          user_id,
          status: status.ACTIVE,
        },
        attributes: {
          exclude: [
            "checklist_id",
            "id",
            "status",
            "created_by",
            "updated_by",
            "createdAt",
            "updatedAt",
          ],
        },
      });
      formData = Object.assign(
        {},
        JSON.parse(JSON.stringify(getStarterForm)),
        JSON.parse(JSON.stringify(getHmrcForm)),
      );
      // Set boolean values properly
      formData.has_p45_form = formData.has_p45_form ? true : formData.has_p45_form == false && typeof formData.has_p45_form !== 'undefined' ? false : null;
      formData.has_student_or_pg_loan = formData.has_student_or_pg_loan ? true : formData.has_student_or_pg_loan == false && typeof formData.has_student_or_pg_loan !== 'undefined' ? false : null;

      // Generate HMRC P45 form link
      if (getStarterForm && !getStarterForm.has_student_or_pg_loan && getStarterForm.has_p45_form) {
        if (formData && formData.hmrc_p45_form) {
          // Check if it's an item_id (number) or a legacy filename (string)
          if (!isNaN(Number(formData.hmrc_p45_form))) {
            // It's an item_id, fetch from Item model
            const item = await Item.findOne({
              where: {
                id: formData.hmrc_p45_form,
                item_status: item_status.ACTIVE
              }
            });
            if (item) {
              formData.hmrc_p45_form_link = `${global.config.API_BASE_URL}${item.item_location}`;
            } else {
              formData.hmrc_p45_form_link = null;
            }
          } else {
            // Legacy path for backward compatibility
            formData.hmrc_p45_form_link = `${global.config.API_BASE_URL}/onbording_documents/${formData.user_id}/${formData.hmrc_p45_form}`;
          }
        } else {
          formData.hmrc_p45_form_link = null;
        }
      } else {
        formData.hmrc_p45_form_link = null;
      }
      if (!isFormExist && !formData) {
        await deleteFiles(req.files);
        return res.status(StatusCodes.OK).json({
          status: true,
          data: [],
        });
      }
    }

    if (isCheckListExist.prefix == "HSI") {
      const findUser = await User.findOne({
        where: { id: user_id, organization_id: req.user.organization_id },
        raw: true,
      });
      const whereObj: any = { status: HealthSafetyCategoryItemStatus.ACTIVE };
      if (findUser && findUser.branch_id) {
        whereObj.branch_id = findUser?.branch_id;
      }
      const findBranchWiseCategory = await HealthSafetyCategoryItem.findAll({
        where: whereObj,
        raw: true,
      });
      if (findBranchWiseCategory.length > 0) {
        const category_Ids = findBranchWiseCategory.map((category) => {
          return category.category_id;
        });
        const searchObj: any = {
          category_use: category_use.TRAINING,
          category_status: category_status.ACTIVE,
        };
        if (findUser?.branch_id) searchObj.branches = `${findUser?.branch_id}`;
        const categories: any = await fetchCategoriesRecursively(
          user_id,
          null,
          10,
          null,
          1,
          null,
          null,
          searchObj,
          null,
          category_Ids,
          req.user.organization_id,
        );
        formData = categories.data;
      }
    }

    if (isCheckListExist.prefix == "EC") {
      const getUser = await User.findOne({
        where: { id: user_id, organization_id: req.user.organization_id },
      });
      const getContract: any = await UserEmploymentContract.findOne({
        where: { user_id: user_id, contract_status: status.ACTIVE },
      });
      if (getContract && getUser && getContract.contract_with_sign) {
        // Check if contract_with_sign is an item_id (number) or legacy filename (string)        
        if (isNaN(Number(getContract.contract_with_sign))) {
          // Legacy path for backward compatibility          
          formData = {
            employment_contract: `${global.config.API_BASE_URL}onbording_documents/${getUser.id}/${getContract.contract_with_sign}`, is_confirm_sign: getContract.is_confirm_sign
          };
        } else {
          // It's an item_id, fetch from Item model         
          const item = await Item.findOne({ where: { id: getContract.contract_with_sign, item_status: item_status.ACTIVE } });
          if (item) {
            formData = { employment_contract: `${global.config.API_BASE_URL}${item.item_location}`, is_confirm_sign: getContract.is_confirm_sign };
          } else {
            formData = {};
          }
        }
      }
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      formDetail: formData || {},
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getHealthSafetyCheckList = async (req: any, res: Response) => {
  try {
    const getCheckList = await HealthSafety.findAll();

    if (getCheckList.length > 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        checklist: getCheckList || [],
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_DATA_NOT_FOUND"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const verifyUserCheckList = async (req: any, res: Response) => {
  try {
    const { verifiedCheckList = [], to_user_id } = req.body;

    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id, organization_id: req.user.organization_id },
      raw: true,
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const isPermitted = await permissionForAdmin(
      getUserDetail.web_user_active_role_id,
    );
    if (!isPermitted) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    // const findFormUser = await User.findOne({ where: { id: to_user_id } });

    // if (
    //   findFormUser &&
    //   findFormUser.user_status != user_status.COMPLETED &&
    //   findFormUser.user_status != user_status.VERIFIED
    // ) {
    //   return res.status(StatusCodes.EXPECTATION_FAILED).json({
    //     status: false,
    //     message: res.__("FAIL_USER_ONBORDING_NOT_COMPLTED"),
    //   });
    // }
    if (verifiedCheckList.length > 0) {
      const verifiedCheckListExist = await CheckList.findAll({
        where: {
          id: { [Op.in]: verifiedCheckList },
          type: Sequelize.literal(
            `(type & ${CHECKLISTTYPE.ADMIN}) = ${CHECKLISTTYPE.ADMIN}`,
          ),
          checklist_status: checklist_status.ACTIVE,
          checklist_type: checklist_type.JOINING,
        },
      });

      if (verifiedCheckListExist.length == 0) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("FAIL_CHECKLIST_NOT_FOUND") });
      }
      await UserCheckList.destroy({
        where: {
          to_user_id: to_user_id,
          from_user_id: { [Op.not]: to_user_id },
        },
      });

      _.forEach(verifiedCheckList, async (checklist: number) => {
        const findUserCheckList = await UserCheckList.findOne({
          where: {
            to_user_id: to_user_id,
            from_user_id: { [Op.not]: to_user_id },
            checklist_id: checklist,
          },
        });
        if (findUserCheckList) {
          await UserCheckList.setHeaders(req).update(
            { to_user_id: to_user_id, from_user_id: req.user.id },
            {
              where: {
                to_user_id: to_user_id,
                from_user_id: { [Op.not]: to_user_id },
                checklist_id: checklist,
              },
            },
          );
        } else {
          await UserCheckList.setHeaders(req).create({
            checklist_id: checklist,
            to_user_id: to_user_id,
            from_user_id: req.user.id,
            status: check_list_status.COMPLETED,
          } as any);
        }
      });
      await UserCheckList.setHeaders(req).update(
        {
          to_user_id: to_user_id,
          from_user_id: req.user.id,
          status: check_list_status.PENDING,
        },
        {
          where: {
            to_user_id: to_user_id,
            from_user_id: { [Op.not]: to_user_id },
            checklist_id: { [Op.not]: verifiedCheckList },
          },
        },
      );

      return res
        .status(StatusCodes.OK)
        .json({
          status: true,
          message: res.__("SUCCESS_ACCEPT_FORM_VERIFIED"),
        });
    } else {
      await UserCheckList.destroy({
        where: {
          to_user_id: to_user_id,
          from_user_id: { [Op.not]: to_user_id },
        },
      });
      return res
        .status(StatusCodes.OK)
        .json({
          status: true,
          message: res.__("SUCCESS_ACCEPT_FORM_VERIFIED"),
        });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const approveRejectForm = async (req: any, res: Response) => {
  try {
    const { approval_status, to_user_id } = req.body;

    const user_id = req.user.id;

    const findUserDetail: any = await User.findOne({
      attributes: ["id", "user_first_name", "user_email"],
      where: { id: to_user_id, organization_id: req.user.organization_id },
      raw: true,
    });

    const templateData: any = {
      name: findUserDetail.user_first_name,
      admin: req.user.user_first_name,
      ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
      LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
      ADDRESS: EMAIL_ADDRESS.ADDRESS,
      PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
      EMAIL: EMAIL_ADDRESS.EMAIL,
      ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
      smtpConfig: "INFO",
    };
    if (approval_status == true) {
      const totalCheckList: any = await CheckList.findAll({
        attributes: ["id", "checkList_name", "checklist_type", "order"],
        where: {
          type: Sequelize.literal(
            `(type & ${CHECKLISTTYPE.ADMIN}) = ${CHECKLISTTYPE.ADMIN}`,
          ),
          checklist_status: checklist_status.ACTIVE,
          checklist_type: checklist_type.JOINING,
        },
        raw: true,
      });
      const checkFormExist = await UserCheckList.findAll({
        attributes: ["id", "checklist_id", "status"],
        where: {
          to_user_id: to_user_id,
          from_user_id: { [Op.not]: to_user_id },
          status: check_list_status.COMPLETED,
        },
        raw: true,
      });

      if (checkFormExist.length != totalCheckList.length) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_PLEASE_VERIFY_ALL_CHECKLIST"),
        });
      }
      await User.setHeaders(req).update(
        {
          user_status: user_status.VERIFIED,
          updated_by: user_id,
          confirm_by: req.user.id,
          confirm_by_date: new Date(),
        } as any,
        { where: { id: to_user_id } },
      );
      templateData.email = findUserDetail.user_email;
      (templateData.mail_type = "form_approved"),
        await sendEmailNotification(templateData);
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FORM_VERIFIED"),
      });
    } else {
      const updateUserStatus = await User.setHeaders(req).update(
        {
          user_status: user_status.REJECTED,
          updated_by: user_id,
          confirm_by: req.user.id,
          confirm_by_date: new Date(),
        } as any,
        { where: { id: to_user_id } },
      );
      if (updateUserStatus.length > 0) {
        await removeFormData(to_user_id, req);
      }
      templateData.email = findUserDetail.user_email;
      (templateData.mail_type = "form_rejected"),
        await sendEmailNotification(templateData);
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_REJECT_FORM_VERIFICATION"),
      });
    }
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getverifiedUserCheckList = async (req: any, res: Response) => {
  try {
    const { to_user_id } = req.query;

    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id, organization_id: req.user.organization_id },
      raw: true,
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const isPermitted = await permissionForAdmin(
      getUserDetail.web_user_active_role_id,
    );
    if (!isPermitted) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    const findFormUser = await User.findOne({
      where: { id: to_user_id, organization_id: req.user.organization_id },
      raw: true,
    });

    if (
      findFormUser &&
      findFormUser.user_status != user_status.COMPLETED &&
      findFormUser.user_status != user_status.VERIFIED &&
      findFormUser.user_status != user_status.REJECTED
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_USER_ONBORDING_NOT_COMPLTED"),
      });
    }

    const getAdminCheckList = await CheckList.findAll({
      attributes: [
        "id",
        "checkList_name",
        "checklist_type",
        "order",
        [
          sequelize.literal(
            `IF((select status from nv_user_checklist where to_user_id = ${to_user_id} AND nv_user_checklist.checklist_id = CheckList.id AND from_user_id IS NOT NULL) IS NOT NULL, (select status from nv_user_checklist where to_user_id = ${to_user_id} AND  nv_user_checklist.checklist_id = CheckList.id AND from_user_id IS NOT NULL), 'pending')`,
          ),
          "status",
        ],
      ],
      where: {
        type: Sequelize.literal(
          `(type & ${CHECKLISTTYPE.ADMIN}) = ${CHECKLISTTYPE.ADMIN}`,
        ),
        checklist_status: checklist_status.ACTIVE,
        checklist_type: checklist_type.JOINING,
      },
    });

    if (getAdminCheckList.length == 0) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_CHECKLIST_NOT_FOUND") });
    }

    if (getAdminCheckList.length > 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: getAdminCheckList,
      });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("FAIL_DATA_NOT_FOUND") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const permissionForAdmin = async (role_id: any) => {
  try {
    const getRoleName: any = await Role.findOne({
      where: { id: role_id },
      attributes: ["role_name"],
    });
    const adminSideUser = [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.AREA_MANAGER,
      ROLE_CONSTANT.BRANCH_MANAGER,
      ROLE_CONSTANT.HOTEL_MANAGER,
    ];
    if (adminSideUser.includes(getRoleName?.role_name)) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
    return false;
  }
};

const removeFormData = async (user_id: any, req: any) => {
  try {
    let rightToWorkRemoved: any;
    let starterFormRemoved: any;
    let hrmcFormRemoved: any;
    let healthSafetyFormRemoved: any;

    const getRightToWork = await RightToWorkCheckList.findOne({
      where: { user_id: user_id },
    });
    const destination_path = path.resolve(
      __dirname,
      "..",
      "uploads",
      "onbording_documents",
      `${user_id}`,
    );
    if (getRightToWork) {
      rightToWorkRemoved = await RightToWorkCheckList.setHeaders(req).update(
        {
          passport_front: null,
          passport_back: null,
          cv: null,
          share_code: null,
          brp_front: null,
          brp_back: null,
          p45: null,
          ni_letter: null,
          student_letter: null,
          statements_dl_utility: null,
          has_right_to_work_in_uk: null,
          is_uk_citizen: null,
          photoID: null,
          is_confirm_upload: null,
          status: status.INACTIVE,
          form_name: null,
        } as any,
        { where: { checklist_id: 1, user_id: user_id } },
      );
    }

    const getNewStarter = await StarterForm.findOne({
      where: { user_id: user_id },
    });
    if (getNewStarter) {
      starterFormRemoved = await StarterForm.setHeaders(req).update(
        {
          medical_disability: 0,
          medical_disability_detail: null,
          kin1_name: null,
          kin1_relation: null,
          kin1_address: null,
          kin1_mobile_number: null,
          kin2_name: null,
          kin2_relation: null,
          kin2_address: null,
          kin2_mobile_number: null,
          professional1_name_contact: null,
          professional1_role_description: null,
          professional2_name_contact: null,
          professional2_role_description: null,
          passport_no: null,
          issued_date: null,
          permit_type: null,
          validity: null,
          bank_account_name: null,
          bank_account_number: null,
          bank_sort_code: null,
          bank_society_name: null,
          bank_address: null,
          professional1_start_date: null,
          professional1_end_date: null,
          professional2_start_date: null,
          professional2_end_date: null,
          has_student_or_pg_loan: null,
          has_p45_form: null,
          hmrc_p45_form: null,
          status: status.INACTIVE,
        } as any,
        { where: { user_id: user_id } },
      );
    }

    const getHrmcForm = await HrmcForm.findOne({ where: { user_id: user_id } });
    if (getHrmcForm) {
      hrmcFormRemoved = await HrmcForm.setHeaders(req).update(
        {
          insurance_number: null,
          postgraduate_loan: null,
          statement_apply: null,
          is_current_information: null,
          another_job: null,
          private_pension: null,
          payment_from: null,
          load_guidance: null,
          statementA: null,
          statementB: null,
          statementC: null,
          status: status.INACTIVE,
        } as any,
        { where: { user_id: user_id } },
      );
    }

    const getHealthSafetyForm: any = await HealthSafetyForm.findOne({
      where: { user_id: user_id, status: status.ACTIVE },
    });
    if (getHealthSafetyForm) {
      healthSafetyFormRemoved = await HealthSafetyForm.setHeaders(req).update(
        { status: status.INACTIVE },
        { where: { user_id: user_id } },
      );
    }

    const removeEmploymentContract = await User.setHeaders(req).update(
      { employment_contract: null } as any,
      { where: { id: user_id } },
    );

    await UserEmploymentContract.setHeaders(req).update(
      { contract_with_sign: null, contract_status: status.INACTIVE } as any,
      { where: { user_id: user_id } },
    );

    if (
      rightToWorkRemoved?.length > 0 &&
      starterFormRemoved?.length > 0 &&
      hrmcFormRemoved?.length > 0 &&
      healthSafetyFormRemoved?.length > 0 &&
      removeEmploymentContract.length > 0
    ) {
      HealthSafetyRelation.destroy({
        where: { health_safety_form_id: getHealthSafetyForm.id },
      });
      await Activity.create({
        activity_table: "Health Safety",
        reference_id: getHealthSafetyForm.id,
        activity_type: activity_type.SUCCESS,
        activity_action: activity_action.DELETED,
        ip_address: req.headers?.["ip-address"],
        userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
        address: req.headers?.["address"],
        location: req.headers?.["location"],
        organization_id: req.user.organization_id
          ? req.user.organization_id
          : null,
        created_by: user_id,
        updated_by: user_id,
      } as any);
      await UserCheckList.setHeaders(req).update(
        { status: check_list_status.PENDING },
        { where: { to_user_id: user_id, from_user_id: user_id } },
      );
      await UserCheckList.destroy({
        where: { to_user_id: user_id, from_user_id: { [Op.not]: user_id } },
      });
      deleteFolderRecursive(destination_path)
        .then(() => {
          console.log(`Folder ${destination_path} deleted successfully.`);
        })
        .catch((error: any) => {
          console.error(`Error deleting folder ${destination_path}:`, error);
        });
    }
  } catch (error) {
    console.log("error", error);
    return false;
  }
};

// const formProgress = async (req: any, res: Response) => {
//   try {

//   } catch (error) {
//     console.log("error", error);
//     return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
//       status: false,
//       message: res.__("SOMETHING_WENT_WRONG"),
//       data: error,
//     });
//   }
// };

async function deleteFolderRecursive(folderPath: any) {
  if (fs.existsSync(folderPath)) {
    if (fs.lstatSync(folderPath).isDirectory()) {
      const files = fs.readdirSync(folderPath);
      for (const file of files) {
        const currentPath = path.join(folderPath, file);
        if (fs.lstatSync(currentPath).isDirectory()) {
          await deleteFolderRecursive(currentPath);
        } else {
          fs.unlinkSync(currentPath);
        }
      }
      fs.rmdirSync(folderPath);
    } else {
      fs.unlinkSync(folderPath);
    }
  }
}

const getRightToWorkFormData = async (req: any, res: Response) => {
  try {
    const getFormDetail = await RightToWorkFormData.findAll({
      include: [
        {
          model: RightToWorkFormOption,
          as: "right_to_work_option",
          attributes: ["id", "description", "order", "separator"],
          order: [["order", "ASC"]],
        },
      ],
    });
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getFormDetail || [],
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const downloadRightTOWorkFiles = async (req: any, res: Response) => {
  try {
    const user_id = req.query.user_id ? req.query.user_id : req.user.id;

    const findUserDetail = await User.findOne({
      where: {
        id: user_id,
        organization_id: req.user.organization_id,
        user_status: {
          [Op.notIn]: [user_status.DELETED, user_status.CANCELLED],
        },
      },
      raw: true,
    });
    if (!findUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const findRightToWorkFiles: any = await RightToWorkCheckList.findOne({
      attributes: [
        "passport_front",
        "passport_back",
        "share_code",
        "brp_front",
        "brp_back",
        "p45",
        "ni_letter",
        "student_letter",
        "statements_dl_utility",
        "cv",
        "photoID",
      ],
      where: { checklist_id: 1, user_id: user_id },
      raw: true,
    });
    if (!findRightToWorkFiles) {
      return res.status(200).send({
        status: false,
        message: res.__("FILE_NOT_EXIST"),
      });
    }
    const files: any = Object.values(findRightToWorkFiles);
    const zip = new JSZip();
    const folderPath = path.resolve(
      __dirname,
      "..",
      "uploads",
      "onbording_documents",
      `${user_id}`,
    );
    if (!fs.existsSync(folderPath)) {
      return res.status(200).send({
        status: false,
        message: res.__("FILE_NOT_EXIST"),
      });
    }
    for (const i in files) {
      if (files[i] && files[i] != "") {
        const folderPath = path.resolve(
          __dirname,
          "..",
          "uploads",
          "onbording_documents",
          `${user_id}`,
        );
        if (fs.existsSync(folderPath)) {
          const filePath = path.join(folderPath, files[i]);
          if (fs.existsSync(filePath)) {
            zip.file(files[i], fs.readFileSync(filePath));
          }
        }
      }
    }
    const employeeName = [];

    if (findUserDetail?.user_first_name) {
      employeeName.push(findUserDetail.user_first_name.trim());
    }
    if (findUserDetail?.user_middle_name) {
      employeeName.push(findUserDetail.user_middle_name.trim());
    }
    if (findUserDetail?.user_last_name) {
      employeeName.push(findUserDetail.user_last_name.trim());
    }
    const zipFileName = `right_to_work_${employeeName.join("_")}_${moment().format("DD-MM-YYYY")}.zip`;
    zip
      .generateAsync({ type: "nodebuffer" })
      .then((buffer: any) => {
        // Send zip as a download
        res.setHeader("Content-Type", "application/octet-stream");
        res.setHeader("Download-File", zipFileName);
        console.log("Download successfully :", zipFileName);
        return res.end(buffer);
      })
      .catch((err: any) => {
        console.log("Download error :", err);
        return res
          .status(200)
          .send({ status: false, message: "Unable to download file" });
      });
    // return res.status(200).send()
  } catch (e: any) {
    console.log(e);
    return res
      .status(400)
      .json({
        status: false,
        message: res.__("SOMETHING_WENT_WRONG"),
        data: e,
      });
  }
};

const getHealthSafetyProgress = async (req: any, res: Response) => {
  try {
    const { user_id, health_safety_category_id } = req.query;
    const findUserDetail = await User.findOne({
      where: { id: user_id, organization_id: req.user.organization_id },
      raw: true,
    });

    if (findUserDetail && findUserDetail.branch_id) {
      const whereObj: any = { status: status.ACTIVE };
      if (health_safety_category_id) {
        whereObj.id = health_safety_category_id;
      }
      const getBranchWiseCategory: any = await HealthSafetyCategory.findAll({
        where: whereObj,
        raw: true,
        nest: true,
      });

      for (const category of getBranchWiseCategory) {
        const getHealthSafetyPlaylist: any = await HealthSafetyPlaylist.findOne(
          {
            include: [
              {
                model: Playlist,
                as: "health_safety_playlist",
                attributes: [
                  "id",
                  [
                    sequelize.literal(
                      `IF((playlist_image IS not NULL),CONCAT('${global.config.API_BASE_URL}media/',playlist_image),'')`,
                    ),
                    "playlist_image_link",
                  ],
                  "playlist_image",
                  "playlist_name",
                  "playlist_description",
                  "playlist_status",
                  [
                    sequelize.literal(
                      `(
                    SELECT (CASE
                    WHEN (ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${user_id})))*100)/COUNT(*)) = 100) THEN 'completed'
                    WHEN (ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${user_id})))*100)/COUNT(*)) = 0) THEN 'pending'
                    ELSE 'ongoing'
                END)
                FROM nv_playlist_media
                    WHERE playlist_media_status = 'active' 
                        AND playlist_id = health_safety_playlist.id
                    )`,
                    ),
                    "playlist_track_status",
                  ],
                  [
                    sequelize.literal(
                      `(
                    SELECT
                      ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${user_id})))*100)/COUNT(*))
                    FROM nv_playlist_media
                    WHERE playlist_media_status = 'active' 
                        AND playlist_id = health_safety_playlist.id
                )`,
                    ),
                    "playlist_track_percentage",
                  ],
                ],
              },
            ],
            where: {
              health_safety_category_id: category.id,
              branch_id: findUserDetail.branch_id,
              status: status.ACTIVE,
            },
          },
        );
        category.checked_playlist =
          getHealthSafetyPlaylist &&
            getHealthSafetyPlaylist?.health_safety_playlist
            ? getHealthSafetyPlaylist?.health_safety_playlist
            : {};
      }

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: getBranchWiseCategory || [],
      });
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("ERROR_USER_NOT_FOUND"),
        data: [],
      });
    }
  } catch (e: any) {
    console.log(e);
    return res
      .status(400)
      .json({
        status: false,
        message: res.__("SOMETHING_WENT_WRONG"),
        data: e,
      });
  }
};

const regenerateEmploymentContract = async (req: any, res: Response) => {
  try {
    const { user_id } = req.params;
    const { notify } = req.query;
    const isUpdated = await regenerateEmploymentContractFuncation(
      user_id,
      req,
      "true",
      false,
    );
    if (isUpdated) {
      setTimeout(() => {
        return res.status(StatusCodes.OK).json({
          status: true,
          message: notify
            ? notify == "only"
              ? res.__("SUCCESS_SEND_REMINDER")
              : notify == "true"
                ? res.__("SUCCESS_SEND_REMINDER_REGENERATE")
                : res.__("CONTRACT_GENERATED_SUCCESSFULLY")
            : res.__("CONTRACT_GENERATED_SUCCESSFULLY"),
        });
      }, 2000);
    } else {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("CONTRACT_NOT_FOUND"),
      });
    }
  } catch (e: any) {
    console.log(e);
    return res
      .status(400)
      .json({
        status: false,
        message: res.__("SOMETHING_WENT_WRONG"),
        data: e,
      });
  }
};

const userOnboardingReset = async (req: any, res: Response) => {
  try {
    const { checklist_ids, user_id }: any = req.body;
    const findUserDetail: any = await User.findOne({
      where: {
        id: user_id,
        organization_id: req.user.organization_id,
        user_status: {
          [Op.in]: [
            user_status.COMPLETED,
            user_status.ONGOING,
            user_status.REJECTED,
            user_status.VERIFIED,
          ],
        },
      },
      raw: true,
    });
    if (!findUserDetail) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_PLEASE_VERIFY_ALL_CHECKLIST"),
      });
    }
    if (checklist_ids.length > 0) {
      for (const checklist_id of checklist_ids) {
        const findCheckList: any = await CheckList.findOne({
          where: { id: checklist_id },
        });

        if (findCheckList.prefix == "RTWC") {
          const getRightToWork = await RightToWorkCheckList.findOne({
            where: { user_id: user_id, checklist_id: findCheckList.id },
          });
          if (getRightToWork) {
            const rightToWorkRemoved = await RightToWorkCheckList.setHeaders(
              req,
            ).update(
              {
                passport_front: null,
                passport_back: null,
                cv: null,
                share_code: null,
                brp_front: null,
                brp_back: null,
                p45: null,
                ni_letter: null,
                student_letter: null,
                statements_dl_utility: null,
                has_right_to_work_in_uk: null,
                is_uk_citizen: null,
                photoID: null,
                is_confirm_upload: null,
                status: status.INACTIVE,
                form_name: null,
              } as any,
              { where: { checklist_id: findCheckList.id, user_id: user_id } },
            );

            if (rightToWorkRemoved.length > 0) {
              await UserCheckList.setHeaders(req).update(
                { status: check_list_status.PENDING, is_last_rejected: true },
                {
                  where: {
                    to_user_id: user_id,
                    from_user_id: user_id,
                    checklist_id: findCheckList.id,
                  },
                },
              );
              const destination_path = path.resolve(
                __dirname,
                "..",
                "uploads",
                "onbording_documents",
                `${user_id}`,
              );
              // Mapping fields to file names
              const filesToDelete = {
                passport_front: getRightToWork.passport_front,
                passport_back: getRightToWork.passport_back,
                cv: getRightToWork.cv,
                share_code: getRightToWork.share_code,
                brp_front: getRightToWork.brp_front,
                brp_back: getRightToWork.brp_back,
                p45: getRightToWork.p45,
                ni_letter: getRightToWork.ni_letter,
                student_letter: getRightToWork.student_letter,
                statements_dl_utility: getRightToWork.statements_dl_utility,
                photoID: getRightToWork.photoID,
              };
              // Iterate over the filesToDelete object and delete corresponding files
              // Iterate over the filesToDelete object and delete corresponding files
              for (const [fileName] of Object.entries(filesToDelete)) {
                if (fileName) {
                  // Ensure fileName is not null
                  const filePath = path.join(destination_path, fileName);
                  if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath); // Delete the file
                  }
                }
              }
            }
          }
        } else if (findCheckList.prefix == "NS&HMRCF") {
          const getNewStarter: any = await StarterForm.findOne({
            where: { user_id: user_id, checklist_id: findCheckList.id },
          });
          let hrmcFormRemoved: any;
          let starterFormRemoved: any;
          if (getNewStarter) {
            starterFormRemoved = await StarterForm.setHeaders(req).update(
              {
                medical_disability: 0,
                medical_disability_detail: null,
                kin1_name: null,
                kin1_relation: null,
                kin1_address: null,
                kin1_mobile_number: null,
                kin2_name: null,
                kin2_relation: null,
                kin2_address: null,
                kin2_mobile_number: null,
                professional1_name_contact: null,
                professional1_role_description: null,
                professional2_name_contact: null,
                professional2_role_description: null,
                passport_no: null,
                issued_date: null,
                permit_type: null,
                permit_type_other: null,
                validity: null,
                bank_account_name: null,
                bank_account_number: null,
                bank_sort_code: null,
                bank_society_name: null,
                bank_address: null,
                professional1_start_date: null,
                professional1_end_date: null,
                professional2_start_date: null,
                professional2_end_date: null,
                has_student_or_pg_loan: null,
                has_p45_form: null,
                hmrc_p45_form: null,
                status: status.INACTIVE,
              } as any,
              { where: { user_id: user_id } },
            );
          }
          const getHrmcForm = await HrmcForm.findOne({
            where: { user_id: user_id, checklist_id: findCheckList.id },
          });
          if (getHrmcForm) {
            hrmcFormRemoved = await HrmcForm.setHeaders(req).update(
              {
                insurance_number: null,
                postgraduate_loan: null,
                statement_apply: null,
                is_current_information: null,
                another_job: null,
                private_pension: null,
                payment_from: null,
                load_guidance: null,
                statementA: null,
                statementB: null,
                statementC: null,
                status: status.INACTIVE,
              } as any,
              { where: { user_id: user_id } },
            );
          }
          if (hrmcFormRemoved?.length > 0 && starterFormRemoved?.length > 0) {
            await UserCheckList.setHeaders(req).update(
              { status: check_list_status.PENDING, is_last_rejected: true },
              {
                where: {
                  to_user_id: user_id,
                  from_user_id: user_id,
                  checklist_id: findCheckList.id,
                },
              },
            );
            const destination_path = path.resolve(
              __dirname,
              "..",
              "uploads",
              "onbording_documents",
              `${user_id}`,
            );
            if (getNewStarter?.hmrc_p45_form) {
              const filePath = path.join(
                destination_path,
                getNewStarter?.hmrc_p45_form,
              );
              if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath); // Delete the file
              }
            }
          }
        } else if (findCheckList.prefix == "EC") {
          const updateContract = await UserMeta.update(
            {
              user_id: findUserDetail.id,
              general_template: null,
              department_template: null,
              other_template: null,
              expire_date: null,
              start_date: null,
              other: null,
              isDraft: false,
              expire_duration: null,
              contract_type: null,
              wages_hours: null,
              leave_policy_id: null,
              tips_grade: null,
              fixed_types: null,
              probation_length: null,
              updated_by: req.user.id,
            } as any,
            { where: { user_id: findUserDetail.id } },
          );
          if (updateContract.length > 0) {
            await regenerateEmploymentContractFuncation(
              user_id,
              req,
              false,
              true,
            );
          }
        }
        // Update user status and destroy non-matching checklist entries for this checklist_id
        await User.setHeaders(req).update(
          { user_status: user_status.ONGOING, updated_by: user_id } as any,
          { where: { id: user_id } },
        );
        await UserCheckList.destroy({
          where: {
            to_user_id: user_id,
            from_user_id: { [Op.not]: user_id },
            checklist_id: checklist_id,
          },
        });
      }
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_USER_ONBOARDING_RESET"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

export default {
  createForm,
  requestForm,
  updateForm,
  deleteUploadedFiles,
  getCheckList,
  getUserFormDetail,
  getHealthSafetyCheckList,
  verifyUserCheckList,
  getverifiedUserCheckList,
  approveRejectForm,
  getRightToWorkFormData,
  downloadRightTOWorkFiles,
  getHealthSafetyProgress,
  regenerateEmploymentContract,
  userOnboardingReset,
};
