import { Router } from "express";
import expenseController from "../../controller/expense.controller";
import expenseValidator from "../../validators/expense.validator";
const router: Router = Router();


router.post("/add", expenseValidator.addExpense(), expenseController.addExpenseDetail);

router.put("/update/:expense_detail_id", expenseValidator.updateExpense(), expenseController.updateExpenseDetail);

router.get("/get-expense-by-id/:expense_detail_id", expenseController.getExpenseById);

router.get("/get-expense-list", expenseController.getExpenseList);

router.delete("/delete/:expense_detail_id", expenseController.deleteExpenseById);

router.get("/get-expense-request-list", expenseController.getExpenseRequestList);

router.get("/get-expense-request-by-id/:expense_request_id", expenseController.getExpenseRequestById);

router.post("/approve-reject-request", expenseController.approveRejectRequest);

router.get("/get-expense-report", expenseController.getExpenseReport);

router.post("/check-expense-exist", expenseController.checkExpenseExist);

router.get("/get-expense-activity/:expense_id", expenseController.getExpenseActivity);

export default router;
