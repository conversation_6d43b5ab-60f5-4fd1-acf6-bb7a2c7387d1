import { Router } from "express";
import userVerificationController from "../../controller/userVerification.controller";
const router: Router = Router();
import { multerS3 } from "../../helper/upload.service";
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";

const multerUpload = multerS3(
  process.env.NODE_ENV || "development",
  FILE_UPLOAD_CONSTANT.USER_VERIFICATION_DOC_PATH.folder,
);

router.post(
  "/user-verification",
  multerUpload.upload("user_verification_doc"),
  userVerificationController.userVerification,
);

export { router as default };
