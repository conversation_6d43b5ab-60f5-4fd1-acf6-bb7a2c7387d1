"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { RightToWorkFormData } from "./RightToWorkFormData";

interface rightToWorkFormOptionAttributes {
  id: number;
  right_to_checklist_data_id: number;
  description: string;
  order: number;
  separator: boolean;
  created_by: number;
  updated_by: number;
}

export class RightToWorkFormOption
  extends Model<rightToWorkFormOptionAttributes, never>
  implements rightToWorkFormOptionAttributes {
  id!: number;
  right_to_checklist_data_id!: number;
  description!: string;
  order!: number;
  separator!: boolean;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}


export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}


RightToWorkFormOption.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    right_to_checklist_data_id: {
      type: DataTypes.INTEGER,
    },
    description: {
      type: DataTypes.TEXT,
    },
    order: {
      type: DataTypes.INTEGER,
    },
    separator: {
      type: DataTypes.BOOLEAN,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_right_work_form_option",
    modelName: "RightToWorkFormOption",
  },
);

RightToWorkFormOption.belongsTo(RightToWorkFormData, { foreignKey: "right_to_checklist_data_id", as: "right_to_work_data" });
RightToWorkFormData.hasMany(RightToWorkFormOption, { foreignKey: "right_to_checklist_data_id", as: "right_to_work_option" });

RightToWorkFormOption.addHook(
  "afterUpdate",
  async (rightToWorkFormOption: any) => {
    await addActivity(
      "rightToWorkFormOption",
      "updated",
      rightToWorkFormOption,
    );
  },
);

RightToWorkFormOption.addHook(
  "afterCreate",
  async (rightToWorkFormOption: RightToWorkFormOption) => {
    await addActivity(
      "RightToWorkFormOption",
      "created",
      rightToWorkFormOption,
    );
  },
);

RightToWorkFormOption.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
