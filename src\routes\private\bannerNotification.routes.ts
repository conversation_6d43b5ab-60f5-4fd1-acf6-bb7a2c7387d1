import { Router } from "express";
import bannerNotification from "../../controller/bannerNotification.controller"
const router: Router = Router();

// add banner config
router.post("/create-banner-config", bannerNotification.createBannerConfig);

// update banner config
router.put("/update-config/:id", bannerNotification.updateBannerConfig);

// get banner config
router.get("/get-banners-list", bannerNotification.getAllBannerConfigs);

// get banner config
router.get("/get-banner-config/:id", bannerNotification.getBannerConfigById);

// delete  banner config
router.delete("/delete-banner/:id", bannerNotification.deleteBannerConfig);

// get bank config by key
router.get("/get-banner-config-by-key/:key", bannerNotification.getBannerConfigByKey);

/** get banner notifications */
router.get("/get-banner-notifications/:user_id", bannerNotification.getBannerNotifications);

/** mark as read notification */
router.put("/mark-as-read/:id", bannerNotification.markAsReadNotification);
export default router;
