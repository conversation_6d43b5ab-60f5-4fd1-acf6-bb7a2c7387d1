
import { StatusCodes } from "http-status-codes";
import { Request, Response } from "express";
import { LeaveAccuralPolicy, status as leave_accural_policy } from "../models/LeaveAccuralPolicy";
import { LeaveTypeModel, status } from "../models/LeaveType";
import { LeaveApplicationRulesPolicy } from "../models/LeaveApplicationRulesPolicy";
import { LeaveRestrictionPolicy } from "../models/LeaveRestrictionPolicy";
import { LeaveHolidayWeekendPolicy } from "../models/LeaveHolidayWeekendPolicy";
import { LeaveApprovalPolicy } from "../models/LeaveApprovalPolicy";
import { approval_type, LeaveApprovalMetaPolicy } from "../models/LeaveApprovalMetaPolicy";
import { User } from "../models/User";
import { Op } from "sequelize";
import { sequelize } from "../models";
import { PolicySetting } from "../models/PolicySetting";
import { PolicySettingMeta } from "../models/PolicySettingMeta";
import { user_leave_policy_status, UserLeavePolicy } from "../models/UserLeavePolicy";
import moment from "moment";
import { getGeneralSettingObj, handleLeaveAccrual } from "../helper/common";
import { UserMeta } from "../models/UserMeta";
import { UserLeavePolicyHistory } from "../models/UserLeavePolicyHistory";
import { formatNumber } from "../helper/utils";



const createLeavePolicy = async (req: Request, res: Response) => {
    try {
        const { leave_type_id, leave_accural_form = {}, leave_application_rules_form = {}, leave_restricition_form = {}, holiday_weekend_form = {}, leave_approval_form = {} } = req.body;

        // Check if the leave type exists
        const checkLeaveTypeExist = await LeaveTypeModel.findOne({ attributes: ['has_annual_leave'], where: { id: leave_type_id, organization_id: req.user.organization_id, status: status.ACTIVE } });

        if (!checkLeaveTypeExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("LEAVE_TYPE_NOT_FOUND"),
            });
        }

        // Create Leave Accrual Policy first if it has values
        let leaveAccrualPolicy: any;
        if (Object.keys(leave_accural_form).length > 0) {
            const { encashment_leavals, ...newObj } = leave_accural_form;
            const leave_policy_name = leave_accural_form?.leave_policy_name;
            const stop_policy_accural_timewise_value = leave_accural_form?.stop_policy_accural_timewise_value;
            const has_leave_policy_default = leave_accural_form?.has_leave_policy_default;
            const generalSettings = await getGeneralSettingObj(req.user.organization_id)
            // Ensure that leave_policy_name does not exist for the same leave_type_id
            const existingPolicy = await LeaveAccuralPolicy.findOne({
                attributes: ['id'],
                where: {
                    leave_policy_name,
                    leave_type_id,
                    status: leave_accural_policy.ACTIVE
                }, raw: true
            });

            if (existingPolicy) {
                return res.status(StatusCodes.EXPECTATION_FAILED).json({
                    status: false,
                    message: res.__("LEAVE_POLICY_NAME_ALREADY_EXIST"),
                });
            }
            const leave_balance_based_on_emp_contract = checkLeaveTypeExist.has_annual_leave ? leave_accural_form?.leave_balance_based_on_emp_contract : false;
            // If has_leave_policy_default is true, remove it from any other policy of the same leave_type_id
            if (has_leave_policy_default) {
                await LeaveAccuralPolicy.update(
                    { has_leave_policy_default: false },
                    {
                        where: {
                            leave_type_id,
                            has_leave_policy_default: true,

                        }
                    }
                );
            }
            if (stop_policy_accural_timewise_value) {
                try {
                    const parsedValue = JSON.parse(stop_policy_accural_timewise_value);
                    const updated = parsedValue.map((entry: any) => {
                        const days = parseFloat(entry.days);
                        if (generalSettings?.leave_period_type == 'day') {
                            return {
                                ...entry,
                                day_value: entry.days,
                                hour_value: formatNumber(days * generalSettings?.working_hours_per_day)
                            };
                        } else {
                            return {
                                ...entry,
                                hour_value: entry.days,
                                day_value: formatNumber(days / generalSettings?.working_hours_per_day)
                            };
                        }
                    });
                    newObj.stop_policy_accural_timewise_value = JSON.stringify(updated);
                } catch (err) {
                    console.error('Invalid stop_policy_accural_timewise_value format:', err);
                }
            }

            leaveAccrualPolicy = await LeaveAccuralPolicy.create({ ...newObj, leave_type_id, leave_balance_based_on_emp_contract });
            if (Array.isArray(encashment_leavals) && encashment_leavals.length > 0) {
                for (const meta of encashment_leavals) {
                    if (meta?.user_id.length > 0) {
                        for (const user_id of meta.user_id) {
                            await LeaveApprovalMetaPolicy.create({ user_id: user_id, leave_policy_leval: meta.leave_policy_leval, auto_approve_or_skip: meta.auto_approve_or_skip, auto_approve_or_skip_days: meta.auto_approve_or_skip_days, leave_accural_policy_id: leaveAccrualPolicy.id, approval_type: approval_type.ENCASEMENT } as any);
                        }
                    }
                }
            }
        }

        const leave_accural_policy_id = leaveAccrualPolicy ? leaveAccrualPolicy.id : null;

        // Create other related policies only if they have values
        if (Object.keys(leave_application_rules_form).length > 0 && leave_accural_policy_id) {
            await LeaveApplicationRulesPolicy.create({ ...leave_application_rules_form, leave_accural_policy_id });
        }

        if (Object.keys(leave_restricition_form).length > 0 && leave_accural_policy_id) {
            await LeaveRestrictionPolicy.create({ ...leave_restricition_form, leave_accural_policy_id });
        }

        if (Object.keys(holiday_weekend_form).length > 0 && leave_accural_policy_id) {
            await LeaveHolidayWeekendPolicy.create({ ...holiday_weekend_form, leave_accural_policy_id });
        }

        if (Object.keys(leave_approval_form).length > 0 && leave_accural_policy_id) {
            const { leave_approval_meta_form, ...newObj } = leave_approval_form;
            await LeaveApprovalPolicy.create({ ...newObj, leave_accural_policy_id });
            if (Array.isArray(leave_approval_meta_form) && leave_approval_meta_form.length > 0) {
                for (const meta of leave_approval_meta_form) {
                    if (meta?.user_id.length > 0) {
                        for (const user_id of meta.user_id) {
                            await LeaveApprovalMetaPolicy.create({ user_id: user_id, leave_policy_leval: meta.leave_policy_leval, leave_accural_policy_id, approval_type: approval_type.LEAVE, auto_approve_or_skip: meta?.auto_approve_or_skip, auto_approve_or_skip_days: meta?.auto_approve_or_skip_days } as any);
                        }
                    }
                }
            }
        }

        return res.status(StatusCodes.CREATED).json({
            status: true,
            message: res.__("LEAVE_POLICY_ADDED_SUCCESSFULLY"),
        });
    } catch (error: any) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const updateLeavePolicy = async (req: Request, res: Response) => {
    try {
        const { leave_type_id, leave_accural_form = {}, leave_application_rules_form = {}, leave_restricition_form = {}, holiday_weekend_form = {}, leave_approval_form = {} } = req.body;
        const { leave_accural_policy_id }: any = req.params;
        // Check if the leave type exists
        const checkLeaveTypeExist = await LeaveTypeModel.findOne({ attributes: ['has_annual_leave', 'id'], where: { id: leave_type_id, organization_id: req.user.organization_id, status: status.ACTIVE }, raw: true });

        if (!checkLeaveTypeExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("LEAVE_TYPE_NOT_FOUND"),
            });
        }

        const checkLeavePolicyExist: any = await LeaveAccuralPolicy.findOne({ attributes: ['id'], where: { id: leave_accural_policy_id, leave_type_id: checkLeaveTypeExist.id, status: leave_accural_policy.ACTIVE }, raw: true });
        if (!checkLeavePolicyExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("LEAVE_POLICY_NOT_FOUND"),
            });
        }

        // Update Leave Accrual Policy if it has values
        if (Object.keys(leave_accural_form).length > 0) {
            const { encashment_leavals, ...newObj } = leave_accural_form;
            const leave_policy_name = leave_accural_form?.leave_policy_name;
            const has_leave_policy_default = leave_accural_form?.has_leave_policy_default;
            const stop_policy_accural_timewise_value = leave_accural_form?.stop_policy_accural_timewise_value;
            const generalSettings = await getGeneralSettingObj(req.user.organization_id)
            // Ensure that leave_policy_name does not exist for the same leave_type_id
            const existingPolicy = await LeaveAccuralPolicy.findOne({
                where: {
                    leave_policy_name,
                    leave_type_id,
                    status: leave_accural_policy.ACTIVE
                }, raw: true
            });
            if (existingPolicy && existingPolicy.id != leave_accural_policy_id) {
                return res.status(StatusCodes.EXPECTATION_FAILED).json({
                    status: false,
                    message: res.__("LEAVE_POLICY_NAME_ALREADY_EXIST"),
                });
            }
            const leave_balance_based_on_emp_contract = checkLeaveTypeExist.has_annual_leave ? leave_accural_form?.leave_balance_based_on_emp_contract : false;
            // If has_leave_policy_default is true, remove it from any other policy of the same leave_type_id
            if (has_leave_policy_default) {
                await LeaveAccuralPolicy.update(
                    { has_leave_policy_default: false },
                    {
                        where: {
                            leave_type_id,
                            has_leave_policy_default: true,
                        }
                    }
                );
            }
            if (stop_policy_accural_timewise_value) {
                try {
                    const parsedValue = JSON.parse(stop_policy_accural_timewise_value);
                    const updated = parsedValue.map((entry: any) => {
                        const days = parseFloat(entry.days);
                        if (generalSettings?.leave_period_type == 'day') {
                            return {
                                ...entry,
                                day_value: entry.days,
                                hour_value: formatNumber(days * generalSettings?.working_hours_per_day)
                            };
                        } else {
                            return {
                                ...entry,
                                hour_value: entry.days,
                                day_value: formatNumber(days / generalSettings?.working_hours_per_day)
                            };
                        }
                    });
                    newObj.stop_policy_accural_timewise_value = JSON.stringify(updated);
                } catch (err) {
                    console.error('Invalid stop_policy_accural_timewise_value format:', err);
                }
            }

            await LeaveAccuralPolicy.update({ ...newObj, leave_type_id, leave_balance_based_on_emp_contract }, { where: { id: leave_accural_policy_id } });
            const findLeavePolicyUser = await UserLeavePolicy.findAll({
                where: {
                    leave_accural_policy_id: leave_accural_policy_id, user_leave_policy_status: user_leave_policy_status.ACTIVE
                },
            })

            if (findLeavePolicyUser.length > 0) {
                for (const userPolicy of findLeavePolicyUser) {
                    const user = await User.findOne({ where: { id: userPolicy.user_id } })
                    if (user) {
                        const findLeaveYear = leave_accural_form.leave_calender_year_start_from;

                        const findLeaveType = leave_accural_form.stop_policy_accural_timewise_type || 'yearly';
                        const findLeaveObj = leave_accural_form.stop_policy_accural_timewise_value ? JSON.parse(leave_accural_form.stop_policy_accural_timewise_value)
                            : [];
                        const findLeaveEnd = leave_accural_form.leave_policy_end_date;
                        const findUserMeta = await UserMeta.findOne({ where: { user_id: user.id } })
                        const leaveBalanceBasedOnEmpContract = leave_accural_form.leave_balance_based_on_emp_contract

                        // Calculate the probation end date
                        const probationEndDate = moment(user?.user_joining_date).clone().add(findUserMeta?.probation_length, "days");
                        const isUserOnProbation = moment().isSameOrBefore(moment(probationEndDate), 'day');

                        let baseDate;
                        if (existingPolicy?.stop_policy_accural_timewise_type != leave_accural_form.stop_policy_accural_timewise_type) {
                            await UserLeavePolicyHistory.destroy({ where: { leave_user_policy_id: userPolicy.id } })
                        }


                        // Determine base date based on effective_from_type
                        if (leave_accural_form.effective_from_type == 'date_of_joining') {
                            baseDate = moment(user?.user_joining_date);
                        } else if (leave_accural_form.effective_from_type == 'after_probation_end' && isUserOnProbation) {
                            baseDate = moment(probationEndDate);
                        }

                        // Calculate the effective date
                        const effectiveDate = moment(baseDate);
                        if (leave_accural_form.effective_after_type === 'days') {
                            effectiveDate.add(leave_accural_form.effective_after_count, 'days');
                        } else if (leave_accural_form.effective_after_type === 'months') {
                            effectiveDate.add(leave_accural_form.effective_after_count, 'months');
                        }


                        // Store user I
                        await UserLeavePolicy.update(
                            { user_leave_policy_status: user_leave_policy_status.ACTIVE },
                            {
                                where: {
                                    id: userPolicy.id
                                }
                            }
                        );
                        const currentYear = moment().year();
                        if (leave_accural_form.leave_policy_accural) {
                            await handleLeaveAccrual(findLeaveType, findLeaveObj, userPolicy, findUserMeta, generalSettings, currentYear, req, user, leaveBalanceBasedOnEmpContract, findLeaveYear, findLeaveEnd)
                        }
                    }
                }
            }

            if (Array.isArray(encashment_leavals) && encashment_leavals.length > 0) {
                await LeaveApprovalMetaPolicy.destroy({ where: { leave_accural_policy_id: leave_accural_policy_id, approval_type: approval_type.ENCASEMENT } });
                for (const meta of encashment_leavals) {
                    if (meta?.user_id.length > 0) {
                        for (const user_id of meta.user_id) {
                            await LeaveApprovalMetaPolicy.create({ user_id: user_id, leave_policy_leval: meta.leave_policy_leval, auto_approve_or_skip: meta.auto_approve_or_skip, auto_approve_or_skip_days: meta.auto_approve_or_skip_days, leave_accural_policy_id: leave_accural_policy_id, approval_type: approval_type.ENCASEMENT } as any);
                        }
                    }
                }
            }
        }

        // Update other related policies only if they have values
        if (Object.keys(leave_application_rules_form).length > 0) {
            await LeaveApplicationRulesPolicy.update(leave_application_rules_form, { where: { leave_accural_policy_id: leave_accural_policy_id } });
        }

        if (Object.keys(leave_restricition_form).length > 0) {
            await LeaveRestrictionPolicy.update(leave_restricition_form, { where: { leave_accural_policy_id: leave_accural_policy_id } });
        }

        if (Object.keys(holiday_weekend_form).length > 0) {
            await LeaveHolidayWeekendPolicy.update(holiday_weekend_form, { where: { leave_accural_policy_id: leave_accural_policy_id } });
        }

        if (Object.keys(leave_approval_form).length > 0) {
            const { leave_approval_meta_form, ...newObj } = leave_approval_form;
            await LeaveApprovalPolicy.update({ ...newObj }, { where: { leave_accural_policy_id: leave_accural_policy_id } });
            if (Array.isArray(leave_approval_meta_form) && leave_approval_meta_form.length > 0) {
                await LeaveApprovalMetaPolicy.destroy({ where: { leave_accural_policy_id: leave_accural_policy_id, approval_type: approval_type.LEAVE } });
                for (const meta of leave_approval_meta_form) {
                    if (meta?.user_id.length > 0) {
                        for (const user_id of meta.user_id) {
                            await LeaveApprovalMetaPolicy.create({ user_id: user_id, leave_policy_leval: meta.leave_policy_leval, leave_accural_policy_id, approval_type: approval_type.LEAVE, auto_approve_or_skip: meta?.auto_approve_or_skip, auto_approve_or_skip_days: meta?.auto_approve_or_skip_days } as any);
                        }
                    }
                }
            }
        }

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("LEAVE_POLICY_UPDATED_SUCCESSFULLY"),
        });
    } catch (error: any) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getLeavePolicyById = async (req: Request, res: Response) => {
    try {
        const { leave_accural_policy_id } = req.params;

        // Fetch leave policy details
        const leavePolicy: any = await LeaveAccuralPolicy.findOne({
            where: { id: leave_accural_policy_id, status: leave_accural_policy.ACTIVE },
            include: [
                { model: LeaveApplicationRulesPolicy, as: "leave_application_rules_form", where: { leave_accural_policy_id }, required: false },
                { model: LeaveRestrictionPolicy, as: "leave_restricition_form", where: { leave_accural_policy_id }, required: false },
                { model: LeaveHolidayWeekendPolicy, as: "holiday_weekend_form", where: { leave_accural_policy_id }, required: false },
                { model: LeaveApprovalPolicy, as: "leave_approval_form", where: { leave_accural_policy_id }, required: false }
            ],
            raw: false,
            nest: true
        });

        if (!leavePolicy) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("LEAVE_POLICY_NOT_FOUND"),
            });
        }

        // Fetch leave approval meta policies
        const leaveApprovalMeta = await LeaveApprovalMetaPolicy.findAll({
            where: { leave_accural_policy_id },
            raw: true,
            nest: true
        });

        // Categorize leave approval meta into "leave" and "encashment"
        const leaveMeta = leaveApprovalMeta.filter(meta => meta.approval_type === approval_type.LEAVE);
        const encashmentMeta = leaveApprovalMeta.filter(meta => meta.approval_type === approval_type.ENCASEMENT);

        // Remove unwanted properties from dataValues and prepare clean accural data
        const {
            leave_application_rules_form,
            leave_restricition_form,
            holiday_weekend_form,
            leave_approval_form,
            ...cleanedDataValues
        } = leavePolicy?.dataValues || {};

        // Construct response in required format
        const response = {
            leave_type_id: leavePolicy.leave_type_id,
            leave_accural_form: {
                ...cleanedDataValues,
                encashment_leavals: encashmentMeta.length ? await getLeaveApprovalMetaWithUsers(encashmentMeta) : [],
            },
            leave_application_rules_form: leave_application_rules_form || {},
            leave_restricition_form: leave_restricition_form || {},
            holiday_weekend_form: holiday_weekend_form || {},
            leave_approval_form: {
                ...(leave_approval_form?.dataValues || {}),
                leave_approval_meta_form: leaveMeta.length ? await getLeaveApprovalMetaWithUsers(leaveMeta) : [],
            },
        };

        return res.status(StatusCodes.OK).json({
            status: true,
            data: response,
        });
    } catch (error: any) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const removeLeavePolicyFromLeaveType = async (req: Request, res: Response) => {
    try {
        const { leave_accural_policy_id }: any = req.params;

        const leavePolicy = await LeaveAccuralPolicy.findOne({ attributes: ['id'], where: { id: leave_accural_policy_id }, raw: true });
        if (!leavePolicy) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("LEAVE_POLICY_NOT_FOUND"),
            });
        }

        const findUserLeavePolicy = await UserLeavePolicy.findAll({ where: { leave_accural_policy_id: leave_accural_policy_id, user_leave_policy_status: user_leave_policy_status.ACTIVE } });
        if (findUserLeavePolicy.length > 0) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("SOME_USER_HAVE_ALREADY_ASSIGNED_THIS_POLICY"),
            });
        }

        await LeaveAccuralPolicy.update({ status: leave_accural_policy.DELETED }, { where: { id: leavePolicy.id } });

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("LEAVE_POLICY_DELETED_SUCCESSFULLY"),
        });
    } catch (error: any) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getLeaveApprovalMetaWithUsers = async (leaveMeta: any) => {
    // Group by leave_policy_leval and collect user_id in an array
    const groupedLeaveMeta = leaveMeta.reduce((acc: any, meta: any) => {
        const { leave_policy_leval, user_id, auto_approve_or_skip, auto_approve_or_skip_days } = meta;

        if (!acc[leave_policy_leval]) {
            acc[leave_policy_leval] = {
                leave_policy_leval,
                users: [], // Store user details instead of just IDs
                auto_approve_or_skip,
                auto_approve_or_skip_days
            };
        }

        acc[leave_policy_leval].users.push(user_id); // Temporarily store user_id

        return acc;
    }, {});

    // Fetch user details for all unique user IDs
    const userIds: any = [...new Set(leaveMeta.map((meta: any) => meta.user_id))]; // Get unique user IDs
    const users = await User.findAll({
        where: { id: { [Op.in]: userIds } },
        attributes: ["id",
            [
                sequelize.fn(
                    "concat",
                    sequelize.col("user_first_name"),
                    " ",
                    sequelize.col("user_last_name"),
                ),
                "user_full_name",
            ],
            "user_joining_date",
            "user_status",
            "user_email",
            "employment_number",
            [
                sequelize.literal(
                    `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
          END`
                ),
                "user_avatar_link",
            ]],
        raw: true
    });

    // Create a user lookup object for quick access
    const userMap = users.reduce((acc: any, user: any) => {
        acc[user.id] = { id: user.id, user_full_name: user.user_full_name, user_avatar_link: user.user_avatar_link };
        return acc;
    }, {});

    // Replace user_id with full user details
    return Object.values(groupedLeaveMeta).map((group: any) => ({
        ...group,
        users: group.users.map((userId: any) => userMap[userId]) // Map user details
    }));
};


// API to create/update policy setting
export const createUpdatePolicySetting = async (req: Request, res: Response) => {
    const { allow_past_date_leave, apply_leave_upto_limit, leave_upto_limit, policy_request_require_approval, display_user_list_in_calender, policy_setting_approval_meta_form } = req.body;
    try {
        const policySetting = await PolicySetting.findOne();
        if (policySetting) {
            await policySetting.update({
                allow_past_date_leave,
                apply_leave_upto_limit,
                leave_upto_limit,
                policy_request_require_approval,
                display_user_list_in_calender
            }, { where: { id: policySetting.id } as any });
            if (policy_setting_approval_meta_form.length > 0) {
                await PolicySettingMeta.destroy({ where: { policy_setting_id: policySetting.id } });
                for (const meta of policy_setting_approval_meta_form) {
                    if (meta?.user_id.length > 0) {
                        for (const user_id of meta.user_id) {
                            await PolicySettingMeta.create({ user_id: user_id, policy_setting_leval: meta.policy_setting_leval, policy_setting_id: policySetting.id } as any);
                        }
                    }
                }
            }
            return res.status(StatusCodes.OK).json({ message: res.__("POLICY_SETTING_UPDATED_SUCCESSFULLY") });
        } else {
            const addPolicySetting: any = await PolicySetting.create({
                allow_past_date_leave,
                apply_leave_upto_limit,
                leave_upto_limit,
                policy_request_require_approval,
                display_user_list_in_calender
                // organization_id: req.user?.organization_id
            } as any);

            if (addPolicySetting) {
                for (const meta of policy_setting_approval_meta_form) {
                    if (meta?.user_id.length > 0) {
                        for (const user_id of meta.user_id) {
                            await PolicySettingMeta.create({ user_id: user_id, policy_setting_leval: meta.policy_setting_leval, policy_setting_id: addPolicySetting.id } as any);
                        }
                    }
                }
            }
            return res.status(StatusCodes.OK).json({ message: res.__("POLICY_SETTING_CREATED_SUCCESSFULLY") });
        }
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};
// API to get policy setting
const getPolicySetting = async (req: Request, res: Response) => {
    try {
        let policySetting: any = await PolicySetting.findOne();
        if (policySetting) {
            policySetting = JSON.parse(JSON.stringify(policySetting))
            const findPolicySettingMeta = await PolicySettingMeta.findAll({ attributes: [['policy_setting_leval', 'leave_policy_leval'], 'user_id'], where: { policy_setting_id: policySetting.id } })
            policySetting.user_policy_setting_leave = findPolicySettingMeta.length > 0 ? await getLeaveApprovalMetaWithUsers(findPolicySettingMeta) : []
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: policySetting || {}
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

export default {
    createLeavePolicy,
    updateLeavePolicy,
    getLeavePolicyById,
    removeLeavePolicyFromLeaveType,
    createUpdatePolicySetting,
    getPolicySetting
}