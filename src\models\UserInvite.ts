"use strict";
import { Model, DataTypes } from "sequelize";
import { addActivity } from "../helper/queue.service";
import { sequelize } from "./index";


interface userInviteAttributes {
  id: number;
  user_id: number;
  invitation_status: string;
  action_by: number;
  created_by: number;
  updated_by: number;
}


export enum invitation_status {
  INVITED = "invited",
  REINVITED = "reinvited",
  ACCEPTED = "accepted"
}

export class UserInvite
  extends Model<userInviteAttributes, never>
  implements userInviteAttributes {
  id!: number;
  user_id!: number;
  invitation_status!: string;
  action_by!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }

}

UserInvite.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
    },
    invitation_status: {
      type: DataTypes.ENUM,
      values: Object.values(invitation_status),
      defaultValue: invitation_status.INVITED
    },
    action_by: {
      type: DataTypes.INTEGER,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_invitations",
    modelName: "UserInvite",
    timestamps: true,
  },
);


UserInvite.addHook("afterUpdate", async (userInvite: any) => {
  await addActivity("UserInvite", "updated", userInvite);
});

UserInvite.addHook("afterCreate", async (userInvite: UserInvite) => {
  await addActivity("UserInvite", "created", userInvite);
});

UserInvite.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});