"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface LeaveRuleAttributes {
    id: number;
    leave_rule_title: string;
    leave_rule_description: string;
    leave_rule_status: string;
    leave_rule_year: string;
    leave_rule_type: string;
    leave_rule_boolean: boolean;
    leave_rule_days: number;
    leave_rule_start_date: string;
    leave_rule_end_date: string
    leave_rule_slug: string;
    created_by: number;
    updated_by: number;
}

export class LeaveRule
    extends Model<LeaveRuleAttributes, never>
    implements LeaveRuleAttributes {
    public id!: number;
    public leave_rule_title!: string;
    public leave_rule_description!: string;
    public leave_rule_status!: string;
    public leave_rule_year!: string;
    public leave_rule_type!: string;
    public leave_rule_boolean!: boolean;
    public leave_rule_days!: number;
    public leave_rule_start_date!: string;
    public leave_rule_end_date!: string
    public leave_rule_slug!: string;
    public created_by!: number;
    public updated_by!: number;

    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

export enum leave_rule_status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DELETED = "deleted"
}

export enum leave_rule_type {
    TOGGLE = "toggle",
    DATE = "date",
    NUMBER = "number",
    COMBINATION = "combination",
}


LeaveRule.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        leave_rule_title: {
            type: DataTypes.TEXT("long"),
            allowNull: false,
        },
        leave_rule_description: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        leave_rule_status: {
            type: DataTypes.ENUM,
            values: Object.values(leave_rule_status),
            defaultValue: leave_rule_status.ACTIVE,
        },
        leave_rule_year: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        leave_rule_type: {
            type: DataTypes.ENUM,
            values: Object.values(leave_rule_type),
            allowNull: true
        },
        leave_rule_boolean: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        leave_rule_days: {
            type: DataTypes.DECIMAL,
            allowNull: true
        },
        leave_rule_start_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        leave_rule_end_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        leave_rule_slug: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_leave_rule",
        modelName: "LeaveRule",
    },
);


// Define hooks for leave type model
LeaveRule.addHook("afterUpdate", async (LeaveRule: any) => {
    await addActivity("LeaveRule", "updated", LeaveRule);
});

LeaveRule.addHook("afterCreate", async (leaveRule: LeaveRule) => {
    await addActivity("LeaveRule", "created", leaveRule);
});

LeaveRule.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});


