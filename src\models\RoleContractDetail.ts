"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { Role } from "./Role";

interface roleWiseContractAttributes {
  id: number;
  role_id: number;
  content: string;
  duties: string;
  status: string;
  created_by: number;
  updated_by: number;
}


export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export class RoleContractDetail
  extends Model<roleWiseContractAttributes, never>
  implements roleWiseContractAttributes {
  id!: number;
  role_id!: number;
  content!: string;
  duties!: string;
  status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

RoleContractDetail.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    role_id: {
      type: DataTypes.INTEGER,
    },
    duties: {
      type: DataTypes.TEXT('long'),
    },
    content: {
      type: DataTypes.TEXT('long'),
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(status)
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_role_contract_detail",
    modelName: "RoleContractDetail",
  },
);

RoleContractDetail.belongsTo(Role, {
  foreignKey: "role_id",
  as: "user_contract_role",
});
Role.hasMany(RoleContractDetail, {
  foreignKey: "role_id",
  as: "role_data",
});


RoleContractDetail.addHook("afterUpdate", async (roleContractDetail: any) => {
  await addActivity("RoleContractDetail", "updated", roleContractDetail);
});

RoleContractDetail.addHook("afterCreate", async (roleContractDetail: RoleContractDetail) => {
  await addActivity("RoleContractDetail", "created", roleContractDetail);
});

RoleContractDetail.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
