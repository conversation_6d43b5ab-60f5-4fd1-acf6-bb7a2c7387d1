import { Request, Response } from "express";
import { Role, role_status } from '../models/MORole';
import { Op, Transaction } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { StatusCodes } from "http-status-codes";
import { ROLE_CONSTANT, ROLE_PERMISSIONS } from "../helper/constant";
import { MOPermission, status as permission_status } from "../models/MOPermission";
import { MOModule } from "../models/MOModule";
import { sequelize } from "../models";
import _ from 'lodash'
import { permittedForAdmin, validateModulePermission } from "../helper/common";

// Create Role
export const createRole = async (req: Request, res: Response) => {
    try {
        const { role_name, parent_role_id, platform } = req.body;

        const checkSuperAdminRole = await permittedForAdmin(req.user?.id, [
        ROLE_CONSTANT.SUPER_ADMIN
      ]);

        if (!checkSuperAdminRole) {
            const checkPermission = await validateModulePermission(
                req.user,
                req.user.organization_id,
                6, // Setting module ID
                ROLE_PERMISSIONS.CREATE
            )
            if (!checkPermission) {
                return res
                    .status(StatusCodes.FORBIDDEN)
                    .json({ status: false, message: res.__("PERMISSION_DENIED") });
            }
        }

        // Duplicate validation
        const existingRole = await Role.findOne({
            where: {
                role_name,
                organization_id: req.user.organization_id
            }
        });
        if (existingRole) {
            if (existingRole.role_status === role_status.ACTIVE) {
                return res.status(400).json({
                    status: false,
                    message: res.__("ROLE_ALREADY_EXISTS"),
                });
            } else {
                await Role.setHeaders(req).update(
                    { role_status: role_status.ACTIVE },
                    {
                        where: {
                            id: existingRole.id,
                            organization_id: req.user.organization_id
                        }
                    }
                )
                return res.status(200).json({
                    status: true,
                    message: res.__("ROLE_UPDATED_SUCCESSFULLY")
                });
            }
        }

        const role = await Role.setHeaders(req).create({
            role_name,
            parent_role_id,
            platform,
            created_by: req.user.id,
            updated_by: req.user.id,
            role_status: role_status.ACTIVE,
            organization_id: req.user.organization_id
        } as any);

        return res.status(201).json({
            status: true,
            message: res.__("ROLE_CREATED_SUCCESSFULLY"),
            data: role
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Roles
export const getRoles = async (req: Request, res: Response) => {
    try {
        const { search, status, page, size, id } = req.query;
        const { limit, offset } = getPagination(Number(page), Number(size));
        const whereClause: any = {
            where: {
                [Op.or]: [
                    { organization_id: req.user.organization_id },
                    { organization_id: null }
                ]
            },
        };

        if (page && size) {
            whereClause.limit = limit;
            whereClause.offset = offset;
        }

        if (id) {
            whereClause.where.id = id;
        }

        if (search) {
            whereClause.where.role_name = { [Op.like]: `%${search}%` };
        }

        if (status) {
            whereClause.where.role_status = status;
        }

        const Roles = await Role.findAndCountAll(whereClause);
        const response = getPaginatedItems(Number(size), Number(page), Roles?.count);

        const data = {
            data: Roles.rows,
            ...response
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Update Role
export const updateRole = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { role_name, parent_role_id, platform } = req.body;


        // Duplicate validation
        const existingRole = await Role.findOne({
            where: {
                role_name,
                id: { [Op.ne]: id },
                organization_id: req.user.organization_id
            }
        });
        if (existingRole && existingRole.role_status != role_status.INACTIVE) {
            return res.status(400).json({
                status: false,
                message: res.__("ROLE_ALREADY_EXISTS"),
            });
        }

        const role = await Role.findOne({
            where: {
                id,
                organization_id: req.user.organization_id
            }
        });
        if (!role) {
            return res.status(404).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND"),
            });
        }

        const updateObj = {
            role_name: role_name ? role_name : role.role_name,
            parent_role_id: parent_role_id ? parent_role_id : role.parent_role_id,
            platform: platform ? platform : role.platform,
            updated_by: req.user.id,
            role_status: role_status.ACTIVE,
        }

        await Role.setHeaders(req).update(updateObj, {
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        })

        return res.status(200).json({
            status: true,
            message: res.__("ROLE_UPDATED_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Soft Delete Role (Set status to inactive)
export const deleteRole = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const role = await Role.findOne({
            where: {
                id,
                organization_id: req.user.organization_id
            }
        });
        if (!role) {
            return res.status(404).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND"),
            });
        }

        await Role.setHeaders(req).update(
            { role_status: role_status.INACTIVE },
            {
                where: {
                    id: id,
                    organization_id: req.user.organization_id
                }
            }
        )

        return res.status(200).json({
            status: true,
            message: res.__("ROLE_SET_INACTIVE_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


// Ensure view permission is added if create/edit/delete is present
export const setViewPermission = (permission: number) => {
    if (permission & (ROLE_PERMISSIONS.CREATE | ROLE_PERMISSIONS.EDIT | ROLE_PERMISSIONS.DELETE)) {
        permission |= ROLE_PERMISSIONS.VIEW;  // Add view permission if create/edit/delete is present
    }
    return permission;
};

// Format permission response
export const formatPermissions = (permissions: any) => {
    const result: any = {};
    permissions.forEach((perm: any) => {
        const permObj = {
            none: perm.permission == 0,
            view: (perm.permission & ROLE_PERMISSIONS.VIEW) > 0,
            create: (perm.permission & ROLE_PERMISSIONS.CREATE) > 0,
            edit: (perm.permission & ROLE_PERMISSIONS.EDIT) > 0,
            delete: (perm.permission & ROLE_PERMISSIONS.DELETE) > 0,
            partial: perm.partial ? true : false,
            module_name: perm?.module?.module_name
        };

        result[perm.module_id] = permObj;
    });

    return result;
};

export const createPermission = async (req: Request, res: Response) => {
    const { role_id, module_id, permission, platform, partial } = req.body;

    try {

        const existingPermission = await MOPermission.findOne({
            where: {
                role_id,
                module_id,
                organization_id: req.user.organization_id
            }
        });

        if (existingPermission && existingPermission.status == permission_status.ACTIVE) {
            return res.status(400).json({
                status: false,
                message: res.__("MODULE_ALREADY_EXISTS_FOR_ROLE")
            });
        }
        // Ensure view permission is added if create/edit/delete is present
        const finalPermission = setViewPermission(permission);

        const newPermission = await MOPermission.setHeaders(req).create({
            role_id,
            module_id,
            permission: finalPermission,
            platform,
            partial,
            created_by: req.user.id,
            updated_by: req.user.id,
            organization_id: req.user.organization_id
        } as any);

        const Roles = await Role.findAll({
            where: {
                id: { [Op.ne]: role_id },
                organization_id: req.user.organization_id
            }
        });
        if (Roles.length) {
            await Promise.all(Roles.map(async (role) => {
                const findRolePermission = await MOPermission.findOne({
                    where: {
                        role_id: role.id,
                        module_id: module_id,
                        organization_id: req.user.organization_id
                    }
                });
                if (findRolePermission) {
                    await MOPermission.setHeaders(req).update(
                        { permission: findRolePermission.permission },
                        {
                            where: {
                                role_id: role.id,
                                module_id: module_id,
                                organization_id: req.user.organization_id
                            }
                        }
                    )
                } else {
                    await MOPermission.setHeaders(req).create({
                        role_id: role.id,
                        module_id,
                        permission: 0,
                        platform,
                        partial,
                        created_by: req.user.id,
                        updated_by: req.user.id,
                        organization_id: req.user.organization_id
                    } as any)
                }
            }))
        }

        return res.status(201).json({
            status: true,
            message: res.__("PERMISSION_CREATED_SUCCESSFULLY"),
            data: newPermission
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

export const updatePermission = async (req: Request, res: Response) => {
    const { role_id, permissions, platform } = req.body;

    try {

        await Promise.all(Object.keys(permissions).map(async (perms) => {
            const permissionRecord = await MOPermission.findOne({
                where: {
                    role_id,
                    module_id: perms,
                    organization_id: req.user.organization_id
                }
            });
            if (!permissionRecord) {
                return res.status(404).json({
                    status: false,
                    message: res.__("PERMISSION_NOT_FOUND")
                });
            }
            // Ensure view permission is added if create/edit/delete is present
            const finalPermission = setViewPermission(permissions[perms]?.permission);

            const updateObj: any = {
                permission: finalPermission,
                partial: permissions[perms]?.partial,
                updated_by: req.user.id,
                platform: platform ? platform : permissionRecord?.platform,
                status: permission_status.ACTIVE
            }

            await MOPermission.setHeaders(req).update(updateObj, {
                where: {
                    id: permissionRecord.id,
                    organization_id: req.user.organization_id
                }
            })
        }))

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSION_UPDATED_SUCCESSFULLY")
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


export const getPermissions = async (req: Request, res: Response) => {
    const { id } = req.params;
    const { page, size, search = "", role_id = null, platform = null }: any = req.query;

    try {
        const { limit, offset } = getPagination(Number(page), Number(size));

        const whereObj: any = {
            where: {
                status: permission_status.ACTIVE,
                organization_id: req.user.organization_id
            },
            include: [
                {
                    model: Role,
                    as: 'role',
                    attributes: ['id', 'role_name', 'role_status']
                },
                {
                    model: MOModule,
                    as: 'module',
                    attributes: ['id', 'module', 'module_name']
                }
            ],
            raw: true,
            nest: true
        }


        if (id) whereObj.where.id = id
        if (role_id) whereObj.where.role_id = role_id;
        if (platform) whereObj.where.platform = platform;
        if (search) {
            whereObj.include[1].where = {
                [Op.or]: [
                    { module: { [Op.like]: `%${search}%` } },
                    { module_name: { [Op.like]: `%${search}%` } }
                ]
            };
        }

        // if (page && size) {
        //     whereObj.limit = limit;
        //     whereObj.offset = offset;
        // }

        const permissions = await MOPermission.findAndCountAll(whereObj);

        // Format the permissions for returning
        const groupedPermission = _.groupBy(permissions.rows, 'role_id')

        let formattedPermissions = []
        for (const roleId in groupedPermission) {
            const permission = groupedPermission[roleId];
            const formattedPermission = formatPermissions(permission)
            formattedPermissions.push({ role_id: parseInt(roleId), role_name: permission[0]?.role?.role_name, permissions: formattedPermission })
        }
        const response = getPaginatedItems(Number(size), Number(page), formattedPermissions.length);

        if (page && size) {
            formattedPermissions = formattedPermissions.slice(offset, page * size);
        }

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_FETCHED_SUCCESSFULLY"),
            data: formattedPermissions,
            ...response
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


export const deletePermission = async (req: Request, res: Response) => {
    const { id } = req.params;

    try {

        const permissionRecord = await MOPermission.findOne({
            where: {
                id,
                organization_id: req.user.organization_id
            }
        });
        if (!permissionRecord) {
            return res.status(404).json({
                status: false,
                message: res.__("PERMISSION_NOT_FOUND")
            });
        }

        await MOPermission.setHeaders(req).update(
            { status: permission_status.INACTIVE } as any,
            {
                where: {
                    id: id,
                    organization_id: req.user.organization_id
                }
            }
        )

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSION_DELETED_SUCCESSFULLY")
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


export const copyPermissions = async (req: Request, res: Response) => {
    const { from_role, to_role } = req.body;

    try {
        // Step 1: Fetch all permissions for the from_role
        const fromPermissions = await MOPermission.findAll({
            where: {
                role_id: from_role,
                organization_id: req.user.organization_id
            }
        });

        if (!fromPermissions.length) {
            return res.status(404).json({
                status: false,
                message: res.__("NO_PERMISSIONS_FOUND_FOR_FROM_ROLE")
            });
        }

        // Step 2: Check for duplicate modules in to_role
        const toPermissions = await MOPermission.findAll({
            where: {
                role_id: to_role,
                organization_id: req.user.organization_id
            }
        });

        const toPermissionModules = toPermissions.map((permission: any) => permission.module_id);

        const duplicateModules = fromPermissions.filter((permission: any) =>
            toPermissionModules.includes(permission.module_id)
        );

        if (duplicateModules.length > 0) {
            return res.status(400).json({
                status: false,
                message: res.__("DUPLICATE_MODULES_FOUND_FOR_TO_ROLE"),
                duplicateModules: duplicateModules.map((permission: any) => permission.module_id)
            });
        }

        await sequelize.transaction(async (t: Transaction) => {
            for (const permission of fromPermissions) {
                await MOPermission.create(
                    {
                        role_id: to_role,
                        module_id: permission.module_id,
                        permission: permission.permission,
                        platform: permission.platform,
                        partial: permission.partial,
                        created_by: req.user.id,
                        updated_by: req.user.id,
                        organization_id: req.user.organization_id
                    } as any,
                    { transaction: t }
                );
            }
        });

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_COPIED_SUCCESSFULLY")
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// ==================== MODULE CRUD APIs ====================

// Create Module
export const createModule = async (req: Request, res: Response) => {
    try {
        const { module, module_name } = req.body;

        // Duplicate validation
        const existingModule = await MOModule.findOne({
            where: { module }
        });

        if (existingModule) {
            return res.status(400).json({
                status: false,
                message: res.__("MODULE_ALREADY_EXISTS"),
            });
        }

        const newModule = await MOModule.setHeaders(req).create({
            module,
            module_name,
            created_by: req.user.id,
            updated_by: req.user.id
        } as any);

        return res.status(201).json({
            status: true,
            message: res.__("MODULE_CREATED_SUCCESSFULLY"),
            data: newModule
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Modules
export const getModules = async (req: Request, res: Response) => {
    try {
        const { search, page, size, id } = req.query;
        const { limit, offset } = getPagination(Number(page), Number(size));

        const whereClause: any = {
            where: {},
        };

        if (page && size) {
            whereClause.limit = limit;
            whereClause.offset = offset;
        }

        if (id) {
            whereClause.where.id = id;
        }

        if (search) {
            whereClause.where[Op.or] = [
                { module: { [Op.like]: `%${search}%` } },
                { module_name: { [Op.like]: `%${search}%` } }
            ];
        }

        const modules = await MOModule.findAndCountAll(whereClause);
        const response = getPaginatedItems(Number(size), Number(page), modules?.count);

        const data = {
            data: modules.rows,
            ...response
        }

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Update Module
export const updateModule = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { module, module_name } = req.body;

        // Check if module exists
        const existingModule = await MOModule.findByPk(id);

        if (!existingModule) {
            return res.status(404).json({
                status: false,
                message: res.__("MODULE_NOT_FOUND"),
            });
        }

        // Duplicate validation
        const duplicateModule = await MOModule.findOne({
            where: {
                module,
                id: { [Op.ne]: id }
            }
        });

        if (duplicateModule) {
            return res.status(400).json({
                status: false,
                message: res.__("MODULE_ALREADY_EXISTS"),
            });
        }

        const updateObj = {
            module: module ? module : existingModule.module,
            module_name: module_name ? module_name : existingModule.module_name,
            updated_by: req.user.id,
        }

        await MOModule.setHeaders(req).update(updateObj, {
            where: { id: id }
        });

        return res.status(200).json({
            status: true,
            message: res.__("MODULE_UPDATED_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Delete Module
export const deleteModule = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const module = await MOModule.findByPk(id);

        if (!module) {
            return res.status(404).json({
                status: false,
                message: res.__("MODULE_NOT_FOUND"),
            });
        }

        await MOModule.setHeaders(req).destroy({
            where: { id: id }
        });

        return res.status(200).json({
            status: true,
            message: res.__("MODULE_DELETED_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};
