"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { Item } from "./Item";

interface documentCategoryAttributes {
  id: number;
  category_image: number;
  category_name: string;
  category_description: string;
  category_status: string;
  category_type: string;
  category_use: string;
  parent_id: number;
  dashboard_view: boolean;
  has_agreement_required: boolean;
  has_video_control: boolean;
  category_order: number;
  is_external_link: boolean;
  organization_id: string;
  created_by: number;
  updated_by: number;
}

export enum category_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}


export enum category_type {
  FILE = "file",
  FOLDER = "folder",
}

export enum category_use {
  DOCUMENT = "document",
  TRAINING = "training",
}

export class DocumentCategory
  extends Model<documentCategoryAttributes, never>
  implements documentCategoryAttributes {
  id!: number;
  category_image!: number;
  category_name!: string;
  category_description!: string;
  category_status!: string;
  category_type!: string;
  category_use!: string;
  parent_id!: number;
  dashboard_view!: boolean;
  has_agreement_required!: boolean;
  has_video_control!: boolean;
  category_order!: number;
  is_external_link!: boolean;
  organization_id!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    return this;
  }
}

DocumentCategory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    category_image: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    category_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    category_description: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    category_status: {
      type: DataTypes.ENUM,
      values: Object.values(category_status),
      defaultValue: category_status.ACTIVE,
    },
    category_type: {
      type: DataTypes.ENUM,
      values: Object.values(category_type),
      allowNull: false,
    },
    category_use: {
      type: DataTypes.ENUM,
      values: Object.values(category_use),
      allowNull: true,
    },
    parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    dashboard_view: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    has_agreement_required: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    has_video_control: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    category_order: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    is_external_link: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_document_category",
    modelName: "DocumentCategory",
  }
);


// Self-referential association
DocumentCategory.hasMany(DocumentCategory, { as: 'children', foreignKey: 'parent_id' });
DocumentCategory.belongsTo(DocumentCategory, { as: 'parent', foreignKey: 'parent_id' });


// Define hooks for Category model
DocumentCategory.addHook("afterUpdate", async (documentCategory: any) => {
  await addActivity("DocumentCategory", "updated", documentCategory);
});

DocumentCategory.addHook("afterCreate", async (documentCategory: DocumentCategory) => {
  await addActivity("DocumentCategory", "created", documentCategory);
});

DocumentCategory.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});


Item.hasOne(DocumentCategory, { foreignKey: "category_image" });
DocumentCategory.belongsTo(Item, { foreignKey: "category_image", as: "item_image" });
