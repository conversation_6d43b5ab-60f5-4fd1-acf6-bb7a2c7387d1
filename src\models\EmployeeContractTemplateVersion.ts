"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface contractAttributes {
    id: number;
    emp_contract_template_id: number;
    versionNumber: number;
    content: string;
    remark: string;
    updated_by: number;
}

export class EmpContractTemplateVersion
    extends Model<contractAttributes, never>
    implements contractAttributes {
    id!: number;
    emp_contract_template_id!: number;
    versionNumber!: number;
    content!: string;
    remark!: string;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    DELETED = "deleted"
}

export enum type {
    GENERAL = "general",
    DEPT = "department"
}

EmpContractTemplateVersion.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        emp_contract_template_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        versionNumber: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        content: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
        },
        remark: {
            type: DataTypes.TEXT('long'),
        },
        updated_by: {
            type: DataTypes.INTEGER,
            allowNull: false,
        }
    },
    {
        sequelize: sequelize,
        tableName: "nv_contract_template_version",
        modelName: "EmpContractTemplateVersion",
    },
);

// Define hooks for HRMC model
EmpContractTemplateVersion.addHook("afterUpdate", async (empContractTmpVersion: any) => {
    await addActivity("EmpContractTemplateVersion", "updated", empContractTmpVersion);
});

EmpContractTemplateVersion.addHook("afterCreate", async (empContractTmpVersion: EmpContractTemplateVersion) => {
    await addActivity("EmpContractTemplateVersion", "created", empContractTmpVersion);
});

EmpContractTemplateVersion.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});


