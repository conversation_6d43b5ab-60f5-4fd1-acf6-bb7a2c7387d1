import cron from "node-cron";
import { User, user_status } from "../models/User";
import { Op, Sequelize } from "sequelize";
import moment from "moment";
import { createNotification, getGeneralSettingObj, getOrganizationLogo, getReminderUser, getReminder<PERSON>serForExpenses, getReminderUserOnLastWeek, getUserFullName, handleLeaveAccrual, reminderContractExpireSoon, reminderProbationUserSoon, sendEmailNotification } from "./common";
import { EMAIL_ADDRESS, NOTIFICATIONCONSTANT, NOTIFICATION_TYPE, REDIRECTION_TYPE } from "./constant";
import { Resignation, resignation_status } from "../models/Resignation";
import { LeaveAccuralPolicy, status } from "../models/LeaveAccuralPolicy";
import { user_leave_policy_status, UserLeavePolicy } from "../models/UserLeavePolicy";
import { UserMeta } from "../models/UserMeta";
import { user_leave_policy_history_status, UserLeavePolicyHistory } from "../models/UserLeavePolicyHistory";
import { ForecastAssignBudget, user_assign_status } from "../models/ForecastAssignBudget";
import { Forecast, forecast_status } from "../models/Forecast";
import { Branch } from "../models/Branch";

export const onboardingReminder = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.onboardingReminder, async () => {
  try {
    console.log("onboarding reminder start cron ====>")
    const currentDate = moment().utc();
    const firstTryStart = currentDate
      .clone()
      .subtract(15, "days")
      .startOf("day");
    const firstTryEnd = currentDate.clone().subtract(14, "days").endOf("day");
    const secondTryStart = currentDate
      .clone()
      .subtract(32, "days")
      .startOf("day");
    const secondTryEnd = currentDate.clone().subtract(31, "days").endOf("day");
    const getUserList = await User.findAll({
      attributes: ['id', 'user_first_name', 'user_middle_name', 'user_last_name', 'user_email', 'organization_id', 'appToken', 'webAppToken'],
      where: {
        user_status: {
          [Op.in]: [user_status.ACTIVE, user_status.PENDING],
        },
        createdAt: {
          [Op.or]: [
            { [Op.between]: [firstTryStart.toDate(), firstTryEnd.toDate()] },
            { [Op.between]: [secondTryStart.toDate(), secondTryEnd.toDate()] },
          ],
        },
      }, raw: true, nest: true
    });

    if (getUserList.length > 0) {
      await createNotification(getUserList, 1, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.ONBOARDING_INCOMPLETE.content, NOTIFICATIONCONSTANT.ONBOARDING_INCOMPLETE.heading, REDIRECTION_TYPE.ONBOARDING)
      for (const user of getUserList) {
        const employeeName = [];

        if (user?.user_first_name) {
          employeeName.push(user.user_first_name);
        }
        if (user?.user_middle_name) {
          employeeName.push(user.user_middle_name);
        }
        if (user?.user_last_name) {
          employeeName.push(user.user_last_name);
        }
        const templateData = {
          name: employeeName.join(" "),
          email: user.user_email,
          mail_type: 'onboarding_pending',
          ORGANIZATION_LOGO: await getOrganizationLogo(user.organization_id),
          LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
          ADDRESS: EMAIL_ADDRESS.ADDRESS,
          PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
          EMAIL: EMAIL_ADDRESS.EMAIL,
          ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
          smtpConfig: 'INFO'
        };
        await sendEmailNotification(templateData)

      }
    }
    reminderContractExpireSoon()
    reminderProbationUserSoon()
  } catch (error) {
    console.error("Error running onboarding reminder cron job:", error);
  }
}, {
  scheduled: true,
  timezone: "Europe/London"
});


export const removeResignedUser = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.removeResignedUser, async () => {
  try {
    console.log("Remove resigned users start cron ====>")
    const currentDate = moment().utc();
    const lastDay = currentDate
      .clone()
      .subtract(1, "days")
      .startOf("day");

    const getResignationList = await Resignation.findAll({
      where: {
        last_serving_date: { [Op.lte]: lastDay },
        resignation_status: resignation_status.ACCEPTED
      }, raw: true, nest: true
    });
    if (getResignationList.length > 0) {
      for (const resignUser of getResignationList) {
        await User.update({ user_status: user_status.DELETED }, { where: { id: resignUser.user_id } })
      }
    }
  } catch (error) {
    console.error("Error running onboarding reminder cron job:", error);
  }
}, {
  scheduled: true,
  timezone: "Europe/London"
});

export const sendDsrReminderAt12AM = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.sendDsrReminderAt12AM, async () => {
  try {
    console.log("send reminder at 12 AM start cron ====>")
    const currentDate = moment();
    const pastDate = currentDate.clone().subtract(1, 'day');
    await getReminderUser(pastDate.format('YYYY-MM-DD'))
  } catch {
    console.error("Error running onboarding reminder cron job:");
  }
}, {
  scheduled: true,
  timezone: "Europe/London"
});

export const sendDsrReminderAt12PMForPreviousDay = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.sendDsrReminderAt12PMForPreviousDay, async () => {
  try {
    console.log("send reminder at 12PM start cron ====>")
    const currentDate = moment();
    const pastDate = currentDate.clone().subtract(1, 'day');
    await getReminderUser(pastDate.format('YYYY-MM-DD'))
  } catch {
    console.error("Error running onboarding reminder cron job:");
  }
}, {
  scheduled: true,
  timezone: "Europe/London"
});

export const sendReminderAtMondayForWsrForLastWeek = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.sendReminderAtMondayForWsrForLastWeek, async () => {
  try {
    console.log("send reminder at Monday start For Last week wsr reminder cron ====>")
    const current_date = moment();
    await getReminderUserOnLastWeek(current_date)
  } catch {
    console.error("Error running WSR reminder cron job:");
  }
}, {
  scheduled: true,
  timezone: "Europe/London"
});

export const sendReminderAtTuesdayForWsrForLastWeek = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.sendReminderAtTuesdayForWsrForLastWeek, async () => {
  try {
    console.log("send reminder at Tuesday start For Last week wsr reminder cron ====>")
    const current_date = moment();
    await getReminderUserOnLastWeek(current_date)
  } catch {
    console.error("Error running WSR reminder cron job:");
  }
}, {
  scheduled: true,
  timezone: "Europe/London"
});

// export const sendReminderAtWeekStartForExpense = cron.schedule('0 0 * * 1', async () => {
//   try {
//     console.log("send reminder at Week start For expense reminder cron ====>")
//     const current_date = moment();
//     await getReminderUserForExpenses(current_date)
//   } catch {
//     console.error("Error running WSR reminder cron job:");
//   }
// }, {
//   scheduled: true,
//   timezone: "Europe/London"
// });

export const sendReminderForLastMonthOn10Expense = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.sendReminderForLastMonthOn10Expense, async () => {
  try {
    console.log("send reminder at Week start For expense reminder cron ====>")
    const current_date = moment();
    await getReminderUserForExpenses(current_date)
  } catch {
    console.error("Error running WSR reminder cron job:");
  }
}, {
  scheduled: true,
  timezone: "Europe/London"
});

export const sendReminderForLastMonthOn25Expense = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.sendReminderForLastMonthOn25Expense, async () => {
  try {
    console.log("send reminder at Week start For expense reminder cron ====>")
    const current_date = moment();
    await getReminderUserForExpenses(current_date)
  } catch {
    console.error("Error running WSR reminder cron job:");
  }
}, {
  scheduled: true,
  timezone: "Europe/London"
});

// export const leaveResetCron = cron.schedule('0 0 * * *', async () => {
//   console.log("Running Leave Policy Reset Cron...");

//   const today = moment();
//   const currentMonth = today.month() + 1; // Months are 0-based
//   const currentDate = today.date();

//   try {
//     // Find leave policies that have a reset type and extract leave_policy_reset_value
//     const policiesToReset = await LeaveAccuralPolicy.findAll({
//       where: {
//         leave_policy_reset: 1,
//         leave_policy_reset_type: { [Op.in]: ['yearly', 'monthly', 'quarterly', 'half_yearly', 'one_time'] },
//         status: status.ACTIVE
//       },
//       raw: true
//     });

//     if (!policiesToReset.length) {
//       console.log("No leave policies to reset this month.");
//       return;
//     }

//     for (const policy of policiesToReset) {
//       let resetDates;
//       try {
//         resetDates = policy.leave_policy_reset_value ? JSON.parse(policy.leave_policy_reset_value) : []
//         if (!Array.isArray(resetDates)) {
//           console.error(`Invalid leave_policy_reset_value format for policy ID ${policy.id}`);
//           continue;
//         }
//       } catch (error) {
//         console.error(`Error parsing leave_policy_reset_value for policy ID ${policy.id}`, error);
//         continue;
//       }

//       // Check if today matches any reset date in the policy
//       const shouldReset = resetDates.some(entry => {
//         const entryDate =
//           entry.on_date === "joining_date"
//             ? entry.on_date
//             : entry.on_date;

//         return entryDate == currentDate &&
//           entry.month == currentMonth &&
//           moment(policy.leave_calender_year_start_from).year() == today.year();
//       });

//       if (!shouldReset) continue;

//       const { id, carry_forward_date } = policy;
//       let carryForwardConfig;

//       try {
//         carryForwardConfig = JSON.parse(carry_forward_date);
//       } catch (error) {
//         console.error(`Invalid JSON in carry_forward_date for policy ID ${id}`);
//         continue;
//       }

//       const { carry_forward_number, carry_forward_number_by_type, carry_forward_max_limit, carry_forward_min_limit } = carryForwardConfig;
//       // Fetch user leave balances for this policy
//       const userPolicies = await UserLeavePolicy.findAll({ where: { leave_accural_policy_id: id, user_leave_policy_status: user_leave_policy_status.ACTIVE } });
//       if (userPolicies.length > 0) {
//         for (const userPolicy of userPolicies) {
//           let carryForwardLeave = 0;
//           if (carry_forward_number_by_type == "percentage") {
//             carryForwardLeave = Math.floor((userPolicy.user_remaining_leave * carry_forward_number) / 100);
//           } else if (carry_forward_number_by_type == "unit") {
//             carryForwardLeave = Math.min(userPolicy.user_remaining_leave, carry_forward_number);
//           }

//           carryForwardLeave = Math.max(carry_forward_min_limit, Math.min(carryForwardLeave, carry_forward_max_limit));
//           // Reset user leave balance and add carry forward
//           // await UserLeavePolicy.update(
//           //   {
//           //     user_leave_policy_status: user_leave_policy_status.INACTIVE
//           //   },
//           //   { where: { leave_accural_policy_id: id, user_id: userPolicy.user_id, user_leave_policy_status: user_leave_policy_status.ACTIVE, updated_by: 1 } }
//           // );
//           // Reset user leave balance and add carry forward
//           // await UserLeavePolicy.create(
//           //   {
//           //     user_remaining_leave: 0,
//           //     user_total_balance: Sequelize.literal(`user_total_balance + ${carryForwardLeave}`),
//           //     leave_accural_date: today,
//           //     leave_accural_policy_id: id,
//           //     user_id: userPolicy.user_id,
//           //     user_leave_policy_status: user_leave_policy_status.ACTIVE,
//           //     created_by: 1,
//           //     updated_by: 1
//           //   } as any);
//         }
//       }
//     }

//     console.log("Leave policy reset and carry forward completed.");
//   } catch (error) {
//     console.error("Error in leave policy reset cron:", error);
//   }
// });

export const updateLeaveCron = cron.schedule('0 0 * * *', async () => {
  console.log("Running Update Leave Policy Balance Cron...");
  try {
    // Find leave policies that have a reset type and extract leave_policy_reset_value
    const today = moment();

    const getLeavePolicy = await LeaveAccuralPolicy.findAll({
      attributes: ['id', 'leave_policy_accural', 'leave_policy_reset', 'leave_policy_reset_value', 'leave_calender_year_start_from', 'stop_policy_accural_timewise_type', 'stop_policy_accural_timewise_value', 'leave_balance_based_on_emp_contract', 'leave_policy_end_date', 'leave_policy_reset_type'],
      where: {
        status: status.ACTIVE,
        leave_policy_end_date: { [Op.gte]: today },
      },
      raw: true
    });
    if (getLeavePolicy.length > 0) {
      for (const leavePolicy of getLeavePolicy) {
        const today = moment();
        const findLeaveYear = leavePolicy.leave_calender_year_start_from;
        const findLeaveType = leavePolicy.stop_policy_accural_timewise_type || 'yearly';
        const findLeaveObj = leavePolicy.stop_policy_accural_timewise_value ? JSON.parse(leavePolicy.stop_policy_accural_timewise_value)
          : []
        const policyReset = leavePolicy.leave_policy_reset
        const findLeaveResetObj = leavePolicy.leave_policy_reset_value ? JSON.parse(leavePolicy.leave_policy_reset_value)
          : []
        const leave_policy_accural = leavePolicy.leave_policy_accural
        const getToUser = await UserLeavePolicy.findAll({ where: { leave_accural_policy_id: leavePolicy?.id, user_leave_policy_status: user_leave_policy_status.ACTIVE }, group: ['leave_accural_policy_id', 'user_id']/* , order: [['leave_accural_date', 'DESC']] */, raw: true })

        if (getToUser.length > 0) {
          for (const userList of getToUser) {
            const user: any = await User.findOne({ attributes: ['id', 'user_joining_date', 'organization_id'], where: { id: userList.user_id }, raw: true })
            const isMatchingDate = findLeaveObj.some((leave: any): any => {
              const on_date = leave.on_date == "joining_date" && user?.user_joining_date ? moment(user?.user_joining_date, "DD-MM-YYYY").date() : leave.on_date
              return today.date() == on_date && today.month() + 1 == leave.month
            });
            const findUserMeta = await UserMeta.findOne({ where: { user_id: user?.id } })

            const leaveBalanceBasedOnEmpContract = leavePolicy.leave_balance_based_on_emp_contract
            const findLeaveEnd = leavePolicy.leave_policy_end_date
            const generalSettings = await getGeneralSettingObj(user?.organization_id)
            const currentYear = moment().year()

            if (isMatchingDate && leave_policy_accural == true) {
              await UserLeavePolicy.update(
                { user_leave_policy_status: user_leave_policy_status.ACTIVE },
                {
                  where: {
                    leave_accural_policy_id: leavePolicy?.id, user_id: user?.id, user_leave_policy_status: user_leave_policy_status.ACTIVE
                  },
                }
              );
              if (leavePolicy.leave_policy_accural) {
                await handleLeaveAccrual(findLeaveType, findLeaveObj, userList, findUserMeta, generalSettings, currentYear, null, user, leaveBalanceBasedOnEmpContract, findLeaveYear, findLeaveEnd, false)
              }

              await UserLeavePolicyHistory.update(
                { user_leave_policy_history_status: user_leave_policy_history_status.INACTIVE },
                {
                  where: {
                    leave_user_policy_id: userList.id,
                    [Op.not]: [
                      Sequelize.where(
                        Sequelize.fn("MONTH", Sequelize.col("leave_accural_date")),
                        today.month() + 1
                      ),
                      Sequelize.where(
                        Sequelize.fn("YEAR", Sequelize.col("leave_accural_date")),
                        moment().year()
                      )
                    ]
                  }
                }
              );
            }

            let isResetOn: any = false
            if (policyReset) {
              isResetOn = findLeaveResetObj.some((leave: any): any => {
                const on_date = leave.on_date == "joining_date" && user?.user_joining_date ? moment(user?.user_joining_date, "DD-MM-YYYY").date() : leave.on_date
                return today.date() == on_date && today.month() + 1 == leave.month
              });
            }
            if (isResetOn) {
              await UserLeavePolicy.update(
                { user_leave_policy_status: user_leave_policy_status.ACTIVE },
                {
                  where: {
                    id: userList?.id
                  },
                }
              );
              if (leavePolicy.leave_policy_accural) {
                await handleLeaveAccrual(findLeaveType, findLeaveObj, userList, findUserMeta, generalSettings, currentYear, null, user, leaveBalanceBasedOnEmpContract, findLeaveYear, findLeaveEnd, isResetOn)
              }
            }

          }
        }
      }

    }
    console.log("Leave policy reset and carry forward completed.");
  } catch (error) {
    console.error("Error in leave policy reset cron:", error);
  }
});

/** send reminder every thursday */
export const budgetReminder = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.budgetReminder, async () => {
  console.log("Running Budget Reminder cron...");
  /** Get Assign Budgets data */
  const getAssignedBudgetData: any = await Forecast.findAll({
    where: {
      forecast_status: forecast_status.ASSIGNED
    },
    include: [
      {
        model: ForecastAssignBudget,
        attributes: ['user_id'],
        where: { user_assign_status: user_assign_status.ACTIVE },
        include: [{
          attributes: ['id', 'appToken', 'webAppToken'],
          model: User
        }]
      }, {
        model: Branch,
        as: 'forecast_branch',
        attributes: ['id', 'branch_name']
      }], raw: true, nest: true
  })
  for (const data of getAssignedBudgetData) {
    const user_id = data.ForecastAssignBudgets.user_id
    const user = data.ForecastAssignBudgets.User
    const forecast_year = data.forecast_year
    const getFullName: any = await getUserFullName(user_id)
    const forecast_id = data.id
    const branch_id = data.branch_id

    await createNotification([user], user_id, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.FORECAST_REMINDER_NOTIFICATION.content(getFullName, forecast_year), NOTIFICATIONCONSTANT.FORECAST_REMINDER_NOTIFICATION.heading, REDIRECTION_TYPE.FORECAST, forecast_id, { forecast_id: forecast_id, branch_id: branch_id })
  }
})

/** send reminder if due date is missed */
export const budgetReminderTillDate = cron.schedule(global.config.CRON_JOB_TIME_OBJECT.budgetReminderTillDate, async () => {
  console.log("Running Budget missing reminder cron...");
  /** Get Assign Budgets data */
  const startOfYesterday = moment().subtract(1, 'days').startOf('day').toDate(); // 2025-04-16 00:00:00
  const endOfYesterday = moment().subtract(1, 'days').endOf('day').toDate();     // 2025-04-16 23:59:59

  const getAssignedBudgetData: any = await Forecast.findAll({
    where: {
      forecast_status: forecast_status.ASSIGNED,
      forecast_due_date: {
        [Op.gte]: startOfYesterday,
        [Op.lte]: endOfYesterday
      }
    },
    include: [
      {
        model: ForecastAssignBudget,
        attributes: ['user_id'],
        where: { user_assign_status: user_assign_status.ACTIVE },
        include: [{
          attributes: ['id', 'appToken', 'webAppToken'],
          model: User
        }]
      }, {
        model: Branch,
        as: 'forecast_branch',
        attributes: ['id', 'branch_name']
      }], raw: true, nest: true
  })
  for (const data of getAssignedBudgetData) {
    const user_id = data.ForecastAssignBudgets.user_id
    const user = data.ForecastAssignBudgets.User
    const forecast_year = data.forecast_year
    const getFullName: any = await getUserFullName(user_id)
    const forecast_id = data.id
    const branch_id = data.branch_id

    await createNotification([user], user_id, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.FORECAST_REMINDER_NOTIFICATION.content(getFullName, forecast_year), NOTIFICATIONCONSTANT.FORECAST_REMINDER_NOTIFICATION.heading, REDIRECTION_TYPE.FORECAST, forecast_id, { forecast_id: forecast_id, branch_id: branch_id })
  }
})