import { Router } from "express";
import resignationController from "../../controller/resignation.controller";
import resignationValidator from "../../validators/resignation.validator";

const router: Router = Router();


router.post("/send-resignation", resignationValidator.sendResignation(), resignationController.sendResignation);
router.get("/get-resignation-list", resignationController.getResignationList);
router.get("/get-Leaving-checklist/:resignation_id", resignationController.getLeavingChecklist);
router.get("/get-resignation", resignationController.getResignationById);
router.put("/update-resignation-request/:resignation_id", resignationValidator.updateResignationRequest(), resignationController.updateResignationRequest);
router.put("/verify-Leaving-checklist/:resignation_id", resignationValidator.verifyLeavingCheckList(), resignationController.verifyLeavingCheckList);

export default router;
