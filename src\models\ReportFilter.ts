"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface reportFilterAttributes {
  id: number;
  key: string;
  value: string;
  report_filter_status: string;
  report_filter_type: string;
  created_by: number;
  updated_by: number;
}

export enum report_filter_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export enum report_filter_type {
  GENERAL = "general",
  DAY = "day",
  GENERAL_CUSTOM = "general_custom",
  TIMEPERIOD = "timeperiod"
}

export class ReportFilter
  extends Model<reportFilterAttributes, never>
  implements reportFilterAttributes {
  id!: number;
  key!: string;
  value!: string;
  report_filter_status!: string;
  report_filter_type!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

ReportFilter.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    key: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    value: {
      type: DataTypes.STRING,
      defaultValue: false
    },
    report_filter_status: {
      type: DataTypes.ENUM,
      values: Object.values(report_filter_status),
      defaultValue: report_filter_status.ACTIVE,
    },
    report_filter_type: {
      type: DataTypes.ENUM,
      values: Object.values(report_filter_type),
      allowNull: true
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_report_filters",
    modelName: "ReportFilter",
  },
);


// Define hooks for Card model
ReportFilter.addHook("afterUpdate", async (reportFilter: any) => {
  await addActivity("ReportFilter", "updated", reportFilter);
});

ReportFilter.addHook("afterCreate", async (reportFilter: ReportFilter) => {
  await addActivity("ReportFilter", "created", reportFilter);
});

ReportFilter.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
