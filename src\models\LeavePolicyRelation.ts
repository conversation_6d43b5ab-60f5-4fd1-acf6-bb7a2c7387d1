"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface LeavePolicyRelationAttributes {
    policy_id: number;
    type_id: number;
    days: number;
    duration_type: string;
    has_unlimited: boolean;
    status: string;
    created_by: number;
    updated_by: number;
}

export class LeavePolicyRelationModel
    extends Model<LeavePolicyRelationAttributes, never>
    implements LeavePolicyRelationAttributes {
    public policy_id!: number;
    public type_id!: number;
    public days!: number;
    public status!: string;
    public duration_type!: string;
    public has_unlimited!: boolean;
    public created_by!: number;
    public updated_by!: number;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    DELETED = "deleted"
}

export enum duration_type {
    HOURS = "Hours",
    DAYS = "Days",
}

LeavePolicyRelationModel.init(
    {
        policy_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        type_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        days: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
            defaultValue: status.ACTIVE,
        },
        duration_type: {
            type: DataTypes.ENUM,
            values: Object.values(duration_type),
            allowNull: true,
        },
        has_unlimited: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_leave_policy_relation",
        modelName: "LeavePolicyRelation",
    },
);

LeavePolicyRelationModel.removeAttribute('id');

// Define hooks for leave type model
LeavePolicyRelationModel.addHook("afterUpdate", async (LeavePolicyRelationModel: any) => {
    await addActivity("LeavePolicyRelation", "updated", LeavePolicyRelationModel);
});

LeavePolicyRelationModel.addHook("afterCreate", async (LeavePolicyRelationModel: LeavePolicyRelationModel) => {
    await addActivity("LeavePolicyRelation", "created", LeavePolicyRelationModel);
});

LeavePolicyRelationModel.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});


