"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface mailAttributes {
    id: number;
    mail_from: string;
    mail_to: string;
    mail_body: string;
    mail_attachment: string;
    mail_subject : string;
    mail_status : string;
    mail_response : string;
    created_by: number;
    updated_by: number;
}

export enum mail_status {
    SENT = "sent",
    FAILED = "failed",
    PENDING = "pending"
  }



export class Mail
    extends Model<mailAttributes, never>
    implements mailAttributes {
    id!: number;
    mail_from!: string;
    mail_to!: string;
    mail_body!: string;
    mail_attachment!: string;
    mail_subject! : string;
    mail_status! : string;
    mail_response! : string;
    created_by!: number;
    updated_by!: number;
}

Mail.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        mail_from: {
            type: DataTypes.STRING,
            allowNull: false,
        }, 
        mail_to: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
        }, 
        mail_body: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        }, 
        mail_attachment: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        mail_subject: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        mail_status: {
            type: DataTypes.ENUM,
            values: Object.values(mail_status),
            defaultValue: mail_status.PENDING,
        },
        mail_response : {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_mails",
        modelName: "Mail",
        timestamps: true,
    },
);
