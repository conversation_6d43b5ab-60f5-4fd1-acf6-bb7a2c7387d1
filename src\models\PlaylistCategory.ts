"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { Category } from "./Category";
import { Playlist } from "./Playlist";
import { addActivity } from "../helper/queue.service";

interface playlistCategoryAttributes {
  playlist_id: number;
  category_id: number;
  playlist_category_status: string;
  order: number;
  created_by: number;
  updated_by: number;
}

export enum playlist_category_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class PlaylistCategory
  extends Model<playlistCategoryAttributes, never>
  implements playlistCategoryAttributes {
  playlist_id!: number;
  category_id!: number;
  order!: number;
  playlist_category_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PlaylistCategory.init(
  {
    playlist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    playlist_category_status: {
      type: DataTypes.ENUM,
      values: Object.values(playlist_category_status),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_playlist_category",
    modelName: "PlaylistCategory",
  },
);

PlaylistCategory.removeAttribute("id");
Category.hasMany(PlaylistCategory, {
  foreignKey: "category_id",
  as: "Categories",
});
PlaylistCategory.belongsTo(Category, {
  foreignKey: "category_id",
  as: "Categories",
});
Playlist.hasMany(PlaylistCategory, {
  foreignKey: "playlist_id",
  as: "playlist",
});
PlaylistCategory.belongsTo(Playlist, {
  foreignKey: "playlist_id",
  as: "playlist",
});

PlaylistCategory.addHook("afterUpdate", async (playlistCategory: any) => {
  await addActivity("PlaylistCategory", "updated", playlistCategory);
});

PlaylistCategory.addHook(
  "afterCreate",
  async (playlistCategory: PlaylistCategory) => {
    await addActivity("PlaylistCategory", "created", playlistCategory);
  },
);

PlaylistCategory.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
