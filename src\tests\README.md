# Testing Guide for validateModulePermission Function

This directory contains comprehensive tests for the `validateModulePermission` function that validates user permissions using the new MORole, MOPermission, and MOModule models.

## Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Run Tests
```bash
# Run all tests
npm test

# Run tests in watch mode (re-runs on file changes)
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run only the validateModulePermission tests
npm run test:validatePermission
```

## Test Structure

### Files:
- `validateModulePermission.test.ts` - Main test file with all test cases
- `setup.ts` - Jest setup configuration
- `TEST_CASES.md` - Detailed documentation of all test cases
- `README.md` - This file

### Test Categories:
1. **Successful Permission Validation** (6 tests)
2. **Failed Validation - Missing Parameters** (4 tests)
3. **Failed Validation - User Issues** (4 tests)
4. **Failed Validation - Role Issues** (3 tests)
5. **Failed Validation - Module Issues** (1 test)
6. **Failed Validation - Permission Issues** (3 tests)
7. **Error Handling** (2 tests)
8. **Bitwise Permission Logic** (1 test)

**Total: 24 test cases**

## What the Tests Cover

### ✅ Positive Test Cases:
- User has VIEW permission
- User has CREATE permission
- User has EDIT permission
- User has DELETE permission
- User has combined permissions (CREATE + EDIT)
- Function works with user ID instead of user object

### ❌ Negative Test Cases:
- Missing or invalid parameters
- User not found or inactive
- User in wrong organization
- Role not found or inactive
- Module not found
- No permission record found
- Insufficient permissions
- Database errors

### 🔧 Technical Test Cases:
- Bitwise permission calculations
- Error handling and graceful failures
- Mock validation and isolation

## Expected Output

When tests pass successfully:
```
 PASS  src/tests/validateModulePermission.test.ts
  validateModulePermission
    ✓ Successful permission validation
      ✓ should return true when user has VIEW permission
      ✓ should return true when user has CREATE permission
      ✓ should return true when user has EDIT permission
      ✓ should return true when user has DELETE permission
      ✓ should return true when user has combined permissions
      ✓ should work with user ID instead of user object
    ✓ Failed permission validation - Missing parameters
      ✓ should return false when user is null/undefined
      ✓ should return false when organization_id is missing
      ✓ should return false when module_id is missing
      ✓ should return false when permission_type is undefined
    ... (and so on)

Test Suites: 1 passed, 1 total
Tests:       24 passed, 24 total
```

## Coverage Report

Run `npm run test:coverage` to see:
- **Function Coverage**: Should be 100%
- **Line Coverage**: Should be 95%+
- **Branch Coverage**: Should be 90%+
- **Statement Coverage**: Should be 95%+

## Debugging Tests

### To debug a specific test:
```bash
# Run a specific test file
npm test -- validateModulePermission.test.ts

# Run tests matching a pattern
npm test -- --testNamePattern="should return true"

# Run tests in verbose mode
npm test -- --verbose
```

### Common Issues:
1. **Import Errors**: Make sure all model imports are correct
2. **Mock Issues**: Ensure mocks are properly cleared between tests
3. **Async Issues**: Make sure to use `await` with async functions

## Test Data

The tests use mock data that simulates:
- **User**: Active user in organization with roles
- **Role**: Active role in organization
- **Module**: Valid module in MOModule table
- **Permission**: Permission record linking role to module with specific permissions

## Integration with CI/CD

Add to your CI/CD pipeline:
```yaml
- name: Run Tests
  run: npm test

- name: Generate Coverage
  run: npm run test:coverage

- name: Upload Coverage
  # Upload coverage reports to your preferred service
```

## Extending Tests

To add new test cases:
1. Add the test case to `validateModulePermission.test.ts`
2. Update `TEST_CASES.md` documentation
3. Ensure mocks are properly configured
4. Run tests to verify they pass

## Troubleshooting

### Tests failing?
1. Check that all dependencies are installed: `npm install`
2. Verify Jest configuration in `jest.config.js`
3. Check that mocks are properly set up in test files
4. Ensure the function being tested is exported correctly

### Need help?
- Check the detailed test cases in `TEST_CASES.md`
- Review the function implementation in `src/helper/common.ts`
- Look at the mock setup in the test file
