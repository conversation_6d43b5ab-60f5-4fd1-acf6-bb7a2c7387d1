"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { SideLetterConfirmation } from "./SideLetterConfirmation";

interface sideLetterItemAttributes {
    side_letter_confirmation_id: number;
    item_id: number;
    item_status: string;
    created_by: number;
    updated_by: number;
}

export enum side_letter_item_status {
    ACTIVE = "active",
    DELETED = "deleted"
}


export class SideLetterItem
    extends Model<sideLetterItemAttributes>
    implements sideLetterItemAttributes {
    side_letter_confirmation_id!: number;
    item_id!: number;
    item_status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

SideLetterItem.init(
    {
        side_letter_confirmation_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        item_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        item_status: {
            type: DataTypes.ENUM,
            values: Object.values(side_letter_item_status),
            defaultValue: side_letter_item_status.ACTIVE,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_side_letter_items",
        modelName: "SideLetterItem",
    },
);

SideLetterItem.removeAttribute('id')
// Define relationships
SideLetterItem.belongsTo(SideLetterConfirmation, {
    foreignKey: "side_letter_confirmation_id",
    as: "side_letter_confirmation"
});
SideLetterConfirmation.hasMany(SideLetterItem, {
    foreignKey: "side_letter_confirmation_id",
    as: "side_letter_confirmation_items"
});




// Define hooks for SideLetterItem model
SideLetterItem.addHook("afterUpdate", async (sideLetterItem: any) => {
    await addActivity("SideLetterItem", "updated", sideLetterItem);
});

SideLetterItem.addHook("afterCreate", async (sideLetterItem: SideLetterItem) => {
    await addActivity("SideLetterItem", "created", sideLetterItem);
});

SideLetterItem.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
}); 