"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { PaymentTypeCategory } from "./PaymentTypeCategory";


interface PaymentTypeCategoryFieldAttributes {
  id: number;
  payment_type_category_id: number;
  field_name: string;
  field_type: string;
  field_limit: number;
  payment_type_category_field_status: string;
  created_by: number;
  updated_by: number;
}

export enum payment_type_category_field_status {
  ACTIVE = "active",
  INACTIVE = "inactive"
}



export class PaymentTypeCategoryField
  extends Model<PaymentTypeCategoryFieldAttributes, never>
  implements PaymentTypeCategoryFieldAttributes {
  id!: number;
  payment_type_category_id!: number;
  field_name!: string;
  field_type!: string;
  payment_type_category_field_status!: string;
  field_limit!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PaymentTypeCategoryField.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    payment_type_category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    field_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    field_type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    field_limit: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    payment_type_category_field_status: {
      type: DataTypes.ENUM,
      values: Object.values(payment_type_category_field_status),
      defaultValue: payment_type_category_field_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_payment_type_category_field",
    modelName: "PaymentTypeCategoryField",
  },
);

PaymentTypeCategoryField.belongsTo(PaymentTypeCategory, { foreignKey: "payment_type_category_id", as: "payment_type_category_list" });
PaymentTypeCategory.hasMany(PaymentTypeCategoryField, { foreignKey: "payment_type_category_id", as: "payment_type_category_field" });

// Define hooks for Card model
PaymentTypeCategoryField.addHook("afterUpdate", async (paymentTypeCategoryField: any) => {
  await addActivity("PaymentTypeCategoryField", "updated", paymentTypeCategoryField);
});

PaymentTypeCategoryField.addHook("afterCreate", async (paymentTypeCategoryField: PaymentTypeCategoryField) => {
  await addActivity("PaymentTypeCategoryField", "created", paymentTypeCategoryField);
});

PaymentTypeCategoryField.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

