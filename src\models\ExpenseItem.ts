"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { ExpenseDetail } from "./ExpenseDetail";
import { PaymentTypeCategory } from "./PaymentTypeCategory";

interface expenseItemAttributes {
  id: number;
  expense_detail_id: number;
  payment_type_category_id: number
  expense_amount: number;
  reference_id: number;
  expense_item_status: string;
  created_by: number;
  updated_by: number;
}

export enum expense_item_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}

export class ExpenseItem
  extends Model<expenseItemAttributes, never>
  implements expenseItemAttributes {
  id!: number;
  expense_detail_id!: number;
  payment_type_category_id!: number
  expense_amount!: number;
  reference_id!: number;
  expense_item_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

ExpenseItem.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    expense_detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    expense_amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    reference_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    expense_item_status: {
      type: DataTypes.ENUM,
      values: Object.values(expense_item_status),
      defaultValue: expense_item_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_expense_items",
    modelName: "ExpenseItem",
  },
);

ExpenseItem.belongsTo(ExpenseDetail, { foreignKey: "expense_detail_id", as: "expense_item" });
ExpenseDetail.hasMany(ExpenseItem, { foreignKey: "expense_detail_id", as: "expense_detail" });

ExpenseItem.belongsTo(PaymentTypeCategory, { foreignKey: "payment_type_category_id", as: "expense_item_type" });
PaymentTypeCategory.hasMany(ExpenseItem, { foreignKey: "payment_type_category_id", as: "expense_detail_type" });


// Define hooks for Card model
ExpenseItem.addHook("afterUpdate", async (expenseItem: any) => {
  await addActivity("ExpenseItem", "updated", expenseItem);
});

ExpenseItem.addHook("afterCreate", async (expenseItem: ExpenseItem) => {
  await addActivity("ExpenseItem", "created", expenseItem);
});

ExpenseItem.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

