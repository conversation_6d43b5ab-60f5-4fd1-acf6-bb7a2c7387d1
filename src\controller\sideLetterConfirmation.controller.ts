import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import {
  SideLetterConfirmation,
  side_letter_confirmation_status,
} from "../models/SideLetterConfirmation";
import { Item, item_status } from "../models/Item";
import { createNotification } from "../helper/common";
import { Op } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { User } from "../models/User";
import { sequelize } from "../models";
import { Role } from "../models/Role";
import { NOTIFICATION_TYPE, NOTIFICATIONCONSTANT, ROLE_CONSTANT, REDIRECTION_TYPE, FILE_UPLOAD_CONSTANT } from "../helper/constant";
import moment from "moment";
import {
  side_letter_item_status,
  SideLetterItem,
} from "../models/SideLetterItem";
import { ItemOwner } from "../models/ItemOwner";
import {
  moveFileInBucket,
  deleteFileFromBucket,
} from "../helper/upload.service";

/**
 * Upload side letter confirmation for a DSR
 * @param req
 * @param res
 * @returns
 */
const uploadSideLetterConfirmation = async (req: any, res: Response) => {
  try {
    // Check user role permission
    let findRole: any;
    if (req.headers["platform-type"] == "web") {
      findRole = await Role.findOne({
        attributes: ['role_name'],
        where: {
          id: req.user?.web_user_active_role_id
            ? req.user.web_user_active_role_id
            : req.user.user_active_role_id,
        },
      });
    } else if (
      (req.headers["platform-type"] == "ios" ||
        req.headers["platform-type"] == "android") &&
      req.user.user_active_role_id
    ) {
      findRole = await Role.findOne({
        attributes: ['role_name'],
        where: { id: req.user?.user_active_role_id },
      });
    }

    // Check if the user's role is allowed to upload confirmations
    if (
      ![
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.ACCOUNTANT,
        ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
        ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
      ].includes(findRole?.role_name)
    ) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const {
      side_letter_title,
      side_letter_description,
      has_confirmation_required = true,
    } = req.body;
    const recipient_user_id = Number(req.body.recipient_user_id);

    if (!side_letter_title) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("SIDE_LETTER_TITLE_REQUIRED"),
      });
    }

    if (!recipient_user_id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("RECIPIENT_REQUIRED"),
      });
    }

    const findRecieptUser = await User.findOne({
      attributes: ['id', 'appToken', 'webAppToken'], 
      where: { id: recipient_user_id },
      raw: true
    });
    const findSenderUser = await User.findOne({attributes: ['id', 'user_first_name', 'user_middle_name', 'user_last_name'], where: { id: req.user.id }, raw: true });
    // Handle file upload - main document
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("AT_LEAST_ONE_FILE_REQUIRED"),
      });
    }

    const findTitleExist = await SideLetterConfirmation.findOne({
      where: {
        side_letter_title: side_letter_title,
        side_letter_confirmation_status: {
          [Op.in]: [
            side_letter_confirmation_status.PENDING,
            side_letter_confirmation_status.COMPLETED,
          ],
        },
        recipient_user_id: recipient_user_id,
      },
    });

    if (findTitleExist) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("SIDE_LETTER_TITLE_ALREADY_EXIST"),
      });
    }

    // Create side letter confirmation record
    const sideLetterConfirmation = await SideLetterConfirmation.create({
      side_letter_title,
      side_letter_description: side_letter_description || null,
      sender_user_id: req.user.id,
      recipient_user_id,
      side_letter_confirmation_status: side_letter_confirmation_status.PENDING,
      has_confirmation_required: has_confirmation_required,
      created_by: req.user.id,
      updated_by: req.user.id,
    } as any);

    // Process files from multerS3
    if (sideLetterConfirmation && req.files && req.files.length > 0) {
      // Move files to the proper location if needed
      for (const file of req.files) {
        if (file.isMovable) {
          await moveFileInBucket(
            file.bucket,
            file.path,
            FILE_UPLOAD_CONSTANT.SIDE_LETTER_FILES.destinationPath(
              req.user.organization_id,
              file.filename,
            ),
            file.item_id,
            true,
          );
        }

        // Create SideLetterItem for each file
        await SideLetterItem.create({
          side_letter_confirmation_id: sideLetterConfirmation.id,
          item_id: file.item_id,
          item_status: side_letter_item_status.ACTIVE,
          created_by: req.user.id,
          updated_by: req.user.id,
        } as any);
      }

      const employeeName = [];

      if (findSenderUser?.user_first_name) {
        employeeName.push(findSenderUser.user_first_name);
      }
      if (findSenderUser?.user_middle_name) {
        employeeName.push(findSenderUser.user_middle_name);
      }
      if (findSenderUser?.user_last_name) {
        employeeName.push(findSenderUser.user_last_name);
      }
      // Create notification for recipient
      await createNotification([findRecieptUser], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.SIDE_LETTER_NOTIFICATION.content(employeeName.join(' '), side_letter_title), NOTIFICATIONCONSTANT.SIDE_LETTER_NOTIFICATION.heading, REDIRECTION_TYPE.SIDE_LATTER, sideLetterConfirmation.id, { side_letter_id: sideLetterConfirmation.id, recipient_user_id: recipient_user_id });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SIDE_LATTER_ADDED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.error("Error in uploadSideLetterConfirmation:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Get side letter confirmations with all related items
 * @param req
 * @param res
 * @returns
 */
const getSideLetterConfirmations = async (req: Request, res: Response) => {
  try {
    const {
      page,
      size,
      side_letter_status,
      search,
      recipient_user_id,
      sender_user_id,
    }: any = req.query;
    // Get pagination parameters
    const { limit, offset } = getPagination(Number(page), Number(size));

    const sideLatterConfirmationObj: any = {
      include: [
        {
          model: SideLetterItem,
          as: "side_letter_confirmation_items",
          attributes: [
            "side_letter_confirmation_id",
            [
              sequelize.literal(`(
                          CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE nv_items.id = side_letter_confirmation_items.item_id))
                        )`),
              "item_image_url",
            ],
            [
              sequelize.literal(
                `(SELECT item_name FROM nv_items WHERE nv_items.id = side_letter_confirmation_items.item_id)`,
              ),
              "item_image_name",
            ],
          ],
          where: {
            item_status: side_letter_item_status.ACTIVE,
          },
          required: false,
        },
        {
          model: User,
          as: "recipient_user",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("recipient_user.user_first_name"),
                " ",
                sequelize.col("recipient_user.user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
          ],
        },
        {
          model: User,
          as: "sender_user",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("sender_user.user_first_name"),
                " ",
                sequelize.col("sender_user.user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
          ],
        },
      ],
      order: [["createdAt", "DESC"]],
    };

    // Define the where conditions
    const whereConditions: any = {
      side_letter_confirmation_status: {
        [Op.in]: [
          side_letter_confirmation_status.PENDING,
          side_letter_confirmation_status.COMPLETED,
        ],
      },
    };

    // Add status filter if provided
    if (side_letter_status) {
      whereConditions.side_letter_confirmation_status = side_letter_status;
    }

    // Add recipient filter if provided
    if (recipient_user_id) {
      whereConditions.recipient_user_id = recipient_user_id;
    }

    // Add sender filter if provided
    if (sender_user_id) {
      whereConditions.sender_user_id = sender_user_id;
    }
    if (search) {
      whereConditions.side_letter_title = { [Op.like]: `%${search}%` };
    }

    if (page && size) {
      sideLatterConfirmationObj.limit = Number(limit);
      sideLatterConfirmationObj.offset = Number(offset);
    }

    sideLatterConfirmationObj.where = whereConditions;

    // Get side letter confirmations
    const sideLetterConfirmations = await SideLetterConfirmation.findAll(
      sideLatterConfirmationObj,
    );
    const count: any = await SideLetterConfirmation.count({
      where: whereConditions,
    });
    // Format response - fix type issue with getPaginatedItems
    const { total_pages } = getPaginatedItems(size, page, count || 0);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: sideLetterConfirmations.length > 0 ? sideLetterConfirmations : [],
      count,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });
  } catch (error) {
    console.error("Error in getSideLetterConfirmations:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Get a specific side letter confirmation by ID
 * @param req
 * @param res
 * @returns
 */
const getSideLetterConfirmationById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Validate ID
    if (!id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("SIDE_LETTER_ID_REQUIRED"),
      });
    }

    // Get side letter confirmation
    const sideLetterConfirmation = await SideLetterConfirmation.findOne({
      include: [
        {
          model: SideLetterItem,
          as: "side_letter_confirmation_items",
          attributes: [
            "side_letter_confirmation_id",
            [
              sequelize.literal(`(
                          CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE nv_items.id = side_letter_confirmation_items.item_id))
                        )`),
              "item_image_url",
            ],
            [
              sequelize.literal(
                `(SELECT item_name FROM nv_items WHERE nv_items.id = side_letter_confirmation_items.item_id)`,
              ),
              "item_image_name",
            ],
          ],
          where: {
            item_status: side_letter_item_status.ACTIVE,
          },
        },
        {
          model: User,
          as: "recipient_user",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("recipient_user.user_first_name"),
                " ",
                sequelize.col("recipient_user.user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
          ],
        },
        {
          model: User,
          as: "sender_user",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("sender_user.user_first_name"),
                " ",
                sequelize.col("sender_user.user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
          ],
        },
      ],
      where: {
        id,
        side_letter_confirmation_status: {
          [Op.not]: side_letter_confirmation_status.DELETED,
        },
      },
    });

    if (!sideLetterConfirmation) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("SIDE_LETTER_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: sideLetterConfirmation,
    });
  } catch (error) {
    console.error("Error in getSideLetterConfirmationById:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Update confirmation status (confirm/reject)
 * @param req
 * @param res
 * @returns
 */
const updateConfirmationStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { confirmation_status } = req.query;

    // Validate required fields
    if (!id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("SIDE_LETTER_ID_REQUIRED"),
      });
    }

    // Find confirmation
    const sideLetterConfirmation: any = await SideLetterConfirmation.findOne({
      where: {
        id,
        side_letter_confirmation_status:
          side_letter_confirmation_status.PENDING,
      },
    });

    if (!sideLetterConfirmation) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("SIDE_LETTER_NOT_FOUND_OR_ALREADY_PROCESSED"),
      });
    }

    // Check if user is the recipient
    if (
      sideLetterConfirmation.recipient_user_id !== req.user.id &&
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
      ].includes(req.user.role_name)
    ) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    if (
      !confirmation_status &&
      (sideLetterConfirmation?.has_confirmation_required == true ||
        sideLetterConfirmation?.has_confirmation_required == "true")
    ) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CONFIRMATION_STATUS_REQUIRED"),
      });
    }

    // Update confirmation status
    await sideLetterConfirmation.update(
      {
        side_letter_confirmation_status:
          side_letter_confirmation_status.COMPLETED,
        side_letter_confirmation_date: moment().format("YYYY-MM-DD HH:mm:ss"),
        updated_by: req.user.id,
      } as any,
      { where: { id: id } },
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SIDE_LETTER_APPROVED"),
    });
  } catch (error) {
    console.error("Error in updateConfirmationStatus:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Delete a side letter confirmation and all its items
 * @param req
 * @param res
 * @returns
 */
const deleteSideLetterConfirmation = async (req: Request, res: Response) => {
  try {
    const { id }: any = req.query;

    if (!id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("SIDE_LETTER_ID_REQUIRED"),
      });
    }
    // Handle multiple IDs (comma separated)
    const ids = id
      ? id
          .split(",")
          .map((i: any) => i.trim())
          .filter((i: any) => i)
      : [];
    // Check user role permission
    let findRole: any;
    if (req.headers["platform-type"] == "web") {
      findRole = await Role.findOne({
        where: {
          id: req.user?.web_user_active_role_id
            ? req.user.web_user_active_role_id
            : req.user.user_active_role_id,
        },
      });
    } else if (
      (req.headers["platform-type"] == "ios" ||
        req.headers["platform-type"] == "android") &&
      req.user.user_active_role_id
    ) {
      findRole = await Role.findOne({
        where: { id: req.user?.user_active_role_id },
      });
    }

    // Check if user has permission
    const hasPermission = [ROLE_CONSTANT.SUPER_ADMIN].includes(
      findRole?.role_name,
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const transaction = await sequelize.transaction();
    try {
      for (const singleId of ids) {
        const sideLetterConfirmation = await SideLetterConfirmation.findOne({
          where: {
            id: singleId,
            side_letter_confirmation_status: {
              [Op.not]: side_letter_confirmation_status.DELETED,
            },
          },
          transaction,
        });

        if (!sideLetterConfirmation) {
          continue; // If one not found, skip to next
        }

        // Mark side letter items as deleted
        await SideLetterItem.update(
          {
            item_status: side_letter_item_status.DELETED,
            updated_by: req.user.id,
          },
          {
            where: {
              side_letter_confirmation_id: singleId,
              item_status: side_letter_item_status.ACTIVE,
            },
            transaction,
          },
        );

        // Mark side letter confirmation as deleted
        await sideLetterConfirmation.update(
          {
            side_letter_confirmation_status:
              side_letter_confirmation_status.DELETED,
            updated_by: req.user.id,
          },
          { transaction },
        );

        // Remove files
        const sideLetterItems = await SideLetterItem.findAll({
          where: {
            side_letter_confirmation_id: singleId,
            item_status: side_letter_item_status.DELETED,
          },
          transaction,
        });

        for (const sideLetterItem of sideLetterItems) {
          const item_id = sideLetterItem.item_id;
          const findItem = await Item.findByPk(item_id, { transaction });
          if (findItem) {
            const findItemOwner = await ItemOwner.findAll({
              where: { item_id: item_id },
              transaction,
            });
            if (findItemOwner.length > 1) {
              await ItemOwner.destroy({
                where: {
                  item_id: item_id,
                  owner_id: sideLetterConfirmation?.sender_user_id,
                },
                transaction,
              });
            } else {
              await ItemOwner.destroy({
                where: {
                  item_id: item_id,
                  owner_id: sideLetterConfirmation?.sender_user_id,
                },
                transaction,
              });
              await Item.update(
                { item_status: item_status.DELETED },
                { where: { id: item_id }, transaction },
              );

              await deleteFileFromBucket(
                process.env.NODE_ENV || "development",
                findItem?.item_location,
              );
            }
          }
        }
      }

      await transaction.commit();

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SIDE_LETTER_DELETED_SUCCESSFULLY"),
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error("Error in deleteSideLetterConfirmation:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export default {
  uploadSideLetterConfirmation,
  getSideLetterConfirmations,
  getSideLetterConfirmationById,
  updateConfirmationStatus,
  deleteSideLetterConfirmation,
};
