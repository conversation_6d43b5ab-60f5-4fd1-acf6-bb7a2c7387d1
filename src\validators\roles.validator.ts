import { Segments, Joi, celebrate } from "celebrate";
export default {
    createRole: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_name: Joi.string().required(),
                platform: Joi.number().required(),
                parent_role_id: Joi.number().required(),
            }),
        }),
    updateRole: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_name: Joi.string().allow(null, ""),
                platform: Joi.number().allow(null),
                parent_role_id: Joi.number().allow(null),
            }),
        }),
    createPermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_id: Joi.number().required(),
                module_id: Joi.number().required(),
                platform: Joi.number().required(),
                partial: Joi.boolean().required(),
                permission: Joi.number().required(),
            }),
        }),
    updatePermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_id: Joi.number().required(),
                // module: Joi.string().allow(null, ""),
                // module_name: Joi.string().allow(null, ""),
                platform: Joi.number().allow(null),
                // partial: Joi.boolean().allow(null),
                permissions: Joi.object().allow(null),
            }),
        }),
    copyPermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                from_role: Joi.number().required(),
                to_role: Joi.number().required(),
            }),
        }),
};