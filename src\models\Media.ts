"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface mediaAttributes {
  id: number;
  media_name: string;
  media_type: string;
  media_status: string;
  is_external_link: boolean;
  media_title: string;
  media_description: string;
  created_by: number;
  updated_by: number;
}

export enum media_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export enum media_type {
  IMAGE = "image",
  VIDEO = "video",
  AUDIO = "audio",
  DOC = "doc",
  YOUTUBE = "youtube",
  PDF = "pdf"
}

export class Media
  extends Model<mediaAttributes, never>
  implements mediaAttributes {
  id!: number;
  media_name!: string;
  media_type!: string;
  media_status!: string;
  media_title!: string;
  is_external_link!: boolean;
  media_description!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Media.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    media_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    media_title: {
      type: DataTypes.STRING,
    },
    media_description: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    is_external_link: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    media_status: {
      type: DataTypes.ENUM,
      values: Object.values(media_status),
      defaultValue: media_status.ACTIVE,
    },
    media_type: {
      type: DataTypes.ENUM,
      values: Object.values(media_type),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_media",
    modelName: "Media",
  },
);

Media.addHook("afterUpdate", async (media: any) => {
  await addActivity("Media", "updated", media);
});

Media.addHook("afterCreate", async (media: Media) => {
  await addActivity("Media", "created", media);
});

Media.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

