"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { DsrDetail } from "./DsrDetail";

interface dsrRequestAttributes {
  id: number;
  dsr_detail_id: number;
  user_id: number;
  dsr_request_status: string;
  dsr_amount_total: string;
  request_remark: string;
  old_dsr_amount_total: string;
  created_by: number;
  updated_by: number;
}

export enum dsr_request_status {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = 'rejected',
  DELETED = 'deleted'
}

export class DsrRequest
  extends Model<dsrRequestAttributes, never>
  implements dsrRequestAttributes {
  id!: number;
  dsr_detail_id!: number;
  user_id!: number;
  dsr_request_status!: string;
  dsr_amount_total!: string;
  request_remark!: string;
  old_dsr_amount_total!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

DsrRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    dsr_detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    dsr_request_status: {
      type: DataTypes.ENUM,
      values: Object.values(dsr_request_status),
      defaultValue: dsr_request_status.PENDING,
    },
    dsr_amount_total: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    request_remark: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    old_dsr_amount_total: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_dsr_requests",
    modelName: "DsrRequest",
  },
);


// DsrRequest.belongsTo(User, { foreignKey: "user_id", as: "dsr_request_user" });
// User.hasMany(DsrRequest, { foreignKey: "user_id", as: "user_request" });

DsrRequest.belongsTo(DsrDetail, { foreignKey: "dsr_detail_id", as: "dsr_detail" });
DsrDetail.hasMany(DsrRequest, { foreignKey: "dsr_detail_id", as: "user" });

// Define hooks for Card model
DsrRequest.addHook("afterUpdate", async (dsrRequest: any) => {
  await addActivity("DsrRequest", "updated", dsrRequest);
});

DsrRequest.addHook("afterCreate", async (dsrRequest: DsrRequest) => {
  await addActivity("DsrRequest", "created", dsrRequest);
});

DsrRequest.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

