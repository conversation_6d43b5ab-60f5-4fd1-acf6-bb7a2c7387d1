"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { User } from "./User";
import { Forecast } from "./Forecast";

interface forecastAssignBudgetAttributes {
    forecast_id: number;
    user_id: number;
    user_assign_status: string;
    is_create_budget: boolean;
    created_by: number;
    updated_by: number;
}

export enum user_assign_status {
    ACTIVE = 'active',
    INACTIVE = 'inactive'
}

export class ForecastAssignBudget
    extends Model<forecastAssignBudgetAttributes, never>
    implements forecastAssignBudgetAttributes {
    forecast_id!: number;
    user_id!: number;
    user_assign_status!: string;
    is_create_budget!: boolean;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

ForecastAssignBudget.init(
    {
        forecast_id: {
            type: DataTypes.INTEGER,
        },
        user_id: {
            type: DataTypes.INTEGER,
        },
        user_assign_status: {
            type: DataTypes.ENUM,
            values: Object.values(user_assign_status),
            defaultValue: user_assign_status.ACTIVE,
        },
        is_create_budget: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_forecast_assign_budgets",
        modelName: "ForecastAssignBudget",
    },
);

ForecastAssignBudget.belongsTo(Forecast, { foreignKey: "forecast_id", });
Forecast.hasMany(ForecastAssignBudget, { foreignKey: "forecast_id" });


ForecastAssignBudget.belongsTo(User, { foreignKey: "user_id", });
User.hasMany(ForecastAssignBudget, { foreignKey: "user_id" });


ForecastAssignBudget.belongsTo(User, { foreignKey: "created_by", as: "created_users" });
User.hasMany(ForecastAssignBudget, { foreignKey: "created_by", as: "cerated_user_assign_budget" });

// Define hooks for Card model
ForecastAssignBudget.addHook("afterUpdate", async (forecast: any) => {
    await addActivity("ForecastAssignBudget", "updated", forecast);
});

ForecastAssignBudget.addHook("afterCreate", async (forecast: ForecastAssignBudget) => {
    await addActivity("Forecast", "created", forecast);
});

ForecastAssignBudget.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});
