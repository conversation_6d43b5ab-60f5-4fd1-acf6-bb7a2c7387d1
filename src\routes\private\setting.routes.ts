import { Router } from "express";
import settingController from "../../controller/setting.controller";
import { multerS3 } from "../../helper/upload.service";
const router: Router = Router();

import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";

// Initialize multerS3 uploader with proper error handling
const multerS3Upload = multerS3(
  process.env.NODE_ENV || "development",
  FILE_UPLOAD_CONSTANT.ORG_SETTINGS.folder,
);

router.post(
  "/add",
  multerS3Upload.fields([{ name: "brand_logo", maxCount: 1 }]),
  settingController.createSetting,
);

router.get("/list", settingController.getSetting);

router.post(
  "/branch-setting",
  multerS3Upload.upload("brand_logo"),
  settingController.createBranchWiseSetting,
);

router.get(
  "/get-branch-setting/:branch_id",
  settingController.getBranchWiseSetting,
);

router.get(
  "/get-health-safety-category",
  settingController.getHealthSafetyCategory,
);

router.get("/get-branch-activity/:branch_id", settingController.getBranchSettingUpdateHistory);

export default router;
