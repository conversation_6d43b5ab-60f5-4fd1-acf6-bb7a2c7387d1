import { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";
import { UserSession } from "../models/UserSession";
// function to generate token
const generateToken = async function (user: any) {
  try {
    const payload = user;
    const token = jwt.sign(payload, global.config.JWT_SECRET, {
      expiresIn: global.config.JWT_EXIPIRATION_TIME,
    });
    if (process.env.NEXT_NODE_ENV !== "staging" && payload.id !== 1) {
      payload.device_type =
        payload.device_type == "ios" ? "android" : payload.device_type;
      // Invalidate previous sessions on the same device type
      await UserSession.destroy({
        where: { user_id: payload.id, device_type: payload.device_type },
      });
      // Store the new session in the database
      await UserSession.create({
        user_id: payload.id,
        device_type: payload.device_type,
        token,
      } as any);
    }
    return token;
  } catch (e: any) {
    console.log(e);
  }
};

const generateRefreshToken = async function (
  req: Request,
  res: Response,
  next?: NextFunction,
) {
  const user = req.user;

  // <PERSON>reate signed refresh token
  const refreshToken = jwt.sign(
    {
      id: user.id.toString(),
    },
    global.config.REFRESH_TOKEN_SECRET,
    {
      expiresIn: global.config.REFRESH_TOKEN_EXPIRE,
    },
  );
  // const iv = crypto.randomBytes(16);
  // let cipher = crypto
  //   .createCipheriv("aes-256-ccm", global.config.REFRESH_TOKEN_SECRET, iv)
  //   .update(refreshToken, "utf-8", "hex");

  res.cookie("refresh_token", refreshToken);
  if (next) {
    next();
  } else {
    return;
  }
};

export { generateToken, generateRefreshToken };
