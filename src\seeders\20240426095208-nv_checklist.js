"use strict";

const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const getCheckList = await queryInterface.sequelize.query(
      `SELECT * FROM nv_checklist`,
      { type: QueryTypes.SELECT },
    );
    if (getCheckList.length == 0) {
      await queryInterface.bulkInsert(
        "nv_checklist",
        [
          {
            checkList_name: "RIGHT TO WORK CHECKLIST",
            order: 1,
            type: 2,
            prefix: "RTWC",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "HMRC & NEW STARTER FORM",
            order: 2,
            type: 2,
            prefix: "NS&HMRCF",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "HEALTH & SAFETY INDUCTION",
            order: 4,
            type: 2,
            prefix: "HSI",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "EMPLOYMENT CONTRACT",
            order: 3,
            type: 2,
            prefix: "EC",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "OTHER",
            order: 5,
            type: 2,
            prefix: "OTHER",
            checklist_status: 'inactive',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Right to work Checklist",
            order: 6,
            type: 1,
            checklist_type: "joining",
            checklist_status: 'active',
            prefix: "RIWCA",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "New Starter Form & Latest HMRC Form",
            order: 7,
            type: 1,
            prefix: "NSHMRC",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "National Insurance",
            order: 8,
            type: 1,
            prefix: "NI",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Address Proof ",
            order: 9,
            type: 1,
            prefix: "AP",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Contract of employment",
            order: 10,
            type: 1,
            prefix: "CE",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name:
              "Health and safety form, Fire safety form (together)",
            order: 11,
            type: 1,
            prefix: "HSFT",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name:
              "Business Induction (History, Ethics and Key personnel)",
            order: 12,
            type: 1,
            prefix: "BIEP",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "video training",
            order: 13,
            type: 1,
            prefix: "VT",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Premises tour",
            order: 14,
            type: 1,
            prefix: "PT",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Adding Clock in/out system access",
            order: 15,
            type: 1,
            prefix: "EC",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Add into holiday drive & check future holiday",
            order: 16,
            type: 1,
            prefix: "HDFH",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Whatsapp group add",
            order: 17,
            type: 1,
            prefix: "WG",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Key allocation form",
            order: 18,
            type: 1,
            prefix: "KAF",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Add into HR Compliance Sheet",
            order: 19,
            type: 1,
            prefix: "HRCS",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name:
              "Adding into Payroll (***Sending details to Accounts department after 1 week completion)",
            order: 20,
            type: 1,
            prefix: "FNM",
            checklist_type: "joining",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Written Advance notice (letter/email)",
            order: 21,
            type: 1,
            prefix: "WAN",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Keys obtained",
            order: 22,
            type: 1,
            prefix: "KO",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Remove from Whatsapp",
            order: 23,
            type: 1,
            prefix: "ROW",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Final calculation of wages/holiday",
            order: 24,
            type: 1,
            prefix: "FCWH",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "P45 from accountant",
            order: 25,
            type: 1,
            prefix: "PFA",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name:
              "Remove from Clock-in/Out System - ***After the final salary***",
            order: 26,
            type: 1,
            prefix: "AFS",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Remove from Payroll submission sheet",
            order: 27,
            type: 1,
            prefix: "RCPH",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Remove from HR Compliance Sheet",
            order: 28,
            type: 1,
            prefix: "RHC",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Remove from Holiday Sheet",
            order: 29,
            type: 1,
            prefix: "RFHS",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            checkList_name: "Records Retained in Ex-Employee File",
            order: 30,
            type: 1,
            prefix: "RXF",
            checklist_type: "leaving",
            checklist_status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        {},
      );
    }
  },
};
