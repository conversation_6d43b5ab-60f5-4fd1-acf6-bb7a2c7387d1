<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee contract</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Calibri:400,700,400italic,700italic" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Symbols:wght@100..900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: Calibri, Candara, Segoe, "Segoe UI", <PERSON><PERSON><PERSON>, <PERSON><PERSON>, "Noto Sans Symbols", sans-serif;
            margin: 20px;
        }

        header {
            background-color: #4CAF50;
            color: white;
            padding: 10px 0;
            text-align: center;
        }

        nav {
            margin: 20px 0;
        }

        nav a {
            margin: 0 10px;
            text-decoration: none;
            color: #4CAF50;
        }

        section {
            margin: 20px 0;
        }

        footer {
            text-align: center;
            margin: 20px 0;
            color: #888;
        }

        .pdf-watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            opacity: 0.2;
            font-size: 100px;
            color: #ccc;
        }

        .m0 {
            margin: 0px !important;
        }

        .mt20 {
            margin-top: 20px !important;
        }

        .ml20 {
            margin-left: 20px !important;
        }

        .ml32 {
            margin-left: 32px !important;
        }

        .mt40 {
            margin-top: 40px !important;
        }

        .ml40 {
            margin-left: 40px !important;
        }

        .mt8 {
            margin-top: 8px !important;
        }

        .mb8 {
            margin-bottom: 8px !important;
        }

        .pt2 {
            padding-top: 2px !important;
        }

        .pt4 {
            padding-top: 4px !important;
        }

        .pt6 {
            padding-top: 6px !important;
        }

        .pt8 {
            padding-top: 8px !important;
        }

        .pt20 {
            padding-top: 20px !important;
        }

        .pt16 {
            padding-top: 16px;
        }

        .pt32 {
            padding-top: 32px !important;
        }

        h6,
        p {
            margin: 0;
        }

        .fw600 {
            font-weight: 600;
        }

        .fw400 {
            font-weight: 400;
        }

        .w90 {
            width: 90%;
        }

        .text-align {
            text-align: center !important;
        }

        ul {
            margin: 0;
        }

        .p9 {
            font-family: Calibri, Candara, Segoe, "Segoe UI", Optima, Arial, "Noto Sans Symbols", sans-serif !important;
            font-size: 9px !important;
        }

        .p10 {
            font-family: Calibri, Candara, Segoe, "Segoe UI", Optima, Arial, "Noto Sans Symbols", sans-serif !important;
            font-size: 10px !important;
        }

        .p12 {
            font-family: Calibri, Candara, Segoe, "Segoe UI", Optima, Arial, "Noto Sans Symbols", sans-serif !important;
            font-size: 12px !important;
            /* line-height: 18px !important;
            letter-spacing: -0.5px !important; */
        }

        .p14 {
            font-family: Calibri, Candara, Segoe, "Segoe UI", Optima, Arial, "Noto Sans Symbols", sans-serif !important;
            font-size: 14px !important;
            /* line-height: 21px !important; */
            /* letter-spacing: -0.5px !important; */
        }

        .p18 {
            font-family: Calibri, Candara, Segoe, "Segoe UI", Optima, Arial, "Noto Sans Symbols", sans-serif !important;
            font-size: 18px !important;
            line-height: 24px !important;
            letter-spacing: -0.5px !important;
        }

        .text-start {
            text-align: start;
        }

        .flex-column {
            flex-direction: column;
        }

        .align-items-baseline {
            align-items: baseline;
        }

        .text-underline {
            text-underline-offset: 3px;
        }

        .pdf-generate-checkbox-sub-text {
            font-size: 16px;
            font-weight: 400;
        }

        .pdf-generate-checkbox-sub-text::before {
            content: "";
            margin-right: 10px;
            border: 1px solid black;
            display: inline-block;
            height: 15px;
            width: 12px;
        }

        .name-wrapper {
            border: 1px solid #000000;
            display: grid;
            grid-template-columns: 3fr 2fr 3fr;
            height: 60px;
        }

        .checkbox {
            margin-right: 10px;
            margin-left: 5px;
            height: 18px;
            width: 18px;
        }

        .radiobox {
            margin-right: 10px;
            margin-left: 5px;
            height: 18px;
            width: 18px;
        }

        .input-field {
            height: 20px;
        }

        .bank-information .bank-details-table .bank-address {
            height: 80px;
        }

        .grid-2container {
            display: grid;
            grid-template-columns: 2fr 2fr;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        td {
            border: 1px solid black;
            padding: 4px;
            text-align: left;
            vertical-align: top;
            width: 50%;
        }

        .additional-personal-information .additional-other-details {
            position: relative;
            border: 1px solid black;
            height: 80px;
        }

        .professional-information .professional-details-table .table-row {
            height: 80px;
        }

        .signature-wrapper .signature-content {
            display: grid;
            grid-template-columns: 3fr 2fr;
        }

        .health-safety-information .health-safety-table td {
            padding: 0px;
        }

        .w70 {
            width: 70%;
        }

        .w30 {
            width: 30%;
        }

        .bold-divider {
            border: 1px solid #000000;
            width: 100%;
        }

        .square-number {
            border: 1px solid #000000;
            padding: 0px 5px 0px 20px;
            margin-right: 5px;
        }

        .custom-input-field {
            padding: 13px 10px;
            border-style: solid;
            border-color: #000000;
            border-width: 1px 1px 0px 1px;
            position: relative;
        }

        .custom-field-value {
            position: absolute;
            top: 3px;
            left: 5px;
            line-height: 24px !important;
        }

        .one-line-ellipsis {
            word-break: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        .two-line-ellipsis {
            word-break: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }

        .three-line-ellipsis {
            word-break: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
        }

        .field-text-placeholder {
            position: absolute;
            color: gray;
            top: 5px;
            left: 5px;
        }

        .border-b {
            border-bottom: 1px solid #000000;
        }

        .hmrc-header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 28px !important;
        }

        .hmrc-header-logo {
            width: 130px;
            height: 100%;
        }

        .hmrc-header-logo img {
            width: 100%;
            height: 100%;
        }

        .hmrc-information-wrapper .number-field-container .checkbox {
            margin-right: 0px;
        }

        .hmrc-information-wrapper .statement-information .statement-details-table td {
            width: 33.33%;
        }

        .footer-wapper {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            text-align: center;
            align-items: center;
        }

        .employee-contract-wrapper {
            padding: 40px 80px 20px 80px;
        }

        .nv-header-logo {
            padding-bottom: 20px;
            max-width: 122px;
            max-height: 120px;
            margin: auto;
        }

        /* .nv-header-logo img {
            width: 100px;
            height: 60px;
        } */

        .calibri-font {
            font-family: Calibri, Candara, Segoe, "Segoe UI", Optima, Arial, "Noto Sans Symbols", sans-serif !important;
        }

        .arial-font {
            font-family: Calibri, Candara, Segoe, "Segoe UI", Optima, Arial, "Noto Sans Symbols", sans-serif !important;
        }

        .MuiTypography-root {
            font-family: Calibri, Candara, Segoe, "Segoe UI", Optima, Arial, "Noto Sans Symbols", sans-serif !important;
        }

        /* ul li::marker {
            font-size: 14px;
        } */

        .emp-contents .nestedList {
            list-style-type: none;
            padding-left: 1rem;
        }

        .emp-contents .nestedList .nestedList {
            padding-top: 5px;
        }

        .emp-list-data .nestedList {
            list-style-type: none;
            padding-left: 1rem;
            padding-top: 1rem;
        }

        .emp-list-data .nestedList li {
            padding-top: 1rem;
        }

        .emp-list-data .nestedList li:first-child {
            padding-top: 0px;
        }

        .emp-list-data .nestedList .description-list li {
            list-style-type: disc;
            font-size: 12px;
            padding-top: 0px;
            line-height: 18px;
        }

        .emp-signature-wrapper .emp-signature-content {
            display: grid;
            grid-template-columns: 2fr 2fr 2fr;
        }

        .emp-signature-wrapper .emp-signature-content .text-sign-border {
            border-bottom: 1px solid black;
            width: 50%;
            margin-top: 50px;
            margin-bottom: 40px;
        }

        .emp-signature-wrapper .emp-signature-content-other .text-sign-border {
            display: inline-flex;
            border-bottom: 1px solid black;
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .emp-signature-wrapper .emp-signature-content .flex-center {
            display: flex;
            align-items: flex-end;

        }

        .emp-signature-wrapper .emp-signature-content .signature-content {
            width: 100% !important;
            height: 95px !important;
        }

        .emp-contract-footer {
            display: grid;
            grid-template-columns: 1fr;
            text-align: center;
            align-items: center;
        }

        .emp-information-content {
            page-break-before: always;
        }

        .page-break-avoid {
            page-break-inside: avoid;
        }

        .emp-information-wrapper {
            padding-top: 40px;
        }

        @media print {
            .page-break-avoid {
                page-break-inside: avoid;
            }

        }
    </style>
</head>

<body>

    <div>
        <div class="employee-contract-wrapper">
            <div class="pb32 text-align">
                <div class="nv-header-logo">
                    <img src="{{NAMASTE_LOGO}}"
                        style="border:0;display:block;outline:none;text-decoration:none;height:auto;width:100%;font-size:13px;">
                </div>
                <div class="text-align pt8">
                    <h6 class="p14 fw600">Contract Of Employment</h6>
                    {{#if branch_heading_name}}
                    <h6 class="p14 pt6 fw600">
                        <span class="p14 fw600">
                            {{branch_heading_name}}</span>
                    </h6>
                    {{/if}}
                    {{#if registration_number}}
                    <h6 class="p10 pt4 fw600">Company Registration No. - <span class="p10 fw600">
                            {{registration_number}}</span>
                    </h6>
                    {{/if}}
                    {{#if branch_heading_work_place}}
                    <h6 class="p10 pt4 fw600">Registered Office Address:<span class="p10 fw600">
                            {{branch_heading_work_place}}</span>
                    </h6>
                    {{/if}}
                </div>
            </div>
            <div class="emp-information-wrapper">
                <div class="">
                    {{#if branch_heading_employer_name}}
                    <h6 class="p14 fw600">Employer’s
                        name:<span variant="h6" class="p14 fw400"> {{branch_heading_employer_name}}</span></h6>
                    <h6 class="p14 fw400">(herein referred to as “the company” or “we”)</h6>
                    {{/if}}
                    {{#if employee_name}}
                    <h6 class="p14 pt20 fw600">Employee
                        Name:<span variant="h6" class="p14 fw400"> {{employee_name}}</span></h6>
                    <h6 class="p14 fw400">(herein referred to as “you”)</h6>
                    {{/if}}
                    {{#if job_title}}
                    <h6 class="p14 pt20 fw600">Job
                        Title:<span variant="h6" class="p14 fw400"> {{job_title}}</span></h6>
                    {{/if}}
                    {{#if work_place}}
                    <h6 class="p14 pt20 fw600">Main
                        place of work:<span variant="h6" class="p14 fw400"> {{work_place}}</span></h6>
                    {{/if}}
                    {{#if employee_address}}
                    <h6 class="p14 pt20 fw600">Employee
                        Address:<span variant="h6" class="p14 fw400"> {{employee_address}}</span></h6>
                    {{/if}}
                    <!-- {{#if nationality}}
                    <h6 class="p14 pt8 fw600">Nationality:<span variant="h6" class="p12 pl8 fw400">
                            {{nationality}}</span></h6>
                    {{/if}} -->
                    {{#if insurance_number}}
                    <h6 class="p14 pt20 fw600">National
                        Insurance number:<span variant="h6" class="p14 fw400"> {{insurance_number}}</span></h6>
                    {{/if}}
                    {{#if joining_date}}
                    <h6 class="p14 pt20 fw600">Date of
                        commencement of employment:<span variant="h6" class="p14 fw400"> {{joining_date}}</span>
                    </h6>
                    {{/if}}
                    <!-- <h6 class="p14 pt8 fw600">Contract Start From:<span variant="h6" class="p12 pl8 fw400">
                            {{start_date}}</span>
                    </h6>
                    <h6 class="p14 pt8 fw600">Contract Expire At:<span variant="h6" class="p12 pl8 fw400">
                            {{expire_date}}</span> -->
                    <!-- </h6> -->
                    <!-- <h6 class="p14 pt8 fw600">Duties
                        and responsibilities:<span variant="h6" class="p12 pl8"></span></h6> -->
                    <div class="duties-responsibilities-container">
                        {{#if deptContent}}
                        <div class="pt20">
                            {{{deptContent}}}
                        </div>
                        {{/if}}
                        {{#if addittionalContent}}
                        <h6 class="p14 pt20 fw600">
                            Addittional Duties : <span class="p14 pt16 fw400">{{{addittionalContent}}}</span>
                        </h6>
                        {{/if}}
                        {{#if otherContent}}
                        <div class="pt20">
                            {{{otherContent}}}
                        </div>
                        {{/if}}
                        <div class="p14 pt20">
                            Please note that the above-included list of duties and responsibilities is intended to be a
                            representative reflection of the job description but is NOT exhaustive.
                        </div>
                        <div class="p14 pt20">
                            The Company reserves the right to revise the job or require that other supporting tasks and
                            duties be performed from time to time. The other tasks and duties assigned may be outside
                            your normal role-related activities and may also require you to work at locations other than
                            your designated place of work.
                        </div>
                        {{#if contract_type_name}}
                        <h6 class="p14 pt16 fw600">
                            Type of Contract: <span class="p14 fw400"> {{contract_type_name}} </span></h6>
                        {{/if}}
                        {{#if probation_period}}
                        <h6 class="p14 pt16 fw600">
                            Probation Period:<span class="p14 fw400"> {{probation_period}} Days</h6>
                        {{/if}}
                        {{#if contract_type}}
                        <h6 class="p14 pt16 fw600">
                            Hours of work: <span class="p14 fw400"> {{contract_type.working_hours}} Hours per
                                {{#eqmark contract_type.duration_type "week"}} Week {{/eqmark}}
                                {{#eqmark contract_type.duration_type "month"}} Month {{/eqmark}} </span></h6>
                        {{/if}}
                        {{#if wages_per_hours}}
                        <h6 class="p14 pt16 fw600">
                            Salary:<span class="p14 fw400"> £{{wages_per_hours}} {{wages_type}}
                                {{# if fixed_types}} / {{fixed_types}} {{/if}} </span></h6>
                        {{/if}}
                        {{#if tips_grade}}
                        <h6 class="p14 pt16 fw600">
                            TipsGrade:<span class="p14 fw400"> {{tips_grade}}</h6>
                        {{/if}}
                        {{#if annual_holiday}}
                        <h6 class="p14 pt16 fw600">
                            Annual Holiday:<span class="p14 fw400"> {{annual_holiday.days}}
                                {{annual_holiday.durationType}}
                                (including bank holiday) </span>
                        </h6>
                        {{/if}}
                        <!-- <ul class="pt16">
                            <li>
                                <h6 class="p12">Serve all customers in line with service flow set up by the management
                                </h6>
                            </li>
                            <li>
                                <h6 class="p12">The set up and maintenance of all restaurant service areas.</h6>
                            </li>
                            <li>
                                <h6 class="p12">Welcoming of members and guests to the restaurants.</h6>
                            </li>
                            <li>
                                <h6 class="p12">All enquires made in person or over the phone must be handled to the
                                    standards of the restaurants.</h6>
                            </li>
                            <li>
                                <h6 class="p12">Serving of meals/drinks to members and guests.</h6>
                            </li>
                            <li>
                                <h6 class="p12">To notify the Manager or his/her deputy where stocks/supplies are low.
                                </h6>
                            </li>
                            <li>
                                <h6 class="p12">To ensure that all duties are carried out in accordance with statutory
                                    and/or Group Health and Safety requirements, including food hygiene regulations.
                                </h6>
                            </li>
                            <li>
                                <h6 class="p12">To adhere to all Restaurant standards in every interaction with members
                                    and guests, colleagues, other members of the team and external suppliers and
                                    contacts.</h6>
                            </li>
                            <li>
                                <h6 class="p12">To manage all payment transactions in accordance with the Restaurant
                                    financial policies and procedures and to handle all transactions with diligence,
                                    honesty and integrity.
                                </h6>
                            </li>
                            <li>
                                <h6 class="p12">To support colleagues at peak times and to undertake any operational
                                    duty which might be reasonably required, to ensure customer expectations are met.
                                </h6>
                            </li>
                            <li>
                                <h6 class="p12">To participate, constructively, in performance reviews with the Manager
                                    and to work towards objectives/goals set by the Manager, and to improve any areas of
                                    performance felt necessary by the Manager, in order to maintain an excellent level
                                    of service to customers.</h6>
                            </li>
                            <li>
                                <h6 class="p12">To assist in the receipt of deliveries when required.</h6>
                            </li>
                            <li>
                                <h6 class="p12">Attend all team meetings &amp; provide valuable inputs </h6>
                            </li>
                        </ul> -->

                        <!-- <h6 class="p12 pt8">*Also, the
                            employer may require you to carry out other reasonable duties as required.</h6>
                        <h6 class="p12 pt8 fw600">
                            Hours of work: Flexible Hours / Maximum 20 hours per week /Full Time/ Zero Hour/ Part Time
                        </h6> -->

                    </div>
                    <!-- <div class="emp-contract-footer" style="margin-top: 30px;">
                        <h6 class="p12 pt4 arial-font">
                            1</h6>
                    </div> -->
                </div>
                <!-- <div class="mt40 pt8 mb8">
                    <p class="p14 fw600"><span class="text-underline">NOTE:</span> Please read and understand this
                        attached document
                        carefully. Should you have any queries or should you not understand anything regarding this
                        contract please contact us for clarification. Not understanding
                        these terms and that of counterpart documents through ignorance or language will not be an
                        acceptable form of argument.</p>
                    <p class="p14 fw600 pt16">
                        Contents</p>
                    <div class="emp-contents pt16 grid-2container">
                        <ul class="nestedList">
                            <li>
                                <p class="p12 fw600">
                                    1.0<span> Staff Guide</span></p>
                                <ul class="nestedList">
                                    <li>
                                        <p class="p12 fw600">
                                            1.1<span> Introduction</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.2<span> Mobile phones</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.3<span> Keys</span></p>
                                        <ul class="nestedList">
                                            <li>
                                                <p class="p12 fw600">
                                                    1.3.1<span> On-site keys</span></p>
                                            </li>
                                            <li>
                                                <p class="p12 fw600">
                                                    1.3.2<span> Staff issued keys</span></p>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.4<span> Card transactions</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.5<span> Teamwork &amp; Flexibility</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.6<span> Preparation and cleaning</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.7<span> Personal details</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.8<span> Training</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.9<span> Observation days</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.10<span> Communication</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            1.11<span> Equal opportunities</span></p>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    2.0<span> Uniform</span></p>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    3.0<span> Breaks</span></p>
                                <ul class="nestedList">
                                    <li>
                                        <p class="p12 fw600">
                                            3.1<span> Presence at work</span></p>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    4.0<span> Resignation/Termination</span></p>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    5.0<span> Pay</span></p>
                                <ul class="nestedList">
                                    <li>
                                        <p class="p12 fw600">
                                            5.1<span> Clock in/ou</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            5.2<span> Pay periods</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            5.3<span> National insurance &amp; tax</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            5.4<span> Wage deductions</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            5.5<span> Final pay</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            5.6<span> Pensions</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            5.7<span> Tips</span></p>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    6.0<span> Time off</span></p>
                                <ul class="nestedList">
                                    <li>
                                        <p class="p12 fw600">
                                            6.1<span> Holiday</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            6.2<span> Day(s) off</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            6.3<span> Bank holidays</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            6.4<span> Pay</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            6.5<span> SMP, SPP &amp; SSP</span></p>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    7.0<span> Staff discount/Purchase</span></p>
                            </li>
                        </ul>
                        <ul class="nestedList">
                            <li>
                                <p class="p12 fw600">
                                    8.0<span> Safe practice</span></p>
                                <ul class="nestedList">
                                    <li>
                                        <p class="p12 fw600">
                                            8.1<span> Posters</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            8.2<span> Personal hygiene</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            8.3<span> Gross misconduct through health &amp; safety</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            8.4<span> Reporting Sickness</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            8.5<span> Accidents</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            8.6<span> Health &amp; safety</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            8.7<span> Drinking at work</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            8.8<span> Gross misconduct, restricted sales</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            8.9<span> Medical examinations</span></p>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    9.0<span> Management sheets</span></p>
                                <ul class="nestedList">
                                    <li>
                                        <p class="p12 fw600">
                                            9.1<span> Daily sheets</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            9.2<span> Responsibility</span></p>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    10.0<span> Accessibility</span></p>
                                <ul class="nestedList">
                                    <li>
                                        <p class="p12 fw600">
                                            10.1<span> Documents</span></p>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    11.0<span> Grievances</span></p>
                                <ul class="nestedList">
                                    <li>
                                        <p class="p12 fw600">
                                            11.1<span> Purpose</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            11.2<span> Fairness &amp; transparency</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            11.3<span> Formal action</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            11.4<span> Procedures</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            11.5<span> CODE OF PRACTICE 1</span></p>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <p class="p12 fw600">
                                    12.0<span> Terms</span></p>
                                <ul class="nestedList">
                                    <li>
                                        <p class="p12 fw600">
                                            12.1<span> Termination of employment contract</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            12.2<span> Severability</span></p>
                                    </li>
                                    <li>
                                        <p class="p12 fw600">
                                            12.3<span> Other important notes</span></p>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                    <div class="emp-contract-footer" style="margin-top: 200px;">
                        <h6 class="p12 pt4 arial-font">
                            2</h6>
                    </div>
                </div> -->
                {{#if generalContent}}
                <div class="pt20 mb8">
                    {{{generalContent}}}
                </div>
                {{/if}}
                <div class="emp-signature-wrapper pt32">
                    <div class="page-break-avoid">
                        <div class="emp-signature-content">
                            <h6 class="p12">Employee
                                Print Name:</h6>
                            <h6 class="p12">Employee
                                Sign:</h6>
                            <h6 class="p12">Date:</h6>
                        </div>
                        <div class="emp-signature-content">
                            <div class="text-sign-border flex-center">
                                <span class="p12">{{employee_name}}</span>
                            </div>
                            <div class="text-sign-border">
                                {{#if employee_sign }}
                                <img class="signature-content" src="{{employee_sign}}"
                                    style="border:0;display:block;outline:none;text-decoration:none;height:auto;width:100%;font-size:13px;">
                                {{else}}
                                <span></span>
                                {{/if}}
                            </div>
                            <div class="text-sign-border flex-center">
                                <span class="p12">
                                    {{date}}
                                </span>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="page-break-avoid">
                        <div class="emp-signature-content">
                            <h6 class="p12">Employer
                                Print Name:</h6>
                            <h6 class="p12">Employer
                                Sign:</h6>
                            <h6 class="p12">Date:</h6>
                        </div>
                        <div class="emp-signature-content">
                            <div class="text-sign-border flex-center"> <span class="p12">{{employer_name}}</span>
                            </div>
                            <div class="text-sign-border">
                                {{#if employer_sign }}
                                <img src="{{employer_sign}}" class="signature-content"
                                    style="border:0;display:block;outline:none;text-decoration:none;height:auto;width:100%;font-size:13px;">
                                {{else}}
                                <span></span>
                                {{/if}}
                            </div>
                            <div class="text-sign-border flex-center">
                                <span class="p12">
                                    {{date}}
                                </span>
                            </div>
                        </div>
                    </div>-->

                    <div class="page-break-avoid">
                        <div class="emp-signature-content-other">
                            <h6 class="p12">Employer Sign:
                                <span class="text-sign-border">
                                    {{#if employer_sign }}
                                    <img src="{{employer_sign}}" class="signature-content"
                                        style="border:0;display:block;outline:none;text-decoration:none;max-height:80px;max-width:150px;width:100%;font-size:13px;">
                                    {{else}}
                                    <span></span>
                                    {{/if}}
                                </span>
                            </h6>

                            <h6 class="p12"><span class="mt8 mb8">Signed on behalf of the company: </span>
                            </h6>

                            <h6 class="p12">Employer Print Name:
                                <span class="text-sign-border p12">{{employer_name}}</span>
                            </h6>

                            <h6 class="p12">Date:
                                <span class="text-sign-border flex-center">
                                    <span class="p12">
                                        {{date}}
                                    </span>
                                </span>
                            </h6>

                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</body>

</html>