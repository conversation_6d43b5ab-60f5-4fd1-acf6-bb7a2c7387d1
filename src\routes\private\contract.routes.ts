import { Router } from "express";
import contractController from "../../controller/contract.controller";
import templateValidator from "../../validators/contract.validator";

const router: Router = Router();

router.post(
  "/create",
  templateValidator.createFile(),
  contractController.createOrUpdateFile,
);
router.get("/category", contractController.getFolders);
router.get("/templates", contractController.getAllFiles);
router.put("/forms", contractController.regenerateContractAfterUpdate);
router.put("/copy/:id", contractController.copyInDuplicateFile);
router.put(
  "/move/:id",
  templateValidator.moveFile(),
  contractController.moveFileToCategory,
);
router.get("/template/versions", contractController.getAllFileVersions);
router.get("/forms/versions", contractController.getAllUserContractVersion);
router.delete(
  "/templates",
  templateValidator.deleteFile(),
  contractController.deleteFilesFolder,
);

router.post(
  "/contract-type/:id?",
  templateValidator.createContractType(),
  contractController.createOrUpdateContractType,
); // Create or Update
router.get("/contract-types", contractController.getContractTypes); // List
router.delete("/contract-type/:id", contractController.deleteContractType); // Delete

router.post(
  "/job-role/:id?",
  templateValidator.createJobRole(),
  contractController.createOrUpdateJobRole,
); // Create or Update
router.get("/job-roles", contractController.getJobRoles); // List
router.delete("/job-role/:id", contractController.deleteJobRole); // Delete

router.put(
  "/update-user-contract",
  templateValidator.updateUserContract(),
  contractController.updateUserContract,
);

router.post('/contract-user-type', templateValidator.createContractName(), contractController.createContractName); // Create or Update
router.get('/get-contract-user-type', contractController.getContractName); // Get contract
router.delete('/delete-contract-history', contractController.deleteUserContractHistory); // Delete
router.post('/get-users-by-template-group', contractController.getUsersByTemplateGroups)

export default router;
