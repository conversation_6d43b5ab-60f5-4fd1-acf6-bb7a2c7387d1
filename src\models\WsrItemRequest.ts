"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { PaymentTypeCategory } from "./PaymentTypeCategory";
import { WsrRequest } from "./WsrRequest";

interface wsrItemRequestAttributes {
  id: number;
  wsr_request_detail_id: number;
  payment_type_category_id: number
  wsr_amount: number;
  reference_id: number;
  wsr_request_item_status: string;
  old_wsr_amount: number
  created_by: number;
  updated_by: number;
}

export enum wsr_request_item_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}


export class WsrItemRequest
  extends Model<wsrItemRequestAttributes, never>
  implements wsrItemRequestAttributes {
  id!: number;
  wsr_request_detail_id!: number;
  payment_type_category_id!: number
  wsr_amount!: number;
  reference_id!: number;
  wsr_request_item_status!: string;
  old_wsr_amount!: number
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

WsrItemRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    wsr_request_detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    wsr_amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    reference_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    wsr_request_item_status: {
      type: DataTypes.ENUM,
      values: Object.values(wsr_request_item_status),
      defaultValue: wsr_request_item_status.ACTIVE,
    },
    old_wsr_amount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_wsr_item_requests",
    modelName: "WsrItemRequest",
  },
);

WsrItemRequest.belongsTo(WsrRequest, { foreignKey: "wsr_request_detail_id", as: "wsr_item_request" });
WsrRequest.hasMany(WsrItemRequest, { foreignKey: "wsr_request_detail_id", as: "wsr_request_detail" });

WsrItemRequest.belongsTo(PaymentTypeCategory, { foreignKey: "payment_type_category_id", as: "wsr_request_item_type" });
PaymentTypeCategory.hasMany(WsrItemRequest, { foreignKey: "payment_type_category_id", as: "wsr_request_detail_type" });


// Define hooks for Card model
WsrItemRequest.addHook("afterUpdate", async (wsrItemRequest: any) => {
  await addActivity("WsrItem", "updated", wsrItemRequest);
});

WsrItemRequest.addHook("afterCreate", async (wsrItemRequest: WsrItemRequest) => {
  await addActivity("WsrItem", "created", wsrItemRequest);
});

WsrItemRequest.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

