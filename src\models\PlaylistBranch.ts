"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { Playlist } from "./Playlist";
import { Branch } from "./Branch";
import { addActivity } from "../helper/queue.service";

interface playlistBranchAttributes {
  playlist_id: number;
  branch_id: number;
  playlist_branch_status: string;
  created_by: number;
  updated_by: number;
}

export enum playlist_branch_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class PlaylistBranch
  extends Model<playlistBranchAttributes, never>
  implements playlistBranchAttributes {
  playlist_id!: number;
  branch_id!: number;
  playlist_branch_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PlaylistBranch.init(
  {
    playlist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    branch_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    playlist_branch_status: {
      type: DataTypes.ENUM,
      values: Object.values(playlist_branch_status),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_playlist_branch",
    modelName: "PlaylistBranch",
  },
);

PlaylistBranch.removeAttribute("id");
Branch.hasMany(PlaylistBranch, { foreignKey: "branch_id", as: "branches" });
PlaylistBranch.belongsTo(Branch, { foreignKey: "branch_id", as: "branches" });
Playlist.hasMany(PlaylistBranch, { foreignKey: "playlist_id" });
PlaylistBranch.belongsTo(Playlist, { foreignKey: "playlist_id" });

PlaylistBranch.addHook("afterUpdate", async (playlistBranch: any) => {
  await addActivity("PlaylistBranch", "updated", playlistBranch);
});

PlaylistBranch.addHook(
  "afterCreate",
  async (playlistBranch: PlaylistBranch) => {
    await addActivity("PlaylistBranch", "created", playlistBranch);
  },
);

PlaylistBranch.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
