"use strict";
const { QueryTypes } = require("sequelize");

module.exports = {
  async up(queryInterface, Sequelize) {
    const findPermissions = await queryInterface.sequelize.query(
      `SELECT * FROM nv_permissions`,
      { type: QueryTypes.SELECT },
    );

    if (findPermissions.length === 0) {
      const userRoles = await queryInterface.sequelize.query(
        `SELECT * FROM nv_roles`,
        { type: QueryTypes.SELECT },
      );

      const commonData = {
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const modules = [
        "dashboard",
        "user",
        "branch",
        "department",
        "notification",
        "setting",
        "staff",
        "leave_center",
        "resignation",
        "category",
        "media",
        "playlist",
        "activity_log",
        "branch_card",
        "branch_bank",
        "dsr",
        "dsr_report",
        "change_request",
        "user_invitation",
        "user_verification",
        "employee_contract",
        "forecast",
        "forecast_budget",
        "leave_setting",
        "leave_report",
        "side_letter",
        "setup"
      ];

      for (const role of userRoles) {
        const insertData = modules.map((module) => ({
          role_id: role.id,
          module,
          permission: getPermission(role.id, module),
          partial: getPartialPermission(role.id, module),
          ...commonData,
        }));

        await queryInterface.bulkInsert("nv_permissions", insertData, {});
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // Add commands to revert seed here.
    // Example: await queryInterface.bulkDelete('People', null, {});
  },
};

function getPermission(roleId, module) {
  switch (roleId) {
    case 1:
    case 2:
      if (module === "user") return 1;
    case 3:
      if (module === "dsr") return 3;
      if (module === "dsr_report") return 3;
      if (module === "forecast") return 3;
      if (module === "forecast_budget") return 3;
      if (module === "leave_setting") return 3;
      if (module === "leave_report") return 3;
    case 4:
      if (module === "dsr") return 0;
      if (module === "dsr_report") return 0;
      if (module === "forecast") return 0;
      if (module === "forecast_budget") return 0;
      if (module === "leave_setting") return 3;
      if (module === "leave_report") return 3;
      return 3;
    case 5:
      if (module === "branch") return 1;
      if (module === "leave_center") return 3;
      if (module === "notification") return 3;
      if (module === "resignation") return 3;
      if (module === "setup") return 3;
      if (module === "forecast") return 1;
      if (module === "side_letter") return 1;
      return 0;
    case 6:
      if (module === "staff") return 1;
      if (module === "leave_center") return 1;
      if (module === "resignation") return 1;
      if (module === "branch_card" || module === "branch_bank" || module === "dsr" || module === "dsr_report" || module === "forecast" || module === "forecast_budget" || module === "forecast_budget" || module === "side_letter" || module === 'setup') return 3;
      return 0;
    case 7:
      // return module !== "user" && module !== "setting" && module !=='activity_log' ? 3 : 0;
      if (module !== "user" && module !== "setting" && module !== 'activity_log' && module !== "branch_card" && module !== "branch_bank" && module !== "dsr_report" && module !== 'change_request' && module !== 'user_verification' && module !== "employee_contract" && module !== "leave_setting")
        return (module === "dashboard" || module === "branch") ? 1 : 3;
      else
        return 0;
    case 14:
      if (module !== "user" && module !== "setting" && module !== 'activity_log' && module !== "branch_card" && module !== "branch_bank" && module !== "dsr_report" && module !== 'change_request' && module !== 'user_verification' && module !== "employee_contract" && module !== "leave_setting")
        return (module === "dashboard" || module === "branch") ? 1 : 3;
      else
        return 0;
    case 19:
      if (module === "staff") return 1;
      if (module === "user_verification") return 3;
      return 0;
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 15:
    case 16:
    case 17:
    case 18:
      return 0;
    default:
      return 3;
  }
}

function getPartialPermission(roleId, module) {
  switch (roleId) {
    case 1:
    case 2:
      if (module === "user") return false;
    case 3:
    case 4:
    case 5:
    case 6:
      return false;
    case 7:
      if (module === "dashboard") return false;
      if (module === "user") return false;
      if (module === "setting") return false;
      if (module === "employee_contract") return false;
      if (module === "activity_log") return false;
      if (module === "branch") return false;
      if (module === "branch_card") return false;
      if (module === "branch_bank") return false;
      if (module === "dsr_report") return false;
      if (module === "change_request") return false;
      if (module === "user_verification") return false;
      if (module === "forecast") return false;
      if (module === "leave_setting") return false;
      if (module === "setup") return false;
      return true;
    case 14:
      if (module === "dashboard") return false;
      if (module === "user") return false;
      if (module === "setting") return false;
      if (module === "employee_contract") return false;
      if (module === "activity_log") return false;
      if (module === "branch") return false;
      if (module === "branch_card") return false;
      if (module === "branch_bank") return false;
      if (module === "dsr_report") return false;
      if (module === "change_request") return false;
      if (module === "user_verification") return false;
      if (module === "forecast") return false;
      if (module === "leave_setting") return false;
      if (module === "setup") return false;
      return true;
    case 19:
      if (module === "staff" || module === "user_verification") return true;
      return false;
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 15:
    case 16:
    case 17:
    case 18:
      return false;
    default:
      return false;
  }
}
