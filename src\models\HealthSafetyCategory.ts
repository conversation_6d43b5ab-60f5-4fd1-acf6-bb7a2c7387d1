"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface healthSafetyCategoryAttributes {
  id: number;
  category_name: string;
  status: string;
  created_by: number;
  updated_by: number;
}

export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}


export class HealthSafetyCategory
  extends Model<healthSafetyCategoryAttributes, never>
  implements healthSafetyCategoryAttributes {
  id!: number;
  category_name!: string;
  status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

HealthSafetyCategory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    category_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(status),
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_health_safety_category",
    modelName: "HealthSafetyCategory",
  },
);

// Define hooks for HealthSafety model
HealthSafetyCategory.addHook("afterUpdate", async (healthSafetyCategory: any) => {
  await addActivity("HealthSafetyCategory", "updated", healthSafetyCategory);
});

HealthSafetyCategory.addHook("afterCreate", async (healthSafetyCategory: HealthSafetyCategory) => {
  await addActivity("HealthSafetyCategory", "created", healthSafetyCategory);
});

HealthSafetyCategory.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

