"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";


interface userFieldOrderAttributes {
    id: number;
    user_field_order: string;
    organization_id: string;
    created_by: number;
    updated_by: number;
}


export class UserFieldOrder
    extends Model<userFieldOrderAttributes, never>
    implements userFieldOrderAttributes {
    id!: number;
    user_field_order!: string;
    organization_id!: string
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

UserFieldOrder.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        user_field_order: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
        },
        organization_id: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "user_field_order",
        modelName: "UserFieldOrder",
    },
);



// Define hooks for Branch model
UserFieldOrder.addHook("afterUpdate", async (userFieldOrder: any) => {
    await addActivity("UserFieldOrder", "updated", userFieldOrder);
});

UserFieldOrder.addHook("afterCreate", async (userFieldOrder: UserFieldOrder) => {
    await addActivity("UserFieldOrder", "created", userFieldOrder);
});

UserFieldOrder.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

