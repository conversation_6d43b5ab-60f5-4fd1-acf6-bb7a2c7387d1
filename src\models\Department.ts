"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface departmentAttributes {
  id: number;
  organization_id: string;
  department_name: string;
  department_status: string;
  department_remark: string;
  created_by: number;
  updated_by: number;
}

/** Role enum  for status*/
export enum department_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class Department
  extends Model<departmentAttributes, never>
  implements departmentAttributes {
  id!: number;
  organization_id!: string;
  department_name!: string;
  department_status!: string;
  department_remark!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Department.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    department_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    department_remark: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    department_status: {
      type: DataTypes.ENUM,
      values: Object.values(department_status),
      defaultValue: department_status.ACTIVE,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_departments",
    modelName: "Department",
  },
);

// Define hooks for Department model
Department.addHook("afterUpdate", async (department: any) => {
  await addActivity("Department", "updated", department);
});

Department.addHook("afterCreate", async (department: Department) => {
  await addActivity("Department", "created", department);
});

Department.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

