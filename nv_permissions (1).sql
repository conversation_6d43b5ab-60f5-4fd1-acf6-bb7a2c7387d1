-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1deb5ubuntu1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jun 12, 2025 at 01:24 PM
-- Server version: 8.0.42-0ubuntu0.22.04.1
-- PHP Version: 8.3.20

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `organization_manager`
--

-- --------------------------------------------------------

--
-- Table structure for table `nv_permissions`
--

CREATE TABLE `nv_permissions` (
  `id` int NOT NULL,
  `role_id` int NOT NULL,
  `module` enum('dashboard','user','branch','department','notification','setting','staff','leave_center','resignation','category','media','playlist','activity_log','branch_card','branch_bank','dsr','dsr_report','change_request','user_invitation','user_verification','employee_contract','forecast','forecast_budget','leave_setting','leave_report','side_letter','setup') NOT NULL,
  `permission` int NOT NULL,
  `partial` tinyint(1) DEFAULT '0',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `nv_permissions`
--

INSERT INTO `nv_permissions` (`id`, `role_id`, `module`, `permission`, `partial`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(1, 1, 'dashboard', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(2, 1, 'user', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(3, 1, 'branch', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(4, 1, 'department', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(5, 1, 'notification', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(6, 1, 'setting', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(7, 1, 'staff', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(8, 1, 'leave_center', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(9, 1, 'resignation', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(10, 1, 'category', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(11, 1, 'media', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(12, 1, 'playlist', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(13, 1, 'activity_log', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(14, 1, 'branch_card', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(15, 1, 'branch_bank', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(16, 1, 'dsr', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(17, 1, 'dsr_report', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:10'),
(18, 1, 'change_request', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(19, 1, 'user_invitation', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(20, 1, 'user_verification', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(21, 1, 'employee_contract', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(22, 1, 'forecast', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(23, 1, 'forecast_budget', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(24, 1, 'leave_setting', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(25, 1, 'leave_report', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(26, 1, 'side_letter', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(27, 1, 'setup', 3, 0, 1, 10, '2025-05-31 09:34:53', '2025-06-11 12:09:11'),
(28, 2, 'dashboard', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(29, 2, 'user', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(30, 2, 'branch', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(31, 2, 'department', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(32, 2, 'notification', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(33, 2, 'setting', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(34, 2, 'staff', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(35, 2, 'leave_center', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(36, 2, 'resignation', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(37, 2, 'category', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(38, 2, 'media', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(39, 2, 'playlist', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(40, 2, 'activity_log', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(41, 2, 'branch_card', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(42, 2, 'branch_bank', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(43, 2, 'dsr', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(44, 2, 'dsr_report', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(45, 2, 'change_request', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(46, 2, 'user_invitation', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(47, 2, 'user_verification', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(48, 2, 'employee_contract', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(49, 2, 'forecast', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(50, 2, 'forecast_budget', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(51, 2, 'leave_setting', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(52, 2, 'leave_report', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(53, 2, 'side_letter', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(54, 2, 'setup', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(55, 3, 'dashboard', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(56, 3, 'user', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(57, 3, 'branch', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(58, 3, 'department', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(59, 3, 'notification', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(60, 3, 'setting', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(61, 3, 'staff', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(62, 3, 'leave_center', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(63, 3, 'resignation', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(64, 3, 'category', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(65, 3, 'media', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(66, 3, 'playlist', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(67, 3, 'activity_log', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(68, 3, 'branch_card', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(69, 3, 'branch_bank', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(70, 3, 'dsr', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(71, 3, 'dsr_report', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(72, 3, 'change_request', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(73, 3, 'user_invitation', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(74, 3, 'user_verification', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(75, 3, 'employee_contract', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(76, 3, 'forecast', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(77, 3, 'forecast_budget', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(78, 3, 'leave_setting', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(79, 3, 'leave_report', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(80, 3, 'side_letter', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(81, 3, 'setup', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(82, 4, 'dashboard', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(83, 4, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(84, 4, 'branch', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(85, 4, 'department', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(86, 4, 'notification', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(87, 4, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(88, 4, 'staff', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(89, 4, 'leave_center', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(90, 4, 'resignation', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(91, 4, 'category', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(92, 4, 'media', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(93, 4, 'playlist', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(94, 4, 'activity_log', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(95, 4, 'branch_card', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(96, 4, 'branch_bank', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(97, 4, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(98, 4, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(99, 4, 'change_request', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(100, 4, 'user_invitation', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(101, 4, 'user_verification', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(102, 4, 'employee_contract', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(103, 4, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(104, 4, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(105, 4, 'leave_setting', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(106, 4, 'leave_report', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(107, 4, 'side_letter', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(108, 4, 'setup', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(109, 5, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(110, 5, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(111, 5, 'branch', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(112, 5, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(113, 5, 'notification', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(114, 5, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(115, 5, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(116, 5, 'leave_center', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(117, 5, 'resignation', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(118, 5, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(119, 5, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(120, 5, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(121, 5, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(122, 5, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(123, 5, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(124, 5, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(125, 5, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(126, 5, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(127, 5, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(128, 5, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(129, 5, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(130, 5, 'forecast', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(131, 5, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(132, 5, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(133, 5, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(134, 5, 'side_letter', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(135, 5, 'setup', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(136, 6, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(137, 6, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(138, 6, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(139, 6, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(140, 6, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(141, 6, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(142, 6, 'staff', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(143, 6, 'leave_center', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(144, 6, 'resignation', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(145, 6, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(146, 6, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(147, 6, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(148, 6, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(149, 6, 'branch_card', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(150, 6, 'branch_bank', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(151, 6, 'dsr', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(152, 6, 'dsr_report', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(153, 6, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(154, 6, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(155, 6, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(156, 6, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(157, 6, 'forecast', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(158, 6, 'forecast_budget', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(159, 6, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(160, 6, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(161, 6, 'side_letter', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(162, 6, 'setup', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(163, 7, 'dashboard', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(164, 7, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(165, 7, 'branch', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(166, 7, 'department', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(167, 7, 'notification', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(168, 7, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(169, 7, 'staff', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(170, 7, 'leave_center', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(171, 7, 'resignation', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(172, 7, 'category', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(173, 7, 'media', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(174, 7, 'playlist', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(175, 7, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(176, 7, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(177, 7, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(178, 7, 'dsr', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(179, 7, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(180, 7, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(181, 7, 'user_invitation', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(182, 7, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(183, 7, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(184, 7, 'forecast', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(185, 7, 'forecast_budget', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(186, 7, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(187, 7, 'leave_report', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(188, 7, 'side_letter', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(189, 7, 'setup', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(190, 8, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(191, 8, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(192, 8, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(193, 8, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(194, 8, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(195, 8, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(196, 8, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(197, 8, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(198, 8, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(199, 8, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(200, 8, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(201, 8, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(202, 8, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(203, 8, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(204, 8, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(205, 8, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(206, 8, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(207, 8, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(208, 8, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(209, 8, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(210, 8, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(211, 8, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(212, 8, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(213, 8, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(214, 8, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(215, 8, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(216, 8, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(217, 9, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(218, 9, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(219, 9, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(220, 9, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(221, 9, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(222, 9, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(223, 9, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(224, 9, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(225, 9, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(226, 9, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(227, 9, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(228, 9, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(229, 9, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(230, 9, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(231, 9, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(232, 9, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(233, 9, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(234, 9, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(235, 9, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(236, 9, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(237, 9, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(238, 9, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(239, 9, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(240, 9, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(241, 9, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(242, 9, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(243, 9, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(244, 10, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(245, 10, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(246, 10, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(247, 10, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(248, 10, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(249, 10, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(250, 10, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(251, 10, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(252, 10, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(253, 10, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(254, 10, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(255, 10, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(256, 10, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(257, 10, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(258, 10, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(259, 10, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(260, 10, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(261, 10, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(262, 10, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(263, 10, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(264, 10, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(265, 10, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(266, 10, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(267, 10, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(268, 10, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(269, 10, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(270, 10, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(271, 11, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(272, 11, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(273, 11, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(274, 11, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(275, 11, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(276, 11, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(277, 11, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(278, 11, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(279, 11, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(280, 11, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(281, 11, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(282, 11, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(283, 11, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(284, 11, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(285, 11, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(286, 11, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(287, 11, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(288, 11, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(289, 11, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(290, 11, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(291, 11, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(292, 11, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(293, 11, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(294, 11, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(295, 11, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(296, 11, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(297, 11, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(298, 12, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(299, 12, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(300, 12, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(301, 12, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(302, 12, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(303, 12, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(304, 12, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(305, 12, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(306, 12, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(307, 12, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(308, 12, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(309, 12, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(310, 12, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(311, 12, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(312, 12, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(313, 12, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(314, 12, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(315, 12, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(316, 12, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(317, 12, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(318, 12, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(319, 12, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(320, 12, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(321, 12, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(322, 12, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(323, 12, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(324, 12, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(325, 13, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(326, 13, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(327, 13, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(328, 13, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(329, 13, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(330, 13, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(331, 13, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(332, 13, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(333, 13, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(334, 13, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(335, 13, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(336, 13, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(337, 13, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(338, 13, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(339, 13, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(340, 13, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(341, 13, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(342, 13, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(343, 13, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(344, 13, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(345, 13, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(346, 13, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(347, 13, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(348, 13, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(349, 13, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(350, 13, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(351, 13, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(352, 14, 'dashboard', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(353, 14, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(354, 14, 'branch', 1, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(355, 14, 'department', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(356, 14, 'notification', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(357, 14, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(358, 14, 'staff', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(359, 14, 'leave_center', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(360, 14, 'resignation', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(361, 14, 'category', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(362, 14, 'media', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(363, 14, 'playlist', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(364, 14, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(365, 14, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(366, 14, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(367, 14, 'dsr', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(368, 14, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(369, 14, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(370, 14, 'user_invitation', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(371, 14, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(372, 14, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(373, 14, 'forecast', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(374, 14, 'forecast_budget', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(375, 14, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(376, 14, 'leave_report', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(377, 14, 'side_letter', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(378, 14, 'setup', 3, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(379, 15, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(380, 15, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(381, 15, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(382, 15, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(383, 15, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(384, 15, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(385, 15, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(386, 15, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(387, 15, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(388, 15, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(389, 15, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(390, 15, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(391, 15, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(392, 15, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(393, 15, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(394, 15, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(395, 15, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(396, 15, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(397, 15, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(398, 15, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(399, 15, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(400, 15, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(401, 15, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(402, 15, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(403, 15, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(404, 15, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(405, 15, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(406, 16, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(407, 16, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(408, 16, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(409, 16, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(410, 16, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(411, 16, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(412, 16, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(413, 16, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(414, 16, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(415, 16, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(416, 16, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(417, 16, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(418, 16, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(419, 16, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(420, 16, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(421, 16, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(422, 16, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(423, 16, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(424, 16, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(425, 16, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(426, 16, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(427, 16, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(428, 16, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(429, 16, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(430, 16, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(431, 16, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(432, 16, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(433, 17, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(434, 17, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(435, 17, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(436, 17, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(437, 17, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(438, 17, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(439, 17, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(440, 17, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(441, 17, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(442, 17, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(443, 17, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(444, 17, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(445, 17, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(446, 17, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(447, 17, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(448, 17, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(449, 17, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(450, 17, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(451, 17, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(452, 17, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(453, 17, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(454, 17, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(455, 17, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(456, 17, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(457, 17, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(458, 17, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(459, 17, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(460, 18, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(461, 18, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(462, 18, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(463, 18, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(464, 18, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(465, 18, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(466, 18, 'staff', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(467, 18, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(468, 18, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(469, 18, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(470, 18, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(471, 18, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(472, 18, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(473, 18, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(474, 18, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(475, 18, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(476, 18, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(477, 18, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(478, 18, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(479, 18, 'user_verification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(480, 18, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(481, 18, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(482, 18, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(483, 18, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(484, 18, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(485, 18, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(486, 18, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(487, 19, 'dashboard', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(488, 19, 'user', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(489, 19, 'branch', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(490, 19, 'department', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(491, 19, 'notification', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(492, 19, 'setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(493, 19, 'staff', 1, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(494, 19, 'leave_center', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(495, 19, 'resignation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(496, 19, 'category', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(497, 19, 'media', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(498, 19, 'playlist', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(499, 19, 'activity_log', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(500, 19, 'branch_card', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(501, 19, 'branch_bank', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(502, 19, 'dsr', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(503, 19, 'dsr_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(504, 19, 'change_request', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(505, 19, 'user_invitation', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(506, 19, 'user_verification', 3, 1, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(507, 19, 'employee_contract', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(508, 19, 'forecast', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(509, 19, 'forecast_budget', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(510, 19, 'leave_setting', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(511, 19, 'leave_report', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(512, 19, 'side_letter', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53'),
(513, 19, 'setup', 0, 0, 1, 1, '2025-05-31 09:34:53', '2025-05-31 09:34:53');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `nv_permissions`
--
ALTER TABLE `nv_permissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `role_id` (`role_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `nv_permissions`
--
ALTER TABLE `nv_permissions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=514;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `nv_permissions`
--
ALTER TABLE `nv_permissions`
  ADD CONSTRAINT `nv_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `nv_roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
