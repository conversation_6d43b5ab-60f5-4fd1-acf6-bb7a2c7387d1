import { Segments, Jo<PERSON>, celebrate } from "celebrate";
export default {
  saveReportFilter: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        filter_name: Joi.string().required(),
        filter_value: Joi.string().allow(null, ""),
        group_value: Joi.string().allow(null, ""),
        user_filter_type: Joi.string().required()
      }),
    }),
  updateReportFilter: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        filter_name: Joi.string().required(),
        filter_value: Joi.string().allow(null, ""),
        group_value: Joi.string().allow(null, ""),
        user_filter_type: Joi.string().required()
      }),
    }),

};
