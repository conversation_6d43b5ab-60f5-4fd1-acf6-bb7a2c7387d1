"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { DsrRequest } from "./DsrRequest";
import { PaymentTypeCategory } from "./PaymentTypeCategory";

interface dsrItemRequestAttributes {
  id: number;
  dsr_request_detail_id: number;
  payment_type_category_id: number
  dsr_amount: number;
  reference_id: number;
  dsr_request_item_status: string;
  old_dsr_amount: number;
  created_by: number;
  updated_by: number;
}

export enum dsr_request_item_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}


export class DsrItemRequest
  extends Model<dsrItemRequestAttributes, never>
  implements dsrItemRequestAttributes {
  id!: number;
  dsr_request_detail_id!: number;
  payment_type_category_id!: number
  dsr_amount!: number;
  reference_id!: number;
  dsr_request_item_status!: string;
  old_dsr_amount!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

DsrItemRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    dsr_request_detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    dsr_amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    reference_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    dsr_request_item_status: {
      type: DataTypes.ENUM,
      values: Object.values(dsr_request_item_status),
      defaultValue: dsr_request_item_status.ACTIVE,
    },
    old_dsr_amount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_dsr_item_requests",
    modelName: "DsrItemRequest",
  },
);

DsrItemRequest.belongsTo(DsrRequest, { foreignKey: "dsr_request_detail_id", as: "dsr_item_request" });
DsrRequest.hasMany(DsrItemRequest, { foreignKey: "dsr_request_detail_id", as: "dsr_request_detail" });

DsrItemRequest.belongsTo(PaymentTypeCategory, { foreignKey: "payment_type_category_id", as: "dsr_request_item_type" });
PaymentTypeCategory.hasMany(DsrItemRequest, { foreignKey: "payment_type_category_id", as: "dsr_request_detail_type" });


// Define hooks for Card model
DsrItemRequest.addHook("afterUpdate", async (dsrItemRequest: any) => {
  await addActivity("DsrItem", "updated", dsrItemRequest);
});

DsrItemRequest.addHook("afterCreate", async (dsrItemRequest: DsrItemRequest) => {
  await addActivity("DsrItem", "created", dsrItemRequest);
});

DsrItemRequest.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

