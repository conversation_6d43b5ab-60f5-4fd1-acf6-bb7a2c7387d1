"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";


interface forecastBudgetDataHistoryAttributes {
    id: number;
    forecast_id: number;
    payment_type_id: number;
    january_amount: number;
    february_amount: number;
    march_amount: number;
    april_amount: number;
    may_amount: number;
    june_amount: number;
    july_amount: number;
    august_amount: number;
    september_amount: number;
    october_amount: number;
    november_amount: number;
    december_amount: number;
    bugdet_target_amount: number;
    forecast_category_type: string;
    forecast_bugdet_data_history_status: string;
    payment_type_category_id: string;
    forecast_history_id: number;
    forecast_category_status: string;
    created_by: number;
    updated_by: number;
}


export enum forecast_budget_data_history_status {
    PENDING = "pending",
    DRAFT = "draft",
    ACTIVE = "active",
    INACTIVE = 'inactive'
}
export enum forecast_category_type {
    INCOME = "income",
    OTHER = "other",
    EXPENSE = "expense",
}

export enum forecast_category_status {
    COMBINED = "combined",
    SEPARATE = 'separate'
}


export enum value_type {
    MONTH = "month",
    TOTAL = "total"
}


export class ForecastBugdetDataHistory
    extends Model<forecastBudgetDataHistoryAttributes, never>
    implements forecastBudgetDataHistoryAttributes {
    id!: number
    forecast_id!: number;
    payment_type_id!: number;
    january_amount!: number;
    february_amount!: number;
    march_amount!: number;
    april_amount!: number;
    may_amount!: number;
    june_amount!: number;
    july_amount!: number;
    august_amount!: number;
    september_amount!: number;
    october_amount!: number;
    november_amount!: number;
    december_amount!: number;
    bugdet_target_amount!: number;
    forecast_category_type!: string;
    payment_type_category_id!: string;
    forecast_bugdet_data_history_status!: string;
    forecast_history_id!: number;
    forecast_category_status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

ForecastBugdetDataHistory.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        forecast_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        payment_type_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        january_amount: {
            type: DataTypes.DOUBLE,
            defaultValue: 0,
        },
        february_amount: {
            type: DataTypes.DOUBLE,
            defaultValue: 0,
        },
        march_amount: {
            type: DataTypes.DOUBLE,
            defaultValue: 0,
        },
        april_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        may_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        june_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        july_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        august_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        september_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        october_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        november_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        december_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        bugdet_target_amount: { type: DataTypes.DOUBLE, defaultValue: 0 },
        forecast_category_type: {
            type: DataTypes.ENUM,
            values: Object.values(forecast_category_type),
            allowNull: true
        },
        forecast_bugdet_data_history_status: {
            type: DataTypes.ENUM,
            values: Object.values(forecast_budget_data_history_status),
            defaultValue: forecast_budget_data_history_status.ACTIVE,
        },
        forecast_history_id: {
            type: DataTypes.INTEGER,
        },
        payment_type_category_id: {
            type: DataTypes.STRING,
        },
        forecast_category_status: {
            type: DataTypes.ENUM,
            values: Object.values(forecast_category_status)
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_forecast_bugdet_data_history",
        modelName: "ForecastBugdetDataHistory",
    },
);


ForecastBugdetDataHistory.removeAttribute("id");


