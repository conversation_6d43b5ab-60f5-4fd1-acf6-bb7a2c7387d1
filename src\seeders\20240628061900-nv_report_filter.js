"use strict";
const { QueryTypes } = require("sequelize");

module.exports = {
  async up(queryInterface, Sequelize) {
    const getReportFilterData = await queryInterface.sequelize.query(
      `SELECT * FROM nv_report_filters`,
      { type: QueryTypes.SELECT },
    );

    if (getReportFilterData.length === 0) {
      const insertData = [{
        key: "today",
        value: "Today",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "yesterday",
        value: "Yesterday",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "this_week",
        value: "This Week",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "this_month",
        value: "This Month",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "this_quarter",
        value: "This Quarter",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "last_week",
        value: "Last Week",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "last_month",
        value: "Last Month",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "last_7_days",
        value: "Last 7 Days",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "last_14_days",
        value: "Last 14 Days",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "last_30_days",
        value: "Last 30 Days",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "last_60_days",
        value: "Last 60 Days",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "last_90_days",
        value: "Last 90 Days",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "custom",
        value: "Custom",
        report_filter_type: "day",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "current_month",
        value: "Current Month",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "previous_month",
        value: "Previous Month",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "1st_quarter",
        value: "1st Quarter",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "2nd_quarter",
        value: "2nd Quarter",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "3rd_quarter",
        value: "3rd Quarter",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "4th_quarter",
        value: "4th Quarter",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "last_4_months",
        value: "Last 4 Months",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "current_year",
        value: "Current Year",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "previous_year",
        value: "Previous Year",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "last_2_years",
        value: "Last 2 Years",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "custom",
        value: "Custom",
        report_filter_type: "general",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "daily",
        value: "Daily",
        report_filter_type: "timeperiod",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "weekly",
        value: "Weekly",
        report_filter_type: "timeperiod",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "monthly",
        value: "Monthly",
        report_filter_type: "timeperiod",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "quarterly",
        value: "Quarterly",
        report_filter_type: "timeperiod",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "yearly",
        value: "Yearly",
        report_filter_type: "timeperiod",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        key: "none",
        value: "None",
        report_filter_type: "timeperiod",
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
        ,]


      await queryInterface.bulkInsert("nv_report_filters", insertData, {});
    }
  },

  async down(queryInterface, Sequelize) {
    // Add commands to revert seed here.
    // Example: await queryInterface.bulkDelete('People', null, {});
  },
};


