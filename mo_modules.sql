-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1deb5ubuntu1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jun 12, 2025 at 01:24 PM
-- Server version: 8.0.42-0ubuntu0.22.04.1
-- PHP Version: 8.3.20

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `organization_manager`
--

-- --------------------------------------------------------

--
-- Table structure for table `mo_modules`
--

CREATE TABLE `mo_modules` (
  `id` int NOT NULL,
  `module` varchar(255) NOT NULL,
  `module_name` varchar(255) NOT NULL,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `mo_modules`
--

INSERT INTO `mo_modules` (`id`, `module`, `module_name`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(1, 'dashboard', 'Dashboard', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(2, 'user', 'User Management', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(3, 'branch', 'Branch Management', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(4, 'department', 'Department Management', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(5, 'notification', 'Notifications', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(6, 'setting', 'Settings', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(7, 'staff', 'Staff Management', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(8, 'leave_center', 'Leave Center', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(9, 'resignation', 'Resignation', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(10, 'category', 'Category Management', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(11, 'media', 'Media Management', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(12, 'playlist', 'Playlist Management', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(13, 'activity_log', 'Activity Log', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(14, 'branch_card', 'Branch Card', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(15, 'branch_bank', 'Branch Bank', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(16, 'dsr', 'DSR', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(17, 'dsr_report', 'DSR Report', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(18, 'change_request', 'Change Request', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(19, 'user_invitation', 'User Invitation', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(20, 'user_verification', 'User Verification', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(21, 'employee_contract', 'Employee Contract', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(22, 'forecast', 'Forecast', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(23, 'forecast_budget', 'Forecast Budget', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(24, 'leave_setting', 'Leave Setting', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(25, 'leave_report', 'Leave Report', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(26, 'side_letter', 'Side Letter', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(27, 'setup', 'Setup', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `mo_modules`
--
ALTER TABLE `mo_modules`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `module` (`module`),
  ADD UNIQUE KEY `module_2` (`module`),
  ADD UNIQUE KEY `module_3` (`module`),
  ADD UNIQUE KEY `module_4` (`module`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `mo_modules`
--
ALTER TABLE `mo_modules`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
