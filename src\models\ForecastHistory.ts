"use strict";
import { Model, DataTypes, Sequelize } from "sequelize";
import { sequelize } from "./index";

interface forecastHistoryAttributes {
    id: number;
    forecast_history_status: string;
    forecast_id: number;
    created_by: number;
    updated_by: number;
    createdAt: string;
}

export enum forecast_history_status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DELETED = 'deleted',
    ASSIGNED = 'assigned',
    SUBMITTED = 'submitted',
    APPROVED = 'approved',
    REVOKED = 'revoked'
}

export class ForecastHistory
    extends Model<forecastHistoryAttributes, never>
    implements forecastHistoryAttributes {
    id!: number;
    forecast_id!: number;
    forecast_history_status!: string;
    created_by!: number;
    updated_by!: number;
    createdAt!: string;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

ForecastHistory.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        forecast_id: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        forecast_history_status: {
            type: DataTypes.ENUM,
            values: Object.values(forecast_history_status),
            defaultValue: forecast_history_status.ACTIVE,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_forecast_history",
        modelName: "ForecastHistory"
    },
);


