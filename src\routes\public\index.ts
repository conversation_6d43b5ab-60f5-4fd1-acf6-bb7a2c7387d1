import express, { Router } from "express";
import { refreshSessionHandle } from "../../helper/utils";
import sessionValidator from "../../middleware/session";
import authRoute from "./auth.routes";
import userRoute from "./user.routes";
const routes: Router = express.Router();

routes.get("/refresh", sessionValidator, refreshSessionHandle);

routes.use("/auth", authRoute);

routes.use('/user', userRoute)

export default routes;
