"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { HolidayPolicy } from "./HolidayPolicy";

interface userHolidayPolicyAttributes {
  user_id: number;
  holiday_policy_id: number;
  user_holiday_policy_status: string;
  created_by: number;
  updated_by: number;
}

export class UserHolidayPolicy
  extends Model<userHolidayPolicyAttributes>
  implements userHolidayPolicyAttributes {
  user_id!: number;
  holiday_policy_id!: number;
  user_holiday_policy_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

export enum user_holiday_policy_status {
  ACTIVE = "active",
  INACTIVE = "inactive"
}

UserHolidayPolicy.init(
  {
    user_id: {
      type: DataTypes.INTEGER,
    },
    holiday_policy_id: {
      type: DataTypes.INTEGER,
    },
    user_holiday_policy_status: {
      type: DataTypes.ENUM,
      values: Object.values(user_holiday_policy_status),
      defaultValue: user_holiday_policy_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER
    }
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_holiday_policy",
    modelName: "UserHolidayPolicy",
    timestamps: false,
  },
);

UserHolidayPolicy.removeAttribute("id");
UserHolidayPolicy.belongsTo(HolidayPolicy, { foreignKey: "holiday_policy_id", as: "user_policy" });
HolidayPolicy.hasMany(UserHolidayPolicy, { foreignKey: "holiday_policy_id", as: "user_policy_list" });


UserHolidayPolicy.addHook("afterUpdate", async (userHolidayPolicy: any) => {
  await addActivity("UserHolidayPolicy", "updated", userHolidayPolicy);
});

UserHolidayPolicy.addHook("afterCreate", async (userHolidayPolicy: UserHolidayPolicy) => {
  await addActivity("UserHolidayPolicy", "created", userHolidayPolicy);
});

UserHolidayPolicy.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
