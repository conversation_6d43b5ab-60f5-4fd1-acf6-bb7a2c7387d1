import { Request, Response } from "express";
import { Branch, branch_status } from "../models/Branch";
import { Op } from "sequelize";
import {
  checkBranchUser,
  isRecordReferencedInAnyTable,
  permittedForAdmin,
  validateModulePermission,
} from "../helper/common";
import { StatusCodes } from "http-status-codes";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { Role, role_status } from "../models/Role";
import { FILE_UPLOAD_CONSTANT, ROLE_CONSTANT, ROLE_PERMISSIONS } from "../helper/constant";
import { User } from "../models/User";
import {
  Item,
  item_IEC,
  item_status,
  item_type,
  item_external_location,
} from "../models/Item";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getHash } from "../helper/common";
import { s3 } from "../helper/upload.service";
import {
  payment_type_category_status,
  PaymentTypeCategory,
} from "../models/PaymentTypeCategory";
import { PaymentTypeCategoryBranch } from "../models/PaymentTypeCategoryBranch";
import { UserBranch } from "../models/UserBranch";
import { sequelize } from "../models";
import { PaymentType } from "../models/PaymentType";

/**
 *  Create new  branch. Only admin/director/hr can create a new branch.
 * @param req
 * @param res
 * @returns
 */

const addBranch = async (req: Request, res: Response) => {
  try {
    const {
      branch_name = "",
      branch_remark = "",
      branchStatus,
      branch_color,
    } = req.body;
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
    ]);
    if (!checkPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    /** check branch name already exist for that organization, then throw error */
    const findBranchByName = await Branch.findOne({
      where: {
        branch_name,
        branch_status: { [Op.not]: branch_status.DELETED },
        organization_id: req.user.organization_id,
      },
      attributes: ["id"],
      raw: true,
    });
    if (findBranchByName) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BRANCH_NAME_EXIST") });
    } else {
      const createBranch: any = await Branch.setHeaders(req).create({
        branch_name: branch_name,
        branch_remark,
        branch_status: branchStatus,
        branch_color,
        organization_id: req.user.organization_id,
        created_by: req.user.id,
        updated_by: req.user.id,
      } as any);
      if (createBranch) {
        const findPaymentTypeCategory = await PaymentTypeCategory.findAll({
          where: {
            payment_type_category_status: {
              [Op.not]: payment_type_category_status.DELETED,
            },
          },
          include: [
            {
              model: PaymentType,
              as: "payment_type_list",
              where: { organization_id: req.user.organization_id },
            },
          ],
          raw: true,
          nest: true,
        });
        if (findPaymentTypeCategory.length > 0) {
          for (const category of findPaymentTypeCategory) {
            await PaymentTypeCategoryBranch.create({
              payment_type_category_id: category.id,
              branch_id: createBranch.id,
              payment_type_category_branch_status:
                category.payment_type_category_status,
              created_by: req.user.id,
              updated_by: req.user.id,
            } as any);
          }
        }
        return res.status(StatusCodes.CREATED).json({
          status: true,
          message: res.__("BRANCH_CREATION_SUCCESSED"),
          data: createBranch,
        });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: false, message: res.__("BRANCH_CREATION_FAILED") });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Update branch
 * @param req
 * @param res
 * @returns
 */

const updatedBranch = async (req: Request, res: Response) => {
  try {
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
    ]);
    if (!checkPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    const {
      branch_name,
      branch_remark,
      branchStatus,
      branch_color,
      branch_sign,
      branch_employer_name,
      branch_heading_employer_name,
      branch_heading_name,
      branch_work_place,
      branch_heading_work_place,
      registration_number,
    } = req.body;
    const { branch_id }: any = req.params;
    const findBranchList = await Branch.findOne({
      where: {
        id: branch_id,
        branch_status: { [Op.not]: branch_status.DELETED },
        organization_id: req.user.organization_id
      }, raw: true
    });
    if (!findBranchList) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }

    let signatureItemId = findBranchList.branch_sign;

    if (
      branch_sign &&
      typeof branch_sign === "string" &&
      branch_sign.includes("base64")
    ) {
      try {
        const bucketName = process.env.NODE_ENV || "development";
        const fileBuffer = Buffer.from(
          branch_sign.split(";base64,").pop() || "",
          "base64",
        );
        const iFileBuffer = Buffer.from(branch_sign.split(";base64,").pop() || "")
        const signatureName = `branch_signature_${findBranchList?.branch_name.trim()}_${findBranchList.id}_${Date.now()}.png`;
        const filePath = FILE_UPLOAD_CONSTANT.SIGNATURES_PATH.destinationPath(req.user.organization_id, signatureName);

        // Generate hash for deduplication
        const fileHash: any = await getHash(iFileBuffer, {
          path: filePath,
          originalname: signatureName,
          mimetype: "image/png",
          size: Buffer.byteLength(iFileBuffer),
        }, "image/png");

        // Check if file already exists by hash
        let fileExists = false;
        let existingItemId = null;

        if (fileHash.status) {
          const getItem: any = await Item.findOne({
            where: {
              item_hash: fileHash.hash,
              item_organization_id: req.user.organization_id,
            },
          });

          if (getItem && getItem.id) {
            fileExists = true;
            existingItemId = getItem.id;
          }
        }

        if (!fileExists) {
          // Upload file to S3
          await s3.send(
            new PutObjectCommand({
              Bucket: bucketName,
              Key: filePath,
              Body: fileBuffer,
              ContentType: "image/png",
            }),
          );

          // Create item record
          const saveItem: any = {
            item_type: item_type.IMAGE,
            item_name: signatureName,
            item_hash: fileHash.hash,
            item_mime_type: "image/png",
            item_extension: ".png",
            item_size: Buffer.byteLength(iFileBuffer),
            item_IEC: item_IEC.B,
            item_status: item_status.ACTIVE,
            item_external_location: item_external_location.NO,
            item_location: filePath,
            item_organization_id: req.user.organization_id,
          };

          const item = await Item.create(saveItem);
          signatureItemId = item.id.toString();
        } else {
          signatureItemId = existingItemId.toString();
        }
      } catch (error) {
        console.error("Error uploading signature to S3:", error);
      }
    }

    const updateObj: any = {
      branch_name: branch_name,
      branch_remark,
      branch_status: branchStatus,
      branch_color: branch_color,
      branch_sign: signatureItemId,
      branch_employer_name,
      branch_heading_employer_name,
      branch_heading_name,
      branch_work_place,
      branch_heading_work_place,
      registration_number,
      updated_by: req.user.id,
    };

    const updateBranch = await Branch.setHeaders(req).update(updateObj, {
      where: { id: findBranchList.id },
    });
    if (updateBranch.length > 0) {
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("BRANCH_UPDATION_SUCCESSED") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("BRANCH_UPDATION_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get All branch list
 * @param req
 * @param res
 * @returns
 */

const getAllBranch = async (req: Request, res: Response) => {
  try {
    const { page, size, search, branchStatus, forecast_year }: any = req.query;
    if (branchStatus != branch_status.ACTIVE) {
      // const checkPermission = await validateModulePermission(
      //   req.user,
      //   req.user.organization_id,
      //   3, // Branch module ID
      //   ROLE_PERMISSIONS.VIEW
      // );
      const checkPermission = await permittedForAdmin(req.user?.id, [
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.AREA_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
        ROLE_CONSTANT.ACCOUNTANT,
      ]);
      if (!checkPermission)
        return res
          .status(StatusCodes.FORBIDDEN)
          .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    let { limit, offset }: any = getPagination(page, size);
    limit = Number(limit);
    offset = Number(offset);
    const whereObj: any = {
      branch_status: { [Op.not]: branch_status.DELETED },
      organization_id: req.user.organization_id,
    };
    /** check user is exist or not */
    const findUserDetail: any = await User.findOne({ where: { id: req.user.id, organization_id: req.user.organization_id }, attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id'], raw: true })

    if (!findUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    if (findUserDetail) {
      const findCurrentRole = await Role.findOne({
        where: {
          id:
            req.headers["platform-type"] == "web"
              ? findUserDetail.web_user_active_role_id
              : findUserDetail.user_active_role_id,
          role_status: role_status.ACTIVE,
        },
        raw: true,
      });
      if (
        findCurrentRole?.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
        findCurrentRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
        findCurrentRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
        findCurrentRole?.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER ||
        findCurrentRole?.role_name == ROLE_CONSTANT.AREA_MANAGER
      ) {
        const findUserBranch = await UserBranch.findAll({
          attributes: ['branch_id'],
          where: {
            user_id: req.user.id
          }, raw: true
        })
        if (findCurrentRole?.role_name == ROLE_CONSTANT.AREA_MANAGER && findUserBranch.length > 0) {
          whereObj.id = { [Op.in]: findUserBranch.map((branch: any) => branch.branch_id) }
        } else {
          if (findUserDetail.branch_id) {
            whereObj.id = findUserDetail.branch_id;
          }
        }
      }
    }
    if (search) {
      whereObj.branch_name = { [Op.like]: `%${search}%` };
    } const attributesArray = [
      "id",
      "branch_name",
      "branch_remark",
      "branch_status",      [
        sequelize.literal(
          `CASE 
            WHEN branch_sign IS NULL OR branch_sign = '' THEN ''
            WHEN NOT branch_sign REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = Branch.branch_sign) 
            THEN CONCAT('${global.config.API_BASE_URL}signatures/', Branch.branch_sign)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = Branch.branch_sign))
          END`
        ),
        "branch_sign"
      ],
      [sequelize.literal(`(Branch.branch_sign)`), "branch_sign_id"],
      "branch_employer_name",
      "branch_color",
      "branch_heading_employer_name",
      "branch_heading_name",
      "branch_heading_work_place",
      "branch_work_place",
      "registration_number",
      "organization_id",
      "created_by",
      "updated_by",
      "createdAt",
      "updatedAt",
      "text_color",
      [
        sequelize.literal(
          `(SELECT CASE WHEN COUNT(*) > 0 THEN true ELSE false END 
          FROM nv_forecast 
          WHERE nv_forecast.branch_id = Branch.id)`,
        ),
        "is_forecast_added",
      ],
    ];

    if (forecast_year) {
      attributesArray.push([
        sequelize.literal(
          `(SELECT CASE WHEN COUNT(*) > 0 THEN true ELSE false END 
            FROM nv_forecast 
            WHERE nv_forecast.branch_id = Branch.id AND nv_forecast.forecast_year = '${forecast_year}')`,
        ),
        "is_forecast_added",
      ]);
    }

    const branchListObj: any = {
      where: whereObj,
      raw: true,
      nest: true,
      attributes: attributesArray,
    };
    if (page && size) {
      branchListObj.limit = limit;
      branchListObj.offset = offset;
    }
    if (branchStatus) {
      whereObj.branch_status = branchStatus;
    }
    const getBranchList: any = await Branch.findAll(branchListObj);
    const count = await Branch.count({ where: whereObj });

    const { total_pages } = getPaginatedItems(
      Number(size),
      Number(page),
      count || 0,
    );
    return res.status(StatusCodes.OK).json({
      status: true,
      data: getBranchList,
      message: res.__("SUCCESS_FETCHED"),
      count: count,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete  by id
 * @param req
 * @param res
 * @returns
 */

const deleteBranch = async (req: Request, res: Response) => {
  try {
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
    ]);
    if (!checkPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    const { branch_id } = req.params;

    /** check branch is exist or not */
    const findBranchList = await Branch.findOne({
      where: {
        id: branch_id,
        branch_status: { [Op.not]: branch_status.DELETED },
        organization_id: req.user.organization_id
      }, attributes: ['id'], raw: true
    });
    if (!findBranchList) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }

    const isReferenced = await isRecordReferencedInAnyTable(Branch.tableName, branch_id);

    if (isReferenced) {
      return res
        .status(400)
        .json({ message: res.__("ERROR_RECORD_IN_USE_TABLES") });
    }
    const deleteBranch = await Branch.setHeaders(req).update(
      { branch_status: branch_status.DELETED, updated_by: req.user.id },
      { where: { id: findBranchList.id } },
    );
    if (deleteBranch.length > 0) {
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("BRANCH_DELETATION_SUCCESSED") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("BRANCH_DELETION_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get branch by id
 * @param req
 * @param res
 * @returns
 */

const getBranchById = async (req: Request, res: Response) => {
  try {
    const { branch_id } = req.params;
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.BRANCH_MANAGER,
      ROLE_CONSTANT.AREA_MANAGER,
      ROLE_CONSTANT.HOTEL_MANAGER,
    ]);
    const isBranchUser = await checkBranchUser(Number(branch_id), req.user.id);
    if (!checkPermission && !isBranchUser) {
      if (!checkPermission)
        return res
          .status(StatusCodes.FORBIDDEN)
          .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }
    let getBranchById: any = await Branch.findOne({
      where: {
        organization_id: req.user.organization_id,
        id: branch_id,
        branch_status: { [Op.not]: branch_status.DELETED },
      },
      raw: true,
    }); if (getBranchById) {
      if (getBranchById.branch_sign) {
        getBranchById = JSON.parse(JSON.stringify(getBranchById));
        getBranchById.branch_sign_id = getBranchById.branch_sign;
        const item = await Item.findOne({
          where: { id: getBranchById.branch_sign },
          attributes: ["item_location"],
          raw: true,
        });
        if(item){
        getBranchById.branch_sign = item?.item_location
          ? `${global.config.API_BASE_URL}${item.item_location}`
          : "";
        }else{
          getBranchById.branch_sign = `${global.config.API_BASE_URL}signatures/${getBranchById.branch_sign_id}`
        }
      }
      return res
        .status(StatusCodes.OK)
        .json({ status: true, data: getBranchById });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND"), data: [] });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const saveBranch = async (req: Request, res: Response) => {
  try {
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
    ]);
    if (!checkPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    const {
      branch_name,
      branch_remark,
      branchStatus,
      branch_color,
      branch_sign,
      branch_employer_name,
      branch_heading_employer_name,
      branch_heading_name,
      branch_work_place,
      branch_heading_work_place,
      registration_number,
      text_color,
    } = req.body;
    const { branch_id } = req.params;

    let existingBranch = null;
    if (branch_id) {
      existingBranch = await Branch.findOne({
        attributes: ['id', 'branch_sign'],
        where: {
          id: branch_id,
          branch_status: { [Op.not]: branch_status.DELETED },
          organization_id: req.user.organization_id
        },
        raw: true,
      });

      if (!existingBranch) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
      }
    }
    // Check for duplicate branch name
    const duplicateBranch = await Branch.findOne({
      attributes: ['id'],
      where: {
        branch_name: branch_name,
        id: { [Op.not]: branch_id || 0 },
        organization_id: req.user.organization_id,
        branch_status: { [Op.not]: branch_status.DELETED },
      },
      raw: true,
    });

    if (duplicateBranch) {
      return res
        .status(StatusCodes.CONFLICT)
        .json({ status: false, message: res.__("BRANCH_NAME_EXIST") });
    }

    if (!branch_id) {
      // Creating a new branch
      const branchData: any = {
        branch_name,
        branch_remark,
        branch_status: branchStatus,
        branch_color,
        branch_employer_name,
        branch_heading_employer_name,
        branch_heading_name,
        branch_work_place,
        branch_heading_work_place,
        registration_number,
        updated_by: req.user.id,
        text_color,
        created_by: req.user.id,
        organization_id: req.user.organization_id,
      };

      const newBranch = await Branch.setHeaders(req).create(branchData);

      if (!newBranch) {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: false, message: res.__("BRANCH_CREATION_FAILED") });
      }

      // Handle signature for new branch
      if (
        branch_sign &&
        typeof branch_sign === "string" &&
        branch_sign.includes("base64")
      ) {
        try {
          const bucketName = process.env.NODE_ENV || "development";
          const fileBuffer = Buffer.from(
            branch_sign.split(";base64,").pop() || "",
            "base64",
          );
          const iFileBuffer = Buffer.from(branch_sign.split(";base64,").pop() || "")
          const signatureName = `branch_signature_${branch_name.trim()}_${newBranch.id}_${Date.now()}.png`;
          const filePath = FILE_UPLOAD_CONSTANT.SIGNATURES_PATH.destinationPath(req.user.organization_id, signatureName);

          // Generate hash for deduplication
          const fileHash: any = await getHash(iFileBuffer, {
            originalname: signatureName,
            // mimetype: "image/png",
            size: Buffer.byteLength(iFileBuffer)
          }, "image/png");

          // Check if file already exists by hash
          let fileExists = false;
          let existingItemId = null;

          if (fileHash.status) {
            const getItem: any = await Item.findOne({
              where: {
                item_hash: fileHash.hash,
                item_organization_id: req.user.organization_id,
              },
            });

            if (getItem && getItem.id) {
              fileExists = true;
              existingItemId = getItem.id;
            }
          }

          if (!fileExists) {
            // Upload file to S3
            await s3.send(
              new PutObjectCommand({
                Bucket: bucketName,
                Key: filePath,
                Body: fileBuffer,
                ContentType: "image/png",
              }),
            );

            // Create item record
            const saveItem: any = {
              item_type: item_type.IMAGE,
              item_name: signatureName,
              item_hash: fileHash.hash,
              item_mime_type: "image/png",
              item_extension: ".png",
              item_size: Buffer.byteLength(iFileBuffer),
              item_IEC: item_IEC.B,
              item_status: item_status.ACTIVE,
              item_external_location: item_external_location.NO,
              item_location: filePath,
              item_organization_id: req.user.organization_id,
            };

            const item = await Item.create(saveItem);
            await Branch.setHeaders(req).update(
              { branch_sign: item.id.toString() },
              {
                where: { id: newBranch.id },
              },
            );
          } else {
            await Branch.setHeaders(req).update(
              { branch_sign: existingItemId.toString() },
              {
                where: { id: newBranch.id },
              },
            );
          }
        } catch (error) {
          console.error("Error uploading signature to S3:", error);
        }
      }

      // Setup payment type categories for the new branch
      const findPaymentTypeCategory = await PaymentTypeCategory.findAll({
        where: {
          payment_type_category_status: {
            [Op.not]: payment_type_category_status.DELETED,
          },
        },
        include: [
          {
            model: PaymentType,
            as: "payment_type_list",
            where: { organization_id: req.user.organization_id },
          },
        ],
        raw: true,
        nest: true,
      });

      if (findPaymentTypeCategory.length > 0) {
        for (const category of findPaymentTypeCategory) {
          await PaymentTypeCategoryBranch.create({
            payment_type_category_id: category.id,
            branch_id: newBranch.id,
            payment_type_category_branch_status:
              category.payment_type_category_status,
            created_by: req.user.id,
            updated_by: req.user.id,
          } as any);
        }
      }

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("BRANCH_CREATION_SUCCESSED"),
        data: newBranch,
      });
    } else {
      // Updating existing branch
      const branchData: any = {
        branch_name,
        branch_remark,
        branch_status: branchStatus,
        branch_color,
        branch_employer_name,
        branch_heading_employer_name,
        branch_heading_name,
        branch_work_place,
        branch_heading_work_place,
        registration_number,
        updated_by: req.user.id,
        text_color,
      };

      // Handle signature for existing branch
      if (
        branch_sign &&
        typeof branch_sign === "string" &&
        branch_sign.includes("base64")
      ) {
        try {
          const bucketName = process.env.NODE_ENV || "development";
          const fileBuffer = Buffer.from(
            branch_sign.split(";base64,").pop() || "",
            "base64",
          );
          const iFileBuffer = Buffer.from(branch_sign.split(";base64,").pop() || "")
          const signatureName = `branch_signature_${branch_name.trim()}_${branch_id}_${Date.now()}.png`;
          const filePath = FILE_UPLOAD_CONSTANT.SIGNATURES_PATH.destinationPath(req.user.organization_id, signatureName);

          // Generate hash for deduplication
          const fileHash: any = await getHash(iFileBuffer, {
            originalname: signatureName,
            size: Buffer.byteLength(iFileBuffer)
          }, "image/png");

          // Check if file already exists by hash
          let fileExists = false;
          let existingItemId = null;

          if (fileHash.status) {
            const getItem: any = await Item.findOne({
              where: {
                item_hash: fileHash.hash,
                item_organization_id: req.user.organization_id,
              },
            });

            if (getItem && getItem.id) {
              fileExists = true;
              existingItemId = getItem.id;
            }
          }

          if (!fileExists) {
            // Upload file to S3
            await s3.send(
              new PutObjectCommand({
                Bucket: bucketName,
                Key: filePath,
                Body: fileBuffer,
                ContentType: "image/png",
              }),
            );

            // Create item record
            const saveItem: any = {
              item_type: item_type.IMAGE,
              item_name: signatureName,
              item_hash: fileHash.hash,
              item_mime_type: "image/png",
              item_extension: ".png",
              item_size: Buffer.byteLength(iFileBuffer),
              item_IEC: item_IEC.B,
              item_status: item_status.ACTIVE,
              item_external_location: item_external_location.NO,
              item_location: filePath,
              item_organization_id: req.user.organization_id,
            };

            const item = await Item.create(saveItem);
            branchData.branch_sign = item.id.toString();
          } else {
            branchData.branch_sign = existingItemId.toString();
          }
        } catch (error) {
          console.error("Error uploading signature to S3:", error);
        }
      }

      // Update existing branch
      const updateBranch = await Branch.setHeaders(req).update(branchData, {
        where: { id: branch_id },
      });

      if (updateBranch.length > 0) {
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("BRANCH_UPDATION_SUCCESSED"),
        });
      } else {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("BRANCH_UPDATION_FAILED"),
        });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

export default {
  addBranch,
  updatedBranch,
  getAllBranch,
  deleteBranch,
  getBranchById,
  saveBranch,
};
