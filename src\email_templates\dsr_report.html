<!DOCTYPE html>
<html>

<head>
    <title>DSR Table</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .table-container {
            background-color: #ffffff;
            /* padding: 20px; */
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            max-width: 100%;
            margin: 0 auto;
            overflow-x: auto;
        }

        .dsr-table {
            width: 100%;
            /* border-collapse: collapse; */
            padding-top: 3;
            padding-bottom: 3px;
        }

        th,
        td {
            border: 1px solid #dddddd;
            text-align: center;
            word-wrap: break-word;
            padding: 2px;
        }

        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .fixed-header-cell {
            position: sticky;
            top: 0;
            z-index: 2;
        }

        .total-row {
            font-weight: bold;
            background-color: #e6ffe6;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            position: relative;
        }

        .logo-container {
            text-align: left;
        }

        .logo-container img {
            max-width: 100px;
        }

        .report-details {
            text-align: right;
        }

        .report-details p {
            margin: 5px 0;
            font-size: 16px;
            color: #666666;
        }

        h2 {
            font-size: 24px;
            color: #333333;
            margin-bottom: 20px;
        }

        .report-title {
            text-align: center;
            font-size: 24px;
            color: #333333;
            margin-bottom: 20px;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
        }

        .report-date {
            text-align: left;
            font-size: 12px;
            color: #666666;
            margin-bottom: 10px;
            /* Reduced margin-bottom */
        }
    </style>
</head>

<body>
    <div class="report-container">
        <div class="header">
            <div class="report-title">Logs book report</div>
            <div class="logo-container">
                <img src="{{NAMASTE_LOGO}}">
            </div>
            <div>
                <div class="report-details" style="margin-bottom: 10px;">
                    <p>Date: {{date}}</p>
                </div>
                <div class="report-details">
                    {{#if branch}}
                    <p>Branch: {{branch}}</p>
                    {{/if}}
                    {{#if time_period}}
                    <p>Time period : {{time_period}}</p>
                    {{/if}}

                </div>
            </div>
        </div>
        <div class="report-date">Report Date: {{current_date}}</div>
        <div class="table-container">
            {{#each splitedData as | tableData |}}
            <table class="dsr-table">
                <thead>
                    <tr>
                        {{#each tableData.columns_group}}
                        <td colspan="{{#if children}}{{children.length}}{{else}}1{{/if}}"
                            class="{{#if (eq key 'col1')}}fixed-header-cell{{/if}}" style="font-weight: bold;">
                            <div title="{{content}}">
                                {{content}}
                            </div>
                        </td>

                        {{/each}}
                    </tr>
                    <tr>
                        {{#each tableData.columns_group}}
                        {{#if children}}
                        <!-- <td> -->
                        {{#each children}}
                        <td style="font-weight: bold;">
                            {{content}}
                        </td>
                        {{/each}}
                        <!-- </td> -->
                        {{else}}
                        <td>
                            <!-- <div title="{{content}}">
                             {{content}} 
                        </div> -->
                        </td>
                        {{/if}}
                        {{/each}}
                    </tr>
                </thead>
                <tbody>
                    {{#each tableData.value }}
                    <tr class="{{#if @last}}total-row{{/if}}">
                        {{#each tableData.columns_group as | column |}}
                        {{#if children}}
                        {{#each children as | child |}}
                        <td>
                            {{lookup ../.. child.key}}
                        </td>
                        {{/each}}
                        {{else}}
                        <td>
                            {{lookup .. column.key}}
                        </td>
                        {{/if}}
                        {{/each}}
                    </tr>
                    {{/each}}
                </tbody>
            </table>
            <br>
            <br>
            {{/each}}
        </div>
</body>
