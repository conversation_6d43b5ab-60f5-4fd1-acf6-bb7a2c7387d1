"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { ExpenseDetail } from "./ExpenseDetail";

interface expenseRequestAttributes {
  id: number;
  expense_detail_id: number;
  user_id: number;
  expense_request_status: string;
  request_remark: string;
  created_by: number;
  updated_by: number;
}

export enum expense_request_status {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = 'rejected',
  DELETED = 'deleted'
}

export class ExpenseRequest
  extends Model<expenseRequestAttributes, never>
  implements expenseRequestAttributes {
  id!: number;
  expense_detail_id!: number;
  user_id!: number;
  expense_request_status!: string;
  request_remark!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

ExpenseRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    expense_detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    expense_request_status: {
      type: DataTypes.ENUM,
      values: Object.values(expense_request_status),
      defaultValue: expense_request_status.PENDING,
    },
    request_remark: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_expense_requests",
    modelName: "ExpenseRequest",
  },
);


ExpenseRequest.belongsTo(ExpenseDetail, { foreignKey: "expense_detail_id", as: "expense_detail" });
ExpenseDetail.hasMany(ExpenseRequest, { foreignKey: "expense_detail_id", as: "user" });

// Define hooks for Card model
ExpenseRequest.addHook("afterUpdate", async (expenseRequest: any) => {
  await addActivity("ExpenseRequest", "updated", expenseRequest);
});

ExpenseRequest.addHook("afterCreate", async (expenseRequest: ExpenseRequest) => {
  await addActivity("ExpenseRequest", "created", expenseRequest);
});

ExpenseRequest.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

