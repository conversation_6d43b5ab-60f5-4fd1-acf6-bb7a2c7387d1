import { Jo<PERSON> } from "celebrate";
export default {
  createCategory: Joi.object().keys({
    category_name: Joi.string().required(),
    category_description: Joi.string().allow(null),
    category_status: Joi.string().required(),
    dashboard_view: Joi.boolean().allow(null),
    category_use: Joi.string().allow(null),
    parent_id: Joi.number().integer().allow(null),
    branch_ids: Joi.array().items(Joi.number().integer()).unique().min(1).required(),
    department_ids: Joi.array().items(Joi.number().integer()).unique().min(1).required(),
    is_notify: Joi.boolean().allow(null),
    notification_content: Joi.string().allow(null),
    has_video_control: Joi.boolean().allow(null),
    has_agreement_required: Joi.boolean().allow(null),
    is_external_link: Joi.boolean().allow(null),
    external_links: Joi.array().allow(null),
    category_type: Joi.string().valid('file', 'folder').required()
  }),
  updateCategory: Joi.object().keys({
    category_name: Joi.string().required(),
    category_description: Joi.string().allow(null),
    category_status: Joi.string().required(),
    dashboard_view: Joi.boolean().allow(null),
    branch_ids: Joi.array().items(Joi.number().integer()).unique().min(1).required(),
    department_ids: Joi.array().items(Joi.number().integer()).unique().min(1).required(),
    is_notify: Joi.boolean().allow(null),
    notification_content: Joi.string().allow(null),
    has_video_control: Joi.boolean().allow(null),
    has_agreement_required: Joi.boolean().allow(null),
    is_external_link: Joi.boolean().allow(null),
    external_links: Joi.array().allow(null),
    existing_item: Joi.array().items(Joi.number().integer()).unique().min(1).allow(null)
  }),
  moveCategory: Joi.object().keys({
    to_move_id: Joi.number().integer().required(),
    from_move_id: Joi.number().integer().required(),
  }),
  trackCategory: Joi.object().keys({
    category_id: Joi.number().integer().required()
  }),
  restoreTrackCategory: Joi.object().keys({
    user_id: Joi.string().allow(null),
    category_id: Joi.number().allow(null),
    search_category_id: Joi.string().allow(null),
  }),
  allRestoreTrackCategory: Joi.object().keys({
    user_ids: Joi.string().allow(null),
    category_ids: Joi.string().allow(null),
    branches: Joi.string().allow(null),
    departments: Joi.string().allow(null),
  })
};

