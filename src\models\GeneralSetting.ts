"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface generalSettingAttributes {
    id: number;
    key: string;
    value: string;
    general_setting_status: string;
    created_by: number;
    updated_by: number;
}

export enum general_setting_status {
    ACTIVE = "active",
    DELETE = "delete",
}

export class GeneralSetting
    extends Model<generalSettingAttributes, never>
    implements generalSettingAttributes {
    id!: number;
    key!: string;
    value!: string;
    general_setting_status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

GeneralSetting.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        key: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        value: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        general_setting_status: {
            type: DataTypes.ENUM,
            values: Object.values(general_setting_status),
            allowNull: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_general_setting",
        modelName: "GeneralSetting",
    },
);

GeneralSetting.addHook("afterUpdate", async (generalSetting: any) => {
    await addActivity("GeneralSetting", "updated", generalSetting);
});

GeneralSetting.addHook("afterCreate", async (generalSetting: GeneralSetting) => {
    await addActivity("GeneralSetting", "created", generalSetting);
});

GeneralSetting.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

