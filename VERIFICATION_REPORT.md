# Module Permission Verification Report

## Overview
This report verifies the consistency between the mo_permissions_insert.sql file, mo_modules.sql file, and the test cases in validateModulePermission.test.ts.

## Module ID Mapping Verification

### ✅ Correct Module Mappings (from mo_modules.sql)
```sql
(1, 'dashboard', 'Dashboard')
(2, 'user', 'User Management')
(3, 'branch', 'Branch Management')
(4, 'department', 'Department Management')
(5, 'notification', 'Notifications')
(6, 'setting', 'Settings')
(7, 'staff', 'Staff Management')
(8, 'leave_center', 'Leave Center')
(9, 'resignation', 'Resignation')
(10, 'category', 'Category Management')
(11, 'media', 'Media Management')
(12, 'playlist', 'Playlist Management')
(13, 'activity_log', 'Activity Log')
(14, 'branch_card', 'Branch Card')
(15, 'branch_bank', 'Branch Bank')
(16, 'dsr', 'DSR')
(17, 'dsr_report', 'DSR Report')
(18, 'change_request', 'Change Request')
(19, 'user_invitation', 'User Invitation')
(20, 'user_verification', 'User Verification')
(21, 'employee_contract', 'Employee Contract')
(22, 'forecast', 'Forecast')
(23, 'forecast_budget', 'Forecast Budget')
(24, 'leave_setting', 'Leave Setting')
(25, 'leave_report', 'Leave Report')
(26, 'side_letter', 'Side Letter')
(27, 'setup', 'Setup')
```

## Permission System Verification

### ✅ ROLE_PERMISSIONS Constants (from constant.ts)
```typescript
ROLE_PERMISSIONS = {
  NONE: 0,
  VIEW: 1,
  CREATE: 2,
  EDIT: 4,
  DELETE: 8
}
```

### ⚠️ Permission Value Discrepancy Found
**Issue**: The mo_permissions_insert.sql file uses permission values like 0, 1, 3 which don't match the bitwise ROLE_PERMISSIONS constants.

**Old System (nv_permissions)**: Used simple integer values
- 0 = No permission
- 1 = Read/View
- 3 = Full access (Create, Read, Update)

**New System (mo_permissions)**: Should use bitwise values
- 0 = NONE
- 1 = VIEW
- 2 = CREATE
- 4 = EDIT
- 8 = DELETE
- 7 = VIEW + CREATE + EDIT (1 + 2 + 4)
- 15 = All permissions (1 + 2 + 4 + 8)

## Test Case Updates Made

### ✅ Fixed Module ID References
- Updated all test cases to use module_id = 2 for 'user' module (was incorrectly using 1)
- Added comprehensive module mapping tests for IDs 1, 3, 7, and 27
- Verified test module object matches the correct module ID

### ✅ Test Coverage Added
- Dashboard module (ID: 1)
- Branch module (ID: 3) 
- Staff module (ID: 7)
- Setup module (ID: 27)

## Recommendations

### 1. Update mo_permissions_insert.sql Permission Values
The permission values in mo_permissions_insert.sql should be updated to use bitwise values:

```sql
-- Instead of permission = 3, use:
-- permission = 7 (VIEW + CREATE + EDIT = 1 + 2 + 4)

-- Instead of permission = 1, use:
-- permission = 1 (VIEW only)

-- Instead of permission = 0, use:
-- permission = 0 (NONE - this is correct)
```

### 2. Migration Strategy
1. **Option A**: Update the SQL file to use bitwise values
2. **Option B**: Update the validation function to handle both old and new permission formats during transition
3. **Option C**: Create a migration script to convert existing permission values

### 3. Validation Function Enhancement
Consider adding validation to ensure permission values are within expected ranges and follow bitwise patterns.

## Files Modified
- ✅ `src/tests/validateModulePermission.test.ts` - Updated all module ID references and added comprehensive test coverage
- ✅ `mo_permissions_insert.sql` - Verified structure and identified permission value discrepancy

## Next Steps
1. Decide on permission value migration strategy
2. Update mo_permissions_insert.sql with correct bitwise permission values
3. Run tests to verify all functionality works correctly
4. Consider adding module ID constants file for better maintainability
