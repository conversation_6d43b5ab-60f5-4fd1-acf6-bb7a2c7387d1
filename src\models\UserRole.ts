"use strict";
import { Model, Sequelize, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { Role } from "./Role";
import { addActivity } from "../helper/queue.service";

interface userRoleAttributes {
  user_id: number;
  role_id: number;
  created_by: number;
  createdAt: string;
}

export class UserRole
  extends Model<userRoleAttributes>
  implements userRoleAttributes {
  user_id!: number;
  role_id!: number;
  created_by!: number;
  createdAt!: string;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

UserRole.init(
  {
    user_id: {
      type: DataTypes.INTEGER,
    },
    role_id: {
      type: DataTypes.INTEGER,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_roles",
    modelName: "UserRole",
    timestamps: false,
  },
);

UserRole.removeAttribute("id");
// UserRole.belongsTo(User, { foreignKey: "user_id", as: "user" });
// User.hasMany(UserRole, { foreignKey: "user_id", as: "user_roles" });
UserRole.belongsTo(Role, { foreignKey: "role_id", as: "role" });
Role.hasMany(UserRole, { foreignKey: "role_id", as: "role" });

UserRole.addHook("afterUpdate", async (userRole: any) => {
  await addActivity("UserRole", "updated", userRole);
});

UserRole.addHook("afterCreate", async (userRole: UserRole) => {
  await addActivity("UserRole", "created", userRole);
});



UserRole.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
