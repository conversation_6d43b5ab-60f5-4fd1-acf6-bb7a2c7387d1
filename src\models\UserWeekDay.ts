"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

export enum weekDayTypes {
    WORKING = "working",
    DAYOFF = "dayoff",
}

export enum user_weekday_status {
    ACTIVE = "active",
    INACTIVE = "inactive"
}


interface userWeekDayAttributes {
    user_id: number;
    monday: string;
    tuesday: string;
    wednesday: string;
    thursday: string;
    friday: string;
    saturday: string;
    sunday: string;
    user_weekday_status: string;
    created_by: number;
    updated_by: number;
}

export class UserWeekDay
    extends Model<userWeekDayAttributes, never>
    implements userWeekDayAttributes {
    user_id!: number;
    monday!: string;
    tuesday!: string;
    wednesday!: string;
    thursday!: string;
    friday!: string;
    saturday!: string;
    sunday!: string;
    user_weekday_status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

UserWeekDay.init(
    {
        user_id: {
            type: DataTypes.INTEGER
        },
        monday: {
            type: DataTypes.ENUM,
            values: Object.values(weekDayTypes),
            defaultValue: weekDayTypes.WORKING
        },
        tuesday: {
            type: DataTypes.ENUM,
            values: Object.values(weekDayTypes),
            defaultValue: weekDayTypes.WORKING
        },
        wednesday: {
            type: DataTypes.ENUM,
            values: Object.values(weekDayTypes),
            defaultValue: weekDayTypes.WORKING
        },
        thursday: {
            type: DataTypes.ENUM,
            values: Object.values(weekDayTypes),
            defaultValue: weekDayTypes.WORKING
        },
        friday: {
            type: DataTypes.ENUM,
            values: Object.values(weekDayTypes),
            defaultValue: weekDayTypes.WORKING
        },
        saturday: {
            type: DataTypes.ENUM,
            values: Object.values(weekDayTypes),
            defaultValue: weekDayTypes.WORKING
        },
        sunday: {
            type: DataTypes.ENUM,
            values: Object.values(weekDayTypes),
            defaultValue: weekDayTypes.WORKING
        },
        user_weekday_status: {
            type: DataTypes.ENUM,
            values: Object.values(user_weekday_status),
            defaultValue: user_weekday_status.ACTIVE
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_user_week_day",
        modelName: "UserWeekDay",
    },
);

UserWeekDay.removeAttribute("id");

// UserWeekDay.hasOne(User, {
//     foreignKey: "user_id",
//     as: "week_day",
// });
// User.belongsTo(UserWeekDay, { foreignKey: "user_id", as: "user_week_day" });
// UserMeta.belongsTo(ContractTypeModel, { foreignKey: "contract_type", as: "user_contract_type_list" });
// ContractTypeModel.hasMany(UserMeta, {
//   foreignKey: "contract_type",
//   as: "contract_type_list",
// });

// User.hasOne(UserWeekDay, {
//     foreignKey: "user_id",
//     as: "user_week_day",
// });
// UserWeekDay.belongsTo(User, { foreignKey: "user_id", as: "week_day" });

UserWeekDay.addHook("afterUpdate", async (userWeekDay: any) => {
    await addActivity("UserWeekDay", "updated", userWeekDay);
});

UserWeekDay.addHook("afterCreate", async (userWeekDay: UserWeekDay) => {
    await addActivity("UserWeekDay", "created", userWeekDay);
});

UserWeekDay.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

