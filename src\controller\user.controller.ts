import { Request, Response } from "express";
import { Activity, activity_action, activity_type } from "../models/Activity";
import { generateRefreshToken, generateToken } from "../helper/auth.service";
import { Permission } from "../models/Permission";
import { GetObjectCommand } from "@aws-sdk/client-s3";
import getCurrentLeaveBalance, {
  findParentUserRole,
  findUserExist,
  getRoleName,
  roleName,
  userCreatePermission,
  checkUserProfileComplete,
  addSpacesBeforeCapitals,
  formatUserAgentData,
  GeneratePassword,
  regenerateEmploymentContractFuncation,
  getGeneralSettingObj,
  getGeoDetails,
  generateEmploymentNumber,
  getSuperAdminUserId,
  getStaffCount,
  handleLeaveAccrual,
  sendEmailNotification,
  getHash
} from "../helper/common";
import { User, user_status } from "../models/User";
import { Op, QueryTypes, Sequelize } from "sequelize";
import _ from "lodash";
import {
  comparePassword,
  encrypt,
  generateSession,
  getPaginatedItems,
  getPagination,
} from "../helper/utils";
import {
  ADMIN_SIDE_USER,
  EMAIL_ADDRESS,
  FILE_UPLOAD_CONSTANT,
  NORMAL_USER,
  ROLE_CONSTANT,
  URL_CONSTANT,
} from "../helper/constant";
import { UserRole } from "../models/UserRole";
import { sequelize } from "../models";
import { StatusCodes } from "http-status-codes";
import { Role, role_status } from "../models/Role";
import { Branch, branch_status } from "../models/Branch";
import { Department, department_status } from "../models/Department";
import userValidator from "../validators/user.validator";
import {
  UserRequest,
  request_status,
  request_type,
} from "../models/UserRequest";
import path from "path";
import fs from 'fs'
import { invitation_status, UserInvite } from "../models/UserInvite";
import { UserMeta } from "../models/UserMeta";
import {
  UserEmploymentContract,
  contract_status as contractStatus,
} from "../models/UserEmployementContract";
import moment from "moment";
import { Setting } from "../models/Setting";
import { LeaveTypeModel, status as leaveTypeStatus } from "../models/LeaveType";
import { Geo } from "../models/Geo";
import { UserBranch } from "../models/UserBranch";
import { user_weekday_status, UserWeekDay } from "../models/UserWeekDay";
import { RABBITMQ_QUEUE } from "../helper/constant";
import rabbitmqPublisher from "../rabbitmq/rabbitmq";

import {
  user_holiday_policy_status,
  UserHolidayPolicy,
} from "../models/UserHolidayPolicy";
import {
  user_leave_policy_status,
  UserLeavePolicy,
} from "../models/UserLeavePolicy";
import { holiday_policy_status, HolidayPolicy } from "../models/HolidayPolicy";
import {
  // deleteFileFromBucket,
  moveFileInBucket,
  s3,
} from "../helper/upload.service";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { Item, item_type, item_IEC, item_external_location, item_status } from "../models/Item";
import { LeaveAccuralPolicy, status } from "../models/LeaveAccuralPolicy";
import { migrateFilesToS3 } from "../helper/migration";
import { generateFile } from "../helper/fileGeneration.service";
import { ContractNameModel } from "../models/ContractNameModel";
import { RightToWorkCheckList } from "../models/RightToWorkCheckList";
import { StarterForm } from "../models/StarterForm";
import { UserFieldOrder } from "../models/UserFieldOrder";
import { HrmcForm } from "../models/HrmcForm";
import { json2csv } from "json-2-csv";


const refreshToken = async (req: Request, res: Response) => {
  try {
    const user = req.user;
    const token = generateToken(user);
    return res.json({ token });
  } catch (err) {
    return res
      .status(StatusCodes.SERVICE_UNAVAILABLE)
      .send({ error: "Error on token refresh" });
  }
};

/**
 *  Create a new User
 * @param req
 * @param res
 * @returns
 */

const createUser = async (req: any, res: Response) => {
  try {
    const { error: fileError } = await userValidator.user_avatar.validate(
      req.file,
    );

    /** Get User Data */
    const organization_id = req.user.organization_id;
    const keycloak_auth_id = req.user.keycloak_auth_id;

    if (fileError) {
      return res
        .status(400)
        .json({ status: false, message: fileError.details[0].message });
    }
    const { error } = await userValidator.addUser.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }

    /** check if as per subscription, staff count reached their limit. */
    const getStaffUserCount: any = await getStaffCount(organization_id)
    if (getStaffUserCount.status == false) {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("ERROR_STAFF_ADD_LIMIT", { limit: getStaffUserCount.staffCount }) });
    }
    const {
      user_first_name = "",
      user_last_name = "",
      user_middle_name = "",
      user_email = "",
      address_line1 = "",
      address_line2 = "",
      emergency_contact,
      country = "",
      pin_code,
      user_phone_number,
      user_designation = "",
      branch_id,
      department_id,
      user_gender,
      user_gender_other,
      marital_status,
      marital_status_other,
      date_of_birth,
      joining_date,
      role_ids = [],
      geo_country,
      geo_state,
      geo_city,
      assign_branch_ids = [],
      leave_policy_id = [],
    } = req.body;
    let employment_number = req.body.employment_number || null;
    // const role_ids = JSON.parse(req.body.role_ids) || [];
    const existUser: any = await User.findOne({
      where: { user_email, organization_id: organization_id },
      raw: true,
      nest: true,
    });
    if (existUser) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("USER_ALREADY_EXIST") });
    }

    const checkPermission = await userCreatePermitted(req.user.id, role_ids);
    if (!checkPermission) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    const findName = await roleName(role_ids);
    if (
      !_.some(
        [
          ROLE_CONSTANT.ADMIN,
          ROLE_CONSTANT.DIRECTOR,
          ROLE_CONSTANT.HR,
          ROLE_CONSTANT.ACCOUNTANT,
          ROLE_CONSTANT.AREA_MANAGER,
        ],
        (role: any) => _.includes(findName, role),
      )
    ) {
      if (!branch_id) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("BRANCH_REQUIRED") });
      }
      if (!department_id) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("DEPARTMENT_REQUIRED") });
      }
    }
    /** check if branch exist or not, throw error */
    if (branch_id) {
      const getBranchById: any = await Branch.findOne({
        attributes: ['id'], where: {
          id: branch_id, branch_status: { [Op.not]: branch_status.DELETED }, organization_id: organization_id
        }, raw: true
      });
      if (!getBranchById) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
      }
    }
    /** check if department exist or not, throw error */
    if (department_id) {
      const getDepartmentById: any = await Department.findOne({
        attributes: ["id"],
        where: {
          id: department_id,
          department_status: { [Op.not]: department_status.DELETED },
          organization_id: organization_id
        }, raw: true
      });
      if (!getDepartmentById) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("DEPARTMENT_NOT_FOUND") });
      }
    }

    const randomPassword = await GeneratePassword();
    const password = await encrypt(randomPassword);
    const findGeneralSetting: any = await getGeneralSettingObj(organization_id);
    if (findGeneralSetting?.employment_number_type == "manual") {
      if (employment_number) {
        const checkEmploymentNumberExist: any = await User.findOne({
          where: {
            employment_number,
            organization_id: organization_id,
            user_status: {
              [Op.notIn]: [user_status.DELETED, user_status.CANCELLED],
            },
          },
          raw: true,
          nest: true,
        });
        if (checkEmploymentNumberExist) {
          return res
            .status(StatusCodes.EXPECTATION_FAILED)
            .json({
              status: false,
              message: res.__("EMPLOYMENT_NUMBER_ALREADY_EXIST"),
            });
        }
      } else {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({
            status: false,
            message: res.__("EMPLOYMENT_NUMBER_REQUIRED"),
          });
      }
    } else {
      employment_number = await generateEmploymentNumber(organization_id);
    }

    const addUser: any = await User.setHeaders(req).create({
      user_first_name,
      user_avatar: req?.files?.[0] ? req?.files?.[0]?.item_id : "",
      user_middle_name: user_middle_name,
      user_last_name,
      user_email,
      user_phone_number,
      user_designation,
      emergency_contact,
      branch_id,
      user_gender_other: user_gender ? null : user_gender_other,
      marital_status_other: marital_status ? null : marital_status_other,
      department_id,
      address_line1: address_line1,
      address_line2: address_line2,
      country: country,
      pin_code: pin_code,
      user_gender: user_gender_other ? null : user_gender,
      marital_status: marital_status_other ? null : marital_status,
      date_of_birth: date_of_birth,
      user_joining_date: joining_date,
      user_status: user_status.PENDING,
      // req.user.id == 1 && findName.includes(ROLE_CONSTANT.ADMIN)
      //   ? user_status.VERIFIED
      //   : user_status.PENDING,
      user_password: password,
      created_by: req.user.id,
      updated_by: req.user.id,
      // web_user_active_role_id: role_ids[0],
      geo_country,
      geo_state,
      geo_city,
      employment_number: employment_number,
      organization_id: organization_id,
    } as any);
    // let templateData: any;

    if (addUser) {
      if (req?.files?.[0]?.item_id && req?.files?.[0]?.isMovable) {
        await moveFileInBucket(
          req.files[0].bucket,
          req.files[0].path,
          FILE_UPLOAD_CONSTANT.USER_PROFILE_API.destinationPath(
            organization_id,
            addUser.id,
            req.files[0].filename,
          ),
          req.files[0]?.item_id,
          true,
        );
      }

      for (const role_id of role_ids) {
        await UserRole.setHeaders(req).create({
          user_id: addUser.id,
          role_id: role_id,
          created_by: req.user.id,
        } as any);
      }
      if (assign_branch_ids.length > 0) {
        for (const branch_id of assign_branch_ids) {
          await UserBranch.setHeaders(req).create({
            user_id: addUser.id,
            branch_id: branch_id,
            created_by: req.user.id,
          } as any);
        }
      }
      if (leave_policy_id.length > 0) {
        for (const policy_id of leave_policy_id) {
          const checkPolicyExist = await LeaveAccuralPolicy.findOne({
            where: { id: policy_id, status: status.ACTIVE },
          });

          if (checkPolicyExist) {
            const findBalance: any =
              (await getCurrentLeaveBalance(
                moment(checkPolicyExist.leave_calender_year_start_from).year(),
                checkPolicyExist.stop_policy_accural_timewise_type,
                checkPolicyExist.stop_policy_accural_timewise_value,
                joining_date,
              )) || 0;
            await UserLeavePolicy.create({
              user_id: addUser.id,
              leave_accural_policy_id: policy_id,
              user_remaining_leave: findBalance.totalDays,
              created_by: req.user.id,
              updated_by: req.user.id,
              user_total_balance: findBalance.fullDays,
              leave_current_balance: findBalance.totalDays,
            } as any);
          }
        }
      }

      // Default Holiday assigned
      const policy_ids = await HolidayPolicy.findAll({
        attributes: ["id"],
        where: {
          holiday_type_id: [
            sequelize.literal(
              `(select id from nv_holiday_type where has_holiday_type_default = true AND holiday_type_status = 'active' AND organization_id = '${organization_id}')`,
            ),
          ],
        },
        raw: true,
      });
      if (policy_ids.length > 0) {
        for (const policy_id of policy_ids) {
          await UserHolidayPolicy.create({
            user_id: addUser?.id,
            holiday_policy_id: policy_id.id,
            created_by: req.user.id,
            updated_by: req.user.id,
          } as any);
        }
      }

      await UserWeekDay.create({
        user_id: addUser.id,
        user_weekday_status: user_weekday_status.ACTIVE,
        created_by: req.user.id,
        updated_by: req.user.id,
      } as any);
      const userRoles = await UserRole.findAll({
        where: { user_id: addUser.id },
        include: [
          {
            model: Role,
            as: "role",
            attributes: ["id", "role_name"],
          },
        ],
        nest: true,
        raw: true,
      });

      const webUserRolesIds: any = (
        userRoles.length > 0
          ? _.map(userRoles, (userRole: any) =>
            ADMIN_SIDE_USER.includes(userRole.role.role_name)
              ? userRole.role.id
              : null,
          )
          : []
      ).filter((id: null) => id !== null);
      const appUserRolesIds: any = (
        userRoles.length > 0
          ? _.map(userRoles, (userRole: any) =>
            NORMAL_USER.includes(userRole.role.role_name)
              ? userRole.role.id
              : null,
          )
          : []
      ).filter((id: null) => id !== null);

      if (webUserRolesIds.length > 0) {
        await User.update(
          { web_user_active_role_id: webUserRolesIds[0] } as any,
          { where: { id: addUser.id } },
        );
      }

      if (appUserRolesIds.length > 0) {
        await User.update({ user_active_role_id: appUserRolesIds[0] } as any, {
          where: { id: addUser.id },
        });
      }
      /** commented out below code, because now we are sending mail through rabbitmq. */
      // await sendInvitation([addUser.id], req.user.id)

      /** Store all staff details in keycloak */
      if (
        user_first_name ||
        user_last_name ||
        user_email ||
        user_phone_number
      ) {
        /** Prepare message for queue */
        const message = {
          user_first_name,
          user_last_name,
          user_email,
          user_phone_number,
          organization_id,
          keycloak_auth_id,
          randomPassword,
          userId: addUser.id,
          adminId: req.user.id,
        };
        /** Publish a message to the "staff creation/update" queue */
        const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_DETAILS;
        await rabbitmqPublisher.publishMessage(queue, message);
      }

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("USER_CREATION_SUCCESSED"),
        data: {
          id: addUser.id,
        },
      });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("USER_CREATION_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Update user detail
 * @param req
 * @param res
 * @returns
 */

const updateUser = async (req: any, res: Response) => {
  try {
    const {
      username = "",
      user_first_name = "",
      user_last_name = "",
      user_middle_name = "",
      user_email = "",
      address_line1 = "",
      address_line2 = "",
      country = "",
      pin_code,
      emergency_contact,
      user_phone_number,
      user_designation = "",
      branch_id,
      department_id,
      user_gender,
      user_gender_other,
      marital_status_other,
      marital_status,
      date_of_birth,
      role_ids = [],
      joining_date,
      assign_branch_ids = [],
      leave_policy_id = [],
      employment_number,
      // user_signature
    } = req.body;
    const { user_id } = req.params;
    const { error: fileError } = await userValidator.user_avatar.validate(
      req.file,
    );

    if (fileError) {
      return res
        .status(400)
        .json({ status: false, message: fileError.details[0].message });
    }

    const { error } = await userValidator.updateUser.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    const geo_country =
      req.body.geo_country == "null" ? null : req.body.geo_country;
    const geo_state = req.body.geo_state == "null" ? null : req.body.geo_state;
    const geo_city = req.body.geo_city == "null" ? null : req.body.geo_city;

    const checkPermission = await userUpdatePermitted(
      req.user.web_user_active_role_id,
      role_ids,
    );
    if (!checkPermission) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    const findUser: any = await User.findOne({
      attributes: ['id', 'user_email', 'web_user_active_role_id', 'user_active_role_id', 'user_avatar', 'user_signature', 'username', 'keycloak_auth_id', 'user_status', 'department_id', 'user_status'],
      where: {
        id: user_id,
        user_status: { [Op.notIn]: [user_status.DELETED, user_status.CANCELLED] },
        organization_id: req.user.organization_id
      },
      raw: true,
    });
    if (!findUser) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("USER_NOT_EXIST") });
    }

    const checkEmailExist: any = await User.findOne({
      attributes: ['id'],
      where: {
        user_email,
        organization_id: req.user.organization_id,
        id: { [Op.ne]: user_id }
      },
      raw: true,
    });
    if (checkEmailExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("USER_ALREADY_EXIST") });
    }

    const findGeneralSetting: any = await getGeneralSettingObj(
      req.user.organization_id,
    );

    if (role_ids.length > 0) {
      await UserRole.destroy({ where: { user_id: user_id } });
      for (const role_id of role_ids) {
        await UserRole.setHeaders(req).create({
          user_id: user_id,
          role_id: role_id,
          created_by: req.user.id,
        } as any);
      }

      if (assign_branch_ids.length > 0) {
        await UserBranch.destroy({ where: { user_id: user_id } });
        for (const branch_id of assign_branch_ids) {
          await UserBranch.setHeaders(req).create({
            user_id: user_id,
            branch_id: branch_id,
            created_by: req.user.id,
          } as any);
        }
      }

      const userRoles = await UserRole.findAll({
        where: { user_id: findUser.id },
        include: [
          {
            model: Role,
            as: "role",
            attributes: ["id", "role_name"],
          },
        ],
        nest: true,
        raw: true,
      });
      const loginUserRoles =
        userRoles.length > 0
          ? _.map(userRoles, (userRole: any) => userRole.role.role_name)
          : [];

      const webUserRolesIds: any = (
        userRoles.length > 0
          ? _.map(userRoles, (userRole: any) =>
            ADMIN_SIDE_USER.includes(userRole.role.role_name)
              ? userRole.role.id
              : null,
          )
          : []
      ).filter((id: null) => id !== null);
      const appUserRolesIds: any = (
        userRoles.length > 0
          ? _.map(userRoles, (userRole: any) =>
            NORMAL_USER.includes(userRole.role.role_name)
              ? userRole.role.id
              : null,
          )
          : []
      ).filter((id: null) => id !== null);

      const getCurrentRole: any = await getRoleName(
        findUser.web_user_active_role_id,
      );

      if (!loginUserRoles.includes(getCurrentRole[0]?.role_name)) {
        await User.update(
          {
            web_user_active_role_id:
              webUserRolesIds.length > 0 ? webUserRolesIds[0] : null,
          } as any,
          { where: { id: findUser.id } },
        );
      }

      const getAppCurrentRole: any = await getRoleName(
        findUser.user_active_role_id,
      );

      if (!loginUserRoles.includes(getAppCurrentRole[0]?.role_name)) {
        await User.update(
          {
            user_active_role_id:
              appUserRolesIds.length > 0 ? appUserRolesIds[0] : null,
          } as any,
          { where: { id: findUser.id } },
        );
      }
    }

    let user_avatar: any = findUser?.user_avatar ? findUser?.user_avatar : null;
    let user_signature: any = findUser?.user_signature ?? null;

    if (req.files?.user_avatar?.[0]?.item_id) {
      if (req.files?.user_avatar?.[0]?.isMovable) {
        await moveFileInBucket(
          req.files?.user_avatar?.[0]?.bucket,
          req.files?.user_avatar?.[0]?.path,
          FILE_UPLOAD_CONSTANT.USER_PROFILE_API.destinationPath(
            req.user.organization_id,
            user_id,
            req.files?.user_avatar?.[0]?.filename,
          ),
          req.files?.user_avatar?.[0]?.item_id,
          true,
        );
      }
      user_avatar = req.files?.user_avatar?.[0]?.item_id ?? user_avatar;
    }

    if (req.files?.user_signature?.[0]?.item_id) {
      if (req.files?.user_signature?.[0]?.isMovable) {
        await moveFileInBucket(
          req.files?.user_signature?.[0]?.bucket,
          req.files?.user_signature?.[0]?.path,
          FILE_UPLOAD_CONSTANT.USER_SIGNATURE_PATH.destinationPath(
            req.user.organization_id,
            user_id,
            req.files?.user_signature?.[0]?.filename,
          ),
          req.files?.user_signature?.[0]?.item_id,
          true,
        );
      }
      user_signature =
        req.files?.user_signature?.[0]?.item_id ?? user_signature;
    }

    if (findGeneralSetting?.employment_number_type == "manual") {
      if (employment_number) {
        const checkEmploymentNumberExist: any = await User.findOne({
          where: {
            employment_number,
            organization_id: req.user.organization_id,
            user_status: {
              [Op.notIn]: [user_status.DELETED, user_status.CANCELLED],
            },
          },
          raw: true,
          nest: true,
        });
        if (checkEmploymentNumberExist.id != findUser.id) {
          return res
            .status(StatusCodes.EXPECTATION_FAILED)
            .json({
              status: false,
              message: res.__("EMPLOYMENT_NUMBER_ALREADY_EXIST"),
            });
        }
      } else {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({
            status: false,
            message: res.__("EMPLOYMENT_NUMBER_REQUIRED"),
          });
      }
    }

    // const signatureName = `emp_signature_${user_first_name?.trim()}_${user_last_name?.trim()}_${findUser.id}.png`;
    // if (user_signature) {
    //   const destinationPath = path.resolve(
    //     __dirname,
    //     "..",
    //     "uploads",
    //     "signatures",
    //   );
    //   if (!fs.existsSync(destinationPath)) {
    //     fs.mkdirSync(destinationPath);
    //   }
    //   fs.writeFile(
    //     path.resolve(destinationPath, signatureName),
    //     user_signature.split(';base64,').pop(),
    //     "base64",
    //     () => {
    //       console.log("file written successfully");
    //     },
    //   );
    // }

    const updateUser = await User.setHeaders(req).update(
      {
        username: username ? username : findUser.username,
        user_first_name,
        user_avatar: user_avatar,
        user_middle_name: user_middle_name,
        user_last_name,
        user_email,
        user_phone_number,
        user_designation,
        branch_id: branch_id ? branch_id : null,
        emergency_contact,
        user_gender_other: user_gender ? null : user_gender_other,
        marital_status_other: marital_status ? null : marital_status_other,
        department_id: department_id ? department_id : null,
        address_line1: address_line1,
        address_line2: address_line2,
        country: country,
        pin_code: pin_code,
        user_gender: user_gender_other ? null : user_gender,
        marital_status: marital_status_other ? null : marital_status,
        date_of_birth: date_of_birth,
        user_joining_date: joining_date,
        updated_by: req.user.id,
        // web_user_active_role_id: role_ids[0],
        user_signature: user_signature,
        geo_country,
        geo_state,
        geo_city,
      } as any,
      {
        where: {
          id: findUser.id,
        },
      },
    );

    /** Update data in keycloak */
    if (
      user_first_name ||
      user_last_name ||
      user_email ||
      user_phone_number ||
      username
    ) {
      /** Prepare message for queue */
      const message = {
        username,
        user_first_name,
        user_last_name,
        user_email,
        user_phone_number,
        keycloak_auth_id: findUser.keycloak_auth_id,
        type: "staff_update",
      };
      /** Publish a message to the "staff creation/update" queue */
      const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_DETAILS;
      await rabbitmqPublisher.publishMessage(queue, message);
    }

    // let templateData: any;
    if (updateUser.length > 0) {
      if (findUser?.department_id != department_id) {
        const updateContract = await UserMeta.update(
          {
            user_id: findUser.id,
            department_template: null,
            isDraft: false,
            updated_by: req.user.id,
          } as any,
          { where: { user_id: findUser.id } },
        );
        if (updateContract.length > 0) {
          await regenerateEmploymentContractFuncation(
            user_id,
            req,
            false,
            false,
          );
        }
      }

      if (leave_policy_id.length > 0) {
        for (const policy_id of leave_policy_id) {
          const checkPolicyExist = await LeaveAccuralPolicy.findOne({
            where: { id: policy_id, status: status.ACTIVE },
          });

          if (checkPolicyExist) {
            const findExistingAssigned = await UserLeavePolicy.findOne({
              attributes: ['user_leave_policy_status'],
              where: { leave_accural_policy_id: checkPolicyExist.id, user_id: findUser.id }, raw: true
            });

            const findAllLeavePolicy = await LeaveAccuralPolicy.findAll({ attributes: ['id'], where: { leave_type_id: checkPolicyExist.leave_type_id, status: status.ACTIVE }, raw: true });
            if (findExistingAssigned) {
              if (
                findExistingAssigned.user_leave_policy_status ===
                user_leave_policy_status.INACTIVE
              ) {
                // Store user ID
                await UserLeavePolicy.update(
                  { user_leave_policy_status: user_leave_policy_status.ACTIVE },
                  {
                    where: {
                      leave_accural_policy_id: policy_id,
                      user_id: findUser.id,
                    },
                  },
                );
              }
            } else {
              await UserLeavePolicy.create({
                user_id: findUser.id,
                leave_accural_policy_id: policy_id,
                created_by: req.user.id,
                updated_by: req.user.id,
              } as any);
            }
            const policyIds =
              findAllLeavePolicy.length > 0
                ? findAllLeavePolicy.map((policy) => policy.id)
                : [];

            if (policyIds.length > 0 && policy_id) {
              await UserLeavePolicy.update(
                { user_leave_policy_status: user_leave_policy_status.INACTIVE },
                {
                  where: {
                    leave_accural_policy_id: {
                      [Op.in]: policyIds,
                      [Op.not]: policy_id,
                    }, // Exclude policy_id
                    user_id: findUser.id,
                  },
                },
              );
            }
          }
        }
      }

      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("USER_UPDATION_SUCCESSED") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("USER_UPDATION_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete User
 * @param req
 * @param res
 * @returns
 */

const deleteUser = async (req: Request, res: Response) => {
  try {
    const { user_id } = req.params;

    const getCurrentRole: any = await getRoleName(
      req.user.web_user_active_role_id,
    );

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getCurrentRole[0].role_name)
    ) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }
    if (user_id == req.user.id) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    const userExist = await findUserExist(user_id, req.user.organization_id);
    if (!userExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    // if (userExist.web_user_active_role_id < req.user.web_user_active_role_id) {
    //   return res
    //     .status(StatusCodes.FORBIDDEN)
    //     .json({ status: false, message: res.__("PERMISSION_DENIED") });
    // }

    const removeUser = await User.setHeaders(req).update(
      { user_status: user_status.DELETED },
      { where: { id: user_id } },
    );
    if (removeUser.length > 0) {
      await Activity.create({
        activity_table: "User",
        reference_id: user_id,
        activity_type: activity_type.SUCCESS,
        activity_action: activity_action.DELETED,
        ip_address: req.headers?.["ip-address"],
        userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
        address: req.headers?.["address"],
        location: req.headers?.["location"],
        organization_id: req.user.organization_id
          ? req.user.organization_id
          : null,
        created_by: user_id,
        updated_by: user_id,
      } as any);

      /** update user status in keycloak */
      /** Prepare message for queue */
      const message = {
        keycloak_auth_id: userExist.keycloak_auth_id,
        type: "staff_delete",
      };
      /** Publish a message to the "staff creation/update" queue */
      const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_DETAILS;
      await rabbitmqPublisher.publishMessage(queue, message);

      return res
        .status(StatusCodes.OK)
        .json({ status: false, message: res.__("USER_DELETED_SUCCESSFULL") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("USER_DELETION_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete account by user
 * @param req
 * @param res
 * @returns
 */

const deleteUserAccount = async (req: Request, res: Response) => {
  try {
    const findUserStatus: any = await User.findOne({
      attributes: ['id'],
      where: {
        id: req.user.id,
        organization_id: req.user.organization_id,
        user_status: {
          [Op.notIn]: [user_status.CANCELLED, user_status.DELETED],
        },
      },
    });
    if (findUserStatus) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("USER_NOT_EXIST") });
    }
    const findRequestExist = await UserRequest.findAll({
      attributes: ['id'],
      where: {
        from_user_id: findUserStatus.id,
        request_type: request_type.DELETE,
        request_status: request_status.PENDING,
      }, raw: true
    });

    if (findRequestExist.length > 0) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("SUCCESS_REQUEST_INPROGRESS") });
    }

    const sendDeleteRequest = await UserRequest.setHeaders(req).create({
      from_user_id: req.user.id,
      request_type: request_type.DELETE,
      request_status: request_status.PENDING,
      created_by: req.user.id,
      updated_by: req.user.id,
    } as any);

    if (sendDeleteRequest) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_DELETE_REQUEST_APPLIED"),
      });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: true, message: res.__("FAI;_DELETE_REQUEST") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Reset password
 * @param req
 * @param res
 * @returns
 */

const resetPassword = async (req: Request, res: Response) => {
  try {
    const { old_password = "", new_password = "" } = req.body;
    if (old_password == new_password) {
      return res.status(StatusCodes.NOT_ACCEPTABLE).json({
        status: false,
        message: res.__("FAIL_NEW_PASSWORD_MUST_DIFFERENT"),
      });
    }
    const findUser: any = await User.findOne({ attributes: ['id', 'user_password', 'user_status'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true });
    const isMatch = await comparePassword(old_password, findUser.user_password);
    if (!isMatch) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .send({ status: false, message: res.__("OLD_PASSWORD_WRONG") });
    }
    const passwordUpdated = await User.setHeaders(req).update(
      {
        user_password: await encrypt(new_password),
        user_status:
          findUser.user_status == user_status.PENDING
            ? user_status.ACTIVE
            : findUser.user_status,
        updated_by: req.user.id,
      },

      { where: { id: req.user.id } },
    );
    if (passwordUpdated.length > 0) {
      const findUserInvitation = await UserInvite.findOne({
        where: {
          user_id: req.user.id,
          invitation_status: { [Op.not]: invitation_status.ACCEPTED },
        },
      });
      if (findUserInvitation) {
        await UserInvite.update(
          {
            invitation_status: invitation_status.ACCEPTED,
            action_by: req.user.id,
            updated_by: req.user.id,
          },
          { where: { id: findUserInvitation.id } },
        );
      }
      return res
        .status(StatusCodes.OK)
        .send({ status: true, message: res.__("RESET_PASSWORD_SUCCESSFULL") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .send({ status: false, message: res.__("RESET_PASSWORD_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Set login pin
 * @param req
 * @param res
 * @returns
 */

const setLoginPin = async (req: Request, res: Response) => {
  try {
    const { is_login_pin, pin } = req.body;

    const findUserDetail: any = await User.findOne({
      attributes: ['id', 'keycloak_auth_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });

    if (!findUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("USER_NOT_EXIST") });
    }

    let updateObj = {
      user_login_pin: await encrypt(pin),
      is_login_pin: is_login_pin,
    };
    if (!is_login_pin) {
      updateObj = { user_login_pin: null, is_login_pin: is_login_pin };
    }

    const setLoginPin = await User.setHeaders(req).update(updateObj, {
      where: { id: req.user.id },
    });

    /** Prepare message for queue to update login pin status */
    const message = {
      is_login_pin: is_login_pin,
      type: "staff_update",
      keycloak_auth_id: findUserDetail.keycloak_auth_id,
    };
    /** Publish a message to the "staff creation/update" queue */
    const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_DETAILS;
    await rabbitmqPublisher.publishMessage(queue, message);

    if (setLoginPin.length > 0) {
      return res
        .status(StatusCodes.OK)
        .send({ status: true, message: res.__("LOGIN_PIN_SUCCESSFULL") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .send({ status: false, message: res.__("LOGIN_PIN_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Reset login pin
 * @param req
 * @param res
 * @returns
 */

const resetLoginPin = async (req: Request, res: Response) => {
  try {
    const { old_pin, new_pin } = req.body;
    if (req.user && req.user.is_login_pin) {
      const findUserDetail: any = await User.findOne({
        attributes: ['id', 'user_login_pin'],
        where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
      });
      const isMatch = await comparePassword(
        old_pin,
        findUserDetail.user_login_pin,
      );
      if (!isMatch) {
        return res
          .status(StatusCodes.FORBIDDEN)
          .send({ status: false, message: res.__("OLD_PASSWORD_WRONG") });
      }
      if (old_pin == new_pin) {
        return res.status(StatusCodes.NOT_ACCEPTABLE).json({
          status: false,
          message: res.__("OLD_PIN_AND_NEW_PIN_match"),
        });
      }

      const passwordUpdated = await User.setHeaders(req).update(
        { user_login_pin: await encrypt(new_pin), updated_by: req.user.id },
        { where: { id: req.user.id } },
      );
      if (passwordUpdated.length > 0) {
        return res
          .status(StatusCodes.OK)
          .send({ status: true, message: res.__("RESET_PIN_SUCCESSFUL") });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .send({ status: false, message: res.__("RESET_PIN_FAILED") });
      }
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .send({ status: false, message: res.__("SET_PIN_FIRST") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const userCreatePermitted = async (user_id: any, role_ids: []) => {
  try {
    const checkPermission = await userCreatePermission(user_id);
    if (!checkPermission) {
      return false;
    }
    const findName = await roleName(role_ids);
    if (_.includes(findName, ROLE_CONSTANT.SUPER_ADMIN)) {
      return false;
    }
    const findUserCurrentRole = await User.findOne({ where: { id: user_id }, raw: true });

    /** Get all super admin Id's */
    const getUserIds = await getSuperAdminUserId(findUserCurrentRole?.organization_id)
    /** Removed static condition of super admin and add super admin id's array condition */
    if (
      !_.includes(getUserIds, user_id) &&
      _.includes(findName, ROLE_CONSTANT.ADMIN)
    ) {
      return false;
    }

    if (!findUserCurrentRole) {
      return false;
    }
    const getRoleName = await Role.findOne({
      where: { id: findUserCurrentRole?.web_user_active_role_id },
      attributes: ["role_name"],
      raw: true,
    });
    if (_.includes(findName, getRoleName?.role_name)) {
      return false;
    }

    const currentUserParent = await findParentUserRole(user_id);

    const uniqueParentIds = _.uniq(currentUserParent);
    const foundInRoles = _.map(uniqueParentIds, (parentId: any) =>
      _.includes(role_ids, parentId),
    );
    if (_.includes(foundInRoles, "true")) {
      return false;
    }
    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
};

const userUpdatePermitted = async (role_id: any, role_ids: []) => {
  try {
    if (
      role_id &&
      role_id != 1 &&
      role_id != 2 &&
      role_id != 3 &&
      role_id != 4 &&
      role_id != 7
    ) {
      return false;
    }
    const findName = await roleName(role_ids);
    if (role_id != 1 && _.includes(findName, ROLE_CONSTANT.ADMIN)) {
      return false;
    }
    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
};

/**
 *  Update profile
 * @param req
 * @param res
 * @returns
 */

const updateUserProfile = async (req: any, res: Response) => {
  try {
    const { error } = await userValidator.updateProfile.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    const { error: fileError } = await userValidator.user_avatar.validate(
      req.file,
    );

    if (fileError) {
      return res
        .status(400)
        .json({ status: false, message: fileError.details[0].message });
    }

    const findUserDetail: any = await User.findOne({
      attributes: ['id', 'keycloak_auth_id', 'user_avatar'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });

    /** if user not found then throw error */
    if (!findUserDetail) {
      return res
        .status(400)
        .json({ status: false, message: res.__("USER_NOT_FOUND") });
    }

    const {
      username,
      user_first_name,
      user_last_name,
      user_email,
      user_middle_name = "",
      address_line1 = "",
      address_line2 = "",
      country = "",
      user_gender,
      user_gender_other,
      marital_status,
      marital_status_other,
      date_of_birth,
      user_signature,
    } = req.body;
    const emergency_contact =
      req.body.emergency_contact == "null" ? null : req.body.emergency_contact;
    const user_phone_number =
      req.body.user_phone_number == "null" ? null : req.body.user_phone_number;
    const pin_code = req.body.pin_code == "null" ? null : req.body.pin_code;
    const geo_country =
      req.body.geo_country == "null" ? null : req.body.geo_country;
    const geo_state = req.body.geo_state == "null" ? null : req.body.geo_state;
    const geo_city = req.body.geo_city == "null" ? null : req.body.geo_city;
    let signatureItemId = null;
    if (user_signature) {
      // Convert base64 to buffer
      const signatureBuffer = Buffer.from(user_signature.split(";base64,").pop(), 'base64');
      const iSignatureBuffer = Buffer.from(user_signature.split(";base64,").pop())
      const fileName = `emp_signature_${user_first_name?.trim()}_${user_last_name?.trim()}_${findUserDetail.id}.png`;
      const filePath = FILE_UPLOAD_CONSTANT.USER_SIGNATURE_PATH.destinationPath(
        req.user.organization_id,
        findUserDetail.id,
        fileName
      );

      const fileHash: any = await getHash(iSignatureBuffer, {
        path: filePath,
        originalname: fileName,
        mimetype: "image/png",
        size: Buffer.byteLength(iSignatureBuffer),
      }, "image/png");

      try {
        // Upload to S3
        await s3.send(
          new PutObjectCommand({
            Bucket: process.env.NODE_ENV,
            Key: filePath,
            Body: signatureBuffer,
            ContentType: 'image/png',
          })
        );

        // Save item record
        const saveItem: any = {
          item_type: item_type.IMAGE,
          item_name: fileName,
          item_hash: fileHash.hash,
          item_mime_type: 'image/png',
          item_extension: '.png',
          item_size: Buffer.byteLength(iSignatureBuffer),
          item_IEC: item_IEC.B,
          item_status: item_status.ACTIVE,
          item_external_location: item_external_location.NO,
          item_location: filePath,
          item_organization_id: req.user.organization_id,
        };

        const item = await Item.create(saveItem as any);
        signatureItemId = item.id;
      } catch (error) {
        console.error("Error uploading signature to S3:", error);
      }
    }
    const user_avatar = req.files[0]
      ? req.files?.[0]?.item_id
      : findUserDetail?.user_avatar;
    if (req?.files?.[0]?.isMovable) {
      await moveFileInBucket(
        req.files[0].bucket,
        req.files[0].path,
        FILE_UPLOAD_CONSTANT.USER_PROFILE_API.destinationPath(
          req.user.organization_id,
          findUserDetail.id,
          req.files[0].filename,
        ),
        req.files[0]?.item_id,
        true,
      );
    }
    const updateUser = await User.setHeaders(req).update(
      {
        username,
        user_first_name,
        user_last_name,
        user_email,
        user_avatar: user_avatar,
        user_middle_name: user_middle_name,
        user_phone_number: user_phone_number,
        emergency_contact: emergency_contact,
        user_gender_other: user_gender ? null : user_gender_other,
        marital_status_other: marital_status ? null : marital_status_other,
        address_line1: address_line1,
        address_line2: address_line2,
        country: country,
        pin_code: pin_code,
        user_gender: user_gender_other ? null : user_gender,
        marital_status: marital_status_other ? null : marital_status,
        date_of_birth: date_of_birth,
        updated_by: req.user.id,
        user_signature: signatureItemId || findUserDetail.user_signature,
        geo_country,
        geo_state,
        geo_city,
      } as any,
      {
        where: {
          id: req.user.id,
        },
      },
    );
    /** Publish queue for update user data in keycloak */
    /** Update data in keycloak */
    if (
      user_first_name ||
      user_last_name ||
      user_email ||
      user_phone_number ||
      username
    ) {
      /** Prepare message for queue */
      const message = {
        username,
        user_first_name,
        user_last_name,
        user_email,
        user_phone_number,
        keycloak_auth_id: findUserDetail.keycloak_auth_id,
        type: "staff_update",
      };
      /** Publish a message to the "staff creation/update" queue */
      const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_DETAILS;
      await rabbitmqPublisher.publishMessage(queue, message);
    }

    if (updateUser.length > 0) {
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("USER_PROFILE_UPDATED") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("USER_PROFILE_NOT_UPDATED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  View profile
 * @param req
 * @param res
 * @returns
 */

const viewProfile = async (req: Request, res: Response) => {
  try {
    let getProfile: any = await User.findOne({
      attributes: [
        "id",
        "user_first_name",
        "user_middle_name",
        "user_last_name",
        "user_email",
        "address_line1",
        "address_line2",
        "country",
        "pin_code",
        "user_phone_number",
        "emergency_contact",
        "user_designation",
        "user_avatar",
        "user_status",
        "user_gender",
        "user_gender_other",
        "marital_status",
        "marital_status_other",
        "user_joining_date",
        "user_signature",
        "date_of_birth",
        "user_active_role_id",
        "web_user_active_role_id",
        "confirm_by_date",
        "appToken",
        "webAppToken",
        "last_reject_remark",
        "user_verification_doc",
        [
          sequelize.literal(
            `(select CONCAT( IF(	user_first_name IS NULL,'',	user_first_name), IF(	user_first_name IS NOT NULL AND 	user_first_name != '',' ',''), IF( user_last_name IS NULL, '', user_last_name )) from nv_users where id = confirm_by )`,
          ),
          "confirm_by",
        ],
        "createdAt",
        "geo_country",
        "geo_state",
        "geo_city",
        "employment_number",
        "username",
      ],
      include: [
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "branch_name"],
        },
        {
          model: Department,
          as: "department",
          attributes: ["id", "department_name"],
        },
      ],
      where: { id: req.user.id },
      raw: true,
      nest: true,
    });
    getProfile = JSON.parse(JSON.stringify(getProfile));
    const selectQuery = `SELECT COUNT(*) as notification_count FROM notifications WHERE to_user_id = ${req.user.id} AND notification_status = 'sent'`;
    const findNotificationCount = await sequelize.query(selectQuery, { type: sequelize.QueryTypes.SELECT })
    let findUserInvitation: any = await UserInvite.findOne({
      attributes: ["id", "invitation_status", "action_by", "updatedAt"],
      where: { user_id: getProfile.id },
      raw: true,
      nest: true,
    });
    findUserInvitation = JSON.parse(JSON.stringify(findUserInvitation));
    if (findUserInvitation && findUserInvitation.action_by) {
      const findAction = await User.findOne({
        attributes: [
          "id",
          [
            sequelize.fn(
              "concat",
              sequelize.col("user_first_name"),
              " ",
              sequelize.col("user_last_name"),
            ),
            "user_full_name",
          ],
        ],
        where: { id: findUserInvitation.action_by },
      });
      findUserInvitation.action_by = findAction;
    }
    const userRoles = await UserRole.findAll({
      where: { user_id: req.user.id },
      include: [
        {
          model: Role,
          as: "role",
          attributes: ["id", "role_name"],
        },
      ],
      nest: true,
      raw: true,
    });
    const userbranches = await UserBranch.findAll({
      where: { user_id: req.user.id },
      include: [
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "branch_name", "branch_color"],
        },
      ],
      nest: true,
      raw: true,
    });
    if (getProfile.branch_id) {
      const getBranchSetting: any = await Branch.findOne({
        attributes: ["id", "branch_sign", "branch_employer_name"],
        where: {
          id: getProfile.branch_id,
          branch_status: branch_status.ACTIVE,
        },
      });
      if (getBranchSetting) {
        getProfile.branch_sign = getBranchSetting.branch_sign
          ? URL_CONSTANT.SIGNATURE_URL(getBranchSetting.branch_sign)
          : "";
      }
    } else {
      getProfile.branch_sign = "";
    }
    getProfile.user_roles =
      userRoles.length > 0
        ? _.map(userRoles, (userRole: any) => userRole.role)
        : [];
    getProfile.switch_user_roles =
      userRoles.length > 0
        ? userRoles
          .filter((userRole: any) =>
            req.headers["platform-type"] == "web"
              ? ADMIN_SIDE_USER.includes(userRole.role.role_name) && userRole
              : NORMAL_USER.includes(userRole.role.role_name) && userRole,
          )
          .map((userRole: any) => userRole.role)
        : [];

    getProfile.user_avatar_id = getProfile.user_avatar;
    getProfile.user_signature_id = getProfile.user_signature
    if (getProfile.user_avatar) {
      const avatarItem = await Item.findOne({
        where: { id: getProfile.user_avatar },
        attributes: ["item_location"],
        raw: true,
      });
      getProfile.user_avatar = avatarItem?.item_location
        ? `${global.config.API_BASE_URL}${avatarItem.item_location}`
        : "";
    } else {
      getProfile.user_avatar = "";
    }
    if (getProfile.user_signature) {
      const signatureItem = await Item.findOne({
        where: { id: getProfile.user_signature },
        attributes: ["item_location"],
        raw: true,
      });
      getProfile.user_signature = signatureItem?.item_location
        ? `${global.config.API_BASE_URL}${signatureItem.item_location}`
        : "";
    } else {
      getProfile.user_signature = "";
    }
    getProfile.profile_status = await checkUserProfileComplete(getProfile.id);
    getProfile.user_invite = findUserInvitation || {}
    getProfile.pending_notifications = findNotificationCount[0]?.notification_count || 0
    const getDocItem = getProfile.user_verification_doc && !isNaN(getProfile.user_verification_doc) ? await Item.findOne({ where: { id: Number(getProfile.user_verification_doc) } }) : 0
    getProfile.user_verification_doc_link = getDocItem && getDocItem?.item_location ? `${global.config.API_BASE_URL}${getDocItem?.item_location}` : getProfile.user_verification_doc
      ? URL_CONSTANT.USER_VERIFICATION_DOC_URL(
        getProfile?.user_verification_doc,
      )
      : "";
    getProfile.vat_per_data = global.config.VAT_PER_DATA;
    const generalSettings = await getGeneralSettingObj(
      req.user.organization_id,
    );
    getProfile.currency_details = generalSettings?.currency
      ? JSON.parse(generalSettings?.currency)
      : {};

    // General Settings
    let setting = await Setting.findOne({
      where: { key: "base_leave", organization_id: req.user.organization_id },
      raw: true,
    });
    getProfile.base_leave = setting ? Number(setting.value) : 0;

    setting = await Setting.findOne({
      where: {
        key: "working_hours_per_day",
        organization_id: req.user.organization_id,
      },
      raw: true,
    });
    getProfile.working_hours_per_day = setting ? Number(setting.value) : 0; // Maximum hours per week from settings

    setting = await Setting.findOne({
      where: {
        key: "max_limit_per_week",
        organization_id: req.user.organization_id,
      },
      raw: true,
    });
    getProfile.max_limit_per_week = setting ? Number(setting.value) : 0; // Maximum hours per week from settings
    const findAnnualExist = await LeaveTypeModel.findOne({
      where: { has_annual_leave: true, status: leaveTypeStatus.ACTIVE },
    });
    getProfile.has_annual_leave = findAnnualExist ? findAnnualExist : {};
    getProfile.geo_country = await getGeoDetails(getProfile.geo_country);
    getProfile.geo_state = await getGeoDetails(getProfile.geo_state);
    getProfile.geo_city = await getGeoDetails(getProfile.geo_city);
    getProfile.assign_branch_ids =
      userbranches.length > 0
        ? _.map(userbranches, (userBranch: any) => userBranch.branch)
        : [];
    getProfile.user_week_day =
      (await UserWeekDay.findOne({ where: { user_id: req.user.id } })) || {};
    const findUserPolicy = await LeaveTypeModel.findAll({
      attributes: [
        "id",
        "name",
        "leave_deduction_type",
        "leave_type_color",
        "leave_period_type",
      ],
      where: { status: status.ACTIVE },
      include: [
        {
          model: LeaveAccuralPolicy,
          as: "leave_accural_policy",
          where: {
            id: {
              [Op.in]: [
                sequelize.literal(`(
                select leave_accural_policy_id from nv_user_leave_policy 
                where user_id = ${req.user.id} AND user_leave_policy_status = '${user_leave_policy_status.ACTIVE}'
              )`),
              ],
            },
            status: status.ACTIVE,
          },
          attributes: [
            "id",
            "leave_calender_year_start_from",
            "leave_policy_end_date",
            "stop_policy_accural_timewise_type",
            "stop_policy_accural_timewise_value",
            "leave_policy_accural",
            "leave_policy_name",
            "has_leave_policy_default",
          ],
        },
      ],
      raw: true,
      nest: true,
    });
    getProfile.leave_policy_list = findUserPolicy;

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getProfile,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get user by id
 * @param req
 * @param res
 * @returns
 */

const getUserById = async (req: Request, res: Response) => {
  try {
    const { user_id } = req.params;
    let getProfile: any = await User.findOne({
      include: [
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "branch_name"],
          // where: { organization_id: req.user.organization_id }
        },
        {
          model: Department,
          as: "department",
          attributes: ["id", "department_name"],
          // where: { organization_id: req.user.organization_id }
        },
        {
          model: UserMeta,
          as: "user_meta",
        },
        {
          model: UserEmploymentContract,
          as: "user_contract",
          attributes: [
            "id",
            "contract_status",
            "expire_date",
            "is_confirm_sign",
          ],
          where: { contract_status: contractStatus.ACTIVE },
          order: [["createdAt", "desc"]],
          required: false,
        },
      ],
      where: { id: user_id, organization_id: req.user.organization_id },
      raw: true,
      nest: true,
    });
    if (!getProfile) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ERROR_USER_NOT_FOUND"),
      });
    }
    const findConfirmByUser = await User.findOne({ attributes: ['id', 'user_first_name', 'user_last_name'], where: { id: getProfile.confirm_by, organization_id: req.user.organization_id }, raw: true })
    if (getProfile.confirm_by != null) {
      getProfile.confirm_by =
        findConfirmByUser?.user_first_name?.trim() +
        " " +
        findConfirmByUser?.user_last_name?.trim();
    }
    let findUserInvitation: any = await UserInvite.findOne({
      attributes: ["id", "invitation_status", "action_by", "updatedAt"],
      where: { user_id: getProfile.id },
      raw: true,
      nest: true,
    });
    findUserInvitation = JSON.parse(JSON.stringify(findUserInvitation));
    if (findUserInvitation && findUserInvitation.action_by) {
      const findAction = await User.findOne({
        attributes: ['id', [
          sequelize.fn(
            "concat",
            sequelize.col("user_first_name"),
            " ",
            sequelize.col("user_last_name"),
          ),
          "user_full_name",
        ]], where: { id: findUserInvitation.action_by, organization_id: req.user.organization_id }
      })
      if (!findAction) {
        return res.status(StatusCodes.NOT_FOUND).json({
          status: false,
          message: res.__("ERROR_USER_NOT_FOUND"),
        });
      }
      findUserInvitation.action_by = findAction
    }
    getProfile = JSON.parse(JSON.stringify(getProfile));
    getProfile.user_invitation_action = findUserInvitation
      ? findUserInvitation
      : {};
    getProfile.user_avatar_id = getProfile.user_avatar;
    getProfile.user_signature_id = getProfile.user_signature;
    if (getProfile.user_avatar) {
      const avatarItem = await Item.findOne({
        where: { id: getProfile.user_avatar },
        attributes: ["item_location"],
        raw: true,
      });
      getProfile.user_avatar = avatarItem?.item_location
        ? `${global.config.API_BASE_URL}${avatarItem.item_location}`
        : "";
    } else {
      getProfile.user_avatar = "";
    }
    if (getProfile.user_signature) {
      const signatureItem = await Item.findOne({
        where: { id: getProfile.user_signature },
        attributes: ["item_location"],
        raw: true,
      });
      getProfile.user_signature = signatureItem?.item_location
        ? `${global.config.API_BASE_URL}${signatureItem.item_location}`
        : "";
    } else {
      getProfile.user_signature = "";
    }
    const getDocItem = getProfile.user_verification_doc && !isNaN(getProfile.user_verification_doc) ? await Item.findOne({ where: { id: Number(getProfile.user_verification_doc) } }) : 0
    getProfile.user_verification_doc_link = getDocItem && getDocItem?.item_location ? `${global.config.API_BASE_URL}${getDocItem?.item_location}` : getProfile.user_verification_doc
      ? URL_CONSTANT.USER_VERIFICATION_DOC_URL(
        getProfile?.user_verification_doc,
      )
      : "";
    // General Settings
    let setting = await Setting.findOne({
      where: { key: "base_leave", organization_id: req.user.organization_id },
      raw: true,
    });
    getProfile.base_leave = setting ? Number(setting.value) : 0;

    setting = await Setting.findOne({
      where: {
        key: "working_hours_per_day",
        organization_id: req.user.organization_id,
      },
      raw: true,
    });
    getProfile.working_hours_per_day = setting ? Number(setting.value) : 0; // Maximum hours per week from settings

    setting = await Setting.findOne({
      where: {
        key: "max_limit_per_week",
        organization_id: req.user.organization_id,
      },
      raw: true,
    });
    getProfile.max_limit_per_week = setting ? Number(setting.value) : 0; // Maximum hours per week from settings

    const findAnnualExist = await LeaveTypeModel.findOne({ where: { has_annual_leave: true, status: leaveTypeStatus.ACTIVE, organization_id: req.user.organization_id }, raw: true })
    getProfile.has_annual_leave = findAnnualExist ? findAnnualExist : {}

    const userRoles = await UserRole.findAll({
      where: { user_id: user_id },
      include: [
        {
          model: Role,
          as: "role",
          attributes: ["id", "role_name"],
        },
      ],
      nest: true,
      raw: true,
    });
    if (getProfile.branch_id) {
      const getBranchSetting: any = await Branch.findOne({ attributes: ['id', 'branch_sign', 'branch_employer_name'], where: { id: getProfile.branch_id, branch_status: branch_status.ACTIVE, organization_id: req.user.organization_id }, raw: true })
      if (getBranchSetting) {
        getProfile.branch_sign = getBranchSetting.branch_sign
          ? URL_CONSTANT.SIGNATURE_URL(getBranchSetting.branch_sign)
          : "";
      }
    } else {
      getProfile.branch_sign = "";
    }
    const userbranches = await UserBranch.findAll({
      where: { user_id: user_id },
      include: [
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "branch_name", "branch_color"],
        },
      ],
      nest: true,
      raw: true,
    });
    getProfile.user_roles =
      userRoles.length > 0
        ? _.map(userRoles, (userRole: any) => userRole.role)
        : [];

    getProfile.geo_country = await getGeoDetails(getProfile.geo_country);
    getProfile.geo_state = await getGeoDetails(getProfile.geo_state);
    getProfile.geo_city = await getGeoDetails(getProfile.geo_city);
    getProfile.assign_branch_ids =
      userbranches.length > 0
        ? _.map(userbranches, (userBranch: any) => userBranch.branch)
        : [];
    getProfile.user_week_day =
      (await UserWeekDay.findOne({ where: { user_id: user_id } })) || {};
    const findUserPolicy = await LeaveTypeModel.findAll({
      attributes: ['id', 'name', 'leave_deduction_type', 'leave_type_color', 'leave_period_type'],
      where: { status: status.ACTIVE, organization_id: req.user.organization_id },
      include: [
        {
          model: LeaveAccuralPolicy,
          as: "leave_accural_policy",
          where: {
            id: {
              [Op.in]: [
                sequelize.literal(`(
                select leave_accural_policy_id from nv_user_leave_policy 
                where user_id = ${user_id} AND user_leave_policy_status = '${user_leave_policy_status.ACTIVE}'
              )`),
              ],
            },
            status: status.ACTIVE,
          },
          attributes: [
            "id",
            "leave_calender_year_start_from",
            "leave_policy_end_date",
            "stop_policy_accural_timewise_type",
            "stop_policy_accural_timewise_value",
            "leave_policy_accural",
            "leave_policy_name",
            "has_leave_policy_default",
          ],
        },
      ],
      raw: true,
      nest: true,
    });
    getProfile.leave_policy_list = findUserPolicy;

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getProfile,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  User list with filter and pagination
 * @param req
 * @param res
 * @returns
 */

const getUserList = async (req: Request, res: Response) => {
  try {
    // Get the filters from request query params
    const {
      size,
      page,
      search,
      branch_id,
      role_id,
      department_id,
      isAdmin,
      status,
      contract_status,
      user_track_status,
      isRotaList,
    }: any = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));

    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'branch_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const findUserRole: any = await Role.findOne({
      where: { id: getUserDetail.web_user_active_role_id },
      raw: true,
    });
    /** Get all super admin user id's */

    const getUserId = await getSuperAdminUserId(req.user?.organization_id)

    const excludedIds = [...getUserId]; // Combine req.user.id with fetched IDs
    const whereObj: any = {
      user_status: {
        [Op.not]: [
          user_status.CANCELLED,
          user_status.DELETED,
        ],
      },
      id: {
        [Op.not]: excludedIds,
      },
      organization_id: req.user.organization_id,
    };

    /** Hide 1 developer user on production server */
    if (process.env.NEXT_NODE_ENV == "production") {
      whereObj.id = { [Op.not]: [1, 2] };
    }

    let contractWhere: any = {
      contract_status: contractStatus.ACTIVE,
    };
    const contractQuery = `  (
    IF (
      EXISTS (
        SELECT 1
        FROM nv_user_meta 
        WHERE user_id = User.id
        AND nv_user_meta.probation_length IS NOT NULL 
        AND nv_user_meta.probation_length != 0
        AND DATE_ADD(user_joining_date, INTERVAL nv_user_meta.probation_length DAY) > CURDATE()
      ), true, false
    )
  )`;

    if (contract_status && contract_status != "") {
      const currentDate = moment().toDate();
      const futureDate = moment().add(15, "days").toDate();

      switch (contract_status) {
        case "expired":
          contractWhere.expire_date = { [Op.lte]: currentDate };
          break;
        case "expiry-soon":
          contractWhere.expire_date = {
            [Op.gt]: currentDate,
            [Op.lte]: futureDate,
          };
          break;
        case "active":
          contractWhere.contract_status = contractStatus.ACTIVE;
          contractWhere.expire_date = {
            [Op.or]: [{ [Op.gte]: futureDate }, null],
          };
          contractWhere.is_confirm_sign = true;
          contractWhere = {
            ...contractWhere,
            [Op.and]: Sequelize.where(sequelize.literal(contractQuery), {
              [Op.eq]: 0,
            }),
          };
          break;
        case "probation":
          contractWhere.contract_status = contractStatus.ACTIVE;
          contractWhere.expire_date = {
            [Op.or]: [{ [Op.gte]: futureDate }, null],
          };
          contractWhere.is_confirm_sign = true;
          // Remove the contractQuery and instead use this condition to check for probation:
          contractWhere = {
            ...contractWhere,
            [Op.and]: Sequelize.where(
              Sequelize.literal(`
          (
            EXISTS (
              SELECT 1
              FROM nv_user_meta 
              WHERE user_id = User.id
              AND probation_length IS NOT NULL 
              AND probation_length != 0
              AND DATE_ADD(user_joining_date, INTERVAL probation_length DAY) > CURDATE()
            )
          )
        `),
              { [Op.eq]: true }, // Match true for users in probation
            ),
          };
          break;
        case "awaiting-signature":
          contractWhere.contract_status = contractStatus.ACTIVE;
          contractWhere.expire_date = {
            [Op.or]: [{ [Op.gte]: futureDate }, null],
          };
          contractWhere.is_confirm_sign = false;
          break;
        default:
          contractWhere.contract_status = contract_status;
          break;
      }
    }
    if (branch_id) {
      whereObj.branch_id = { [Op.in]: branch_id.split(",") };
    }
    if (department_id) {
      whereObj.department_id = { [Op.in]: department_id.split(",") };
    }
    if (status) {
      // whereObj.user_status[Op.not] = [
      //   ...whereObj.user_status[Op.not],
      //   user_status.VERIFIED,
      //   user_status.COMPLETED,
      // ];
      whereObj.user_status = status;
    }

    if (isAdmin == "true") {
      // whereObj.user_active_role_id = 2;
      whereObj[Op.and] = [
        sequelize.literal(
          `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id IN (2)))`,
        ),
      ];
    } else if (isAdmin == "false") {
      // whereObj.user_active_role_id = { [Op.not]: [1, 2] };
      whereObj[Op.and] = [
        sequelize.literal(
          `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1,2))) `,
        ),
      ];
    } else {
      whereObj[Op.and] = [
        sequelize.literal(
          `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1))) `,
        ),
      ];
    }


    const userTrackStatusQuery = ` (
          CASE
              WHEN EXISTS (select status from nv_user_checklist where to_user_id = User.id AND from_user_id = User.id AND checklist_id = 3)
              THEN (select status from nv_user_checklist where to_user_id = User.id AND from_user_id = User.id AND checklist_id = 3)
              ELSE 'pending'
          END)`;

    if (user_track_status && user_track_status != "") {
      whereObj[Op.and].push(
        Sequelize.where(Sequelize.literal(userTrackStatusQuery), {
          [Op.eq]: `${user_track_status}`,
        })
      );
    }

    if (
      findUserRole &&
      (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.SIGNATURE) &&
      getUserDetail.branch_id
    ) {
      whereObj.branch_id = getUserDetail.branch_id;
      // Construct recursive query to find child roles
      const getChildRolesQuery = `
        WITH RECURSIVE ChildRoles AS (
          SELECT id, role_name, parent_role_id
          FROM nv_roles
          WHERE id = :activeRoleId
          UNION ALL
          SELECT r.id, r.role_name, r.parent_role_id
          FROM nv_roles r
          INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
        )
        SELECT id
        FROM ChildRoles
        WHERE id != :activeRoleId`;

      // Execute recursive query to find child roles
      const getChildRoles = await sequelize.query(getChildRolesQuery, {
        replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
        type: QueryTypes.SELECT,
      });

      // Build WHERE clause for user roles based on child roles
      let whereStr = "";
      getChildRoles.forEach((child_role: any, index: number) => {
        if (index > 0) {
          whereStr += " OR ";
        }
        whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = User.id)) > 0`;
      });
      // Include only the logged-in user with their role
      if (whereStr) {
        whereStr += ` OR User.id=${req.user.id}`;
        whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
      }
    }
    // Search
    if (search) {
      whereObj[Op.or] = [
        Sequelize.where(
          Sequelize.fn(
            "concat",
            Sequelize.col("user_first_name"),
            " ",
            Sequelize.col("user_last_name"),
          ),
          {
            [Op.like]: `%${search}%`,
          },
        ),
        Sequelize.where(Sequelize.col("user_email"), {
          [Op.like]: `%${search}%`,
        }),
      ];
    }
    const getUserListQuery: any = {
      attributes: [
        "id",
        [
          sequelize.fn(
            "concat",
            sequelize.col("user_first_name"),
            " ",
            sequelize.col("user_last_name"),
          ),
          "user_full_name",
        ],
        "user_joining_date",
        "user_status",
        "user_email",
        "user_active_role_id",
        "web_user_active_role_id",
        "employment_number",
        [
          sequelize.literal(
            `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
          END`
          ),
          "user_avatar_link",
        ],
        "user_avatar",
        [sequelize.literal(`(User.user_avatar)`), "user_avatar_id"],
        [sequelize.literal(userTrackStatusQuery), "user_track_status"],
        [
          sequelize.literal(
            `(
              SELECT
                ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = User.id)))*100)/COUNT(*))
              FROM nv_playlist_media
              WHERE playlist_media_status = 'active' 
                  AND playlist_id IN (
                    SELECT id 
                    FROM nv_playlist 
                    WHERE id IN (SELECT playlist_id FROM nv_playlist_branch WHERE branch_id = User.branch_id AND playlist_branch_status = 'active' AND playlist_id = nv_playlist.id) AND 
                          id IN (SELECT playlist_id FROM nv_playlist_department WHERE department_id = User.department_id AND playlist_department_status = 'active' AND playlist_id = nv_playlist.id)
                    AND playlist_status = 'active')
          )`,
          ),
          "user_track_percentage",
        ],
        [sequelize.literal(contractQuery), "is_probation"],
        [
          sequelize.literal(`(
            CASE
              WHEN EXISTS (
                SELECT 1
                FROM nv_user_meta
                WHERE user_id = User.id
              ) 
              AND EXISTS (
                SELECT 1
                FROM nv_user_employment_contract
                WHERE user_id = User.id
                AND contract_status = 'active'
              )
              THEN TRUE
              ELSE FALSE
            END
          )`),
          "has_user_meta",
        ],
      ],
      where: whereObj,
      nest: true,
      raw: true,
      distinct: true, // Ensures unique users are counted correctly
      subQuery: false, // Prevents subquery issues that lead to incorrect count
      include: [
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "branch_name", "branch_color", "text_color"],
        },
        {
          model: Department,
          as: "department",
          attributes: ["id", "department_name"],
        },
        {
          model: UserEmploymentContract,
          as: "user_contract",
          attributes: [
            "id",
            "contract_status",
            "expire_date",
            "is_confirm_sign",
          ],
          where: contractWhere,
          order: [["createdAt", "desc"]],
          required: contract_status && contract_status != "" ? true : false,
        },
      ],
    };
    if (page && size) {
      getUserListQuery.limit = Number(limit);
      getUserListQuery.offset = Number(offset);
    }
    if (role_id) {
      getUserListQuery.include.push({
        model: UserRole,
        as: "user_roles",
        attributes: [
          "role_id",
          [
            sequelize.literal(
              `(SELECT role_name FROM nv_roles WHERE id=user_roles.role_id)`,
            ),
            "role_name",
          ],
        ],
        where: { role_id: { [Op.in]: role_id.split(",") } },
        required: true,
      });
    }
    // else {
    //   getUserListQuery.attributes.push([
    //     sequelize.literal(`(
    //       SELECT CONCAT('[', GROUP_CONCAT(JSON_OBJECT(
    //         'role_id', r.id,
    //         'role_name', r.role_name
    //       )), ']')
    //       FROM nv_user_roles ur
    //       JOIN nv_roles r ON ur.role_id = r.id
    //       WHERE ur.user_id = User.id
    //     )`),
    //     "roles",
    //   ]);
    //   // Add group by to the main query
    //   // getUserListQuery.group = ['User.id'];
    // }
    if (isRotaList && isRotaList == "true") {
      getUserListQuery.order = [["list_order", "asc"]];
    } else {
      getUserListQuery.order = [["createdAt", "desc"]];
    }

    const getUserList: any = await User.findAll(getUserListQuery);

    const findUserRoles = await UserRole.findAll({ where: { user_id: { [Op.in]: getUserList?.map((user: any) => user.id) } }, raw: true })
    const findRoles = await Role.findAll({ where: { id: { [Op.in]: findUserRoles?.map(r => r.role_id) } }, raw: true });

    const roleMap = new Map(findRoles.map(role => [role.id, { role_id: role.id, ...role }]));

    const userRoleMap: Record<number, any[]> = {};

    for (const ur of findUserRoles) {
      const role = roleMap.get(ur.role_id);
      if (role) {
        if (!userRoleMap[ur.user_id]) {
          userRoleMap[ur.user_id] = [];
        }
        userRoleMap[ur.user_id].push(role);
      }
    }

    if (getUserList.length > 0) {
      for (const user of getUserList) {
        user.user_roles = userRoleMap[user.id] || [];
      }
    }

    delete getUserListQuery.attributes;
    const getUserCount: any = await User.count(getUserListQuery);
    const { total_pages } = getPaginatedItems(size, page, getUserCount || 0);
    return res.status(StatusCodes.OK).json({
      status: true,
      userList: getUserList,
      message: res.__("SUCCESS_FETCHED"),
      count: getUserCount,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Logout User API
 * @param req
 * @param res
 * @returns
 */

const logOut = async (req: Request, res: Response) => {
  try {
    const { webAppToken, appToken } = req.body;
    const getUserDetail: any = await User.findOne({ attributes: ['id', 'organization_id'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true })
    let result: any = false;

    if (typeof webAppToken != "undefined")
      result =
        (
          await User.setHeaders(req).update(
            { webAppToken: "" },
            { where: { webAppToken: webAppToken, id: req.user.id } },
          )
        ).length > 0
          ? true
          : false;

    if (typeof appToken != "undefined")
      result =
        (
          await User.setHeaders(req).update(
            { appToken: "" },
            { where: { appToken: appToken, id: req.user.id } },
          )
        ).length > 0
          ? true
          : false;

    if (result) {
      await Activity.create({
        activity_table: "User",
        reference_id: getUserDetail.id,
        activity_type: activity_type.SUCCESS,
        activity_action: activity_action.LOGOUT,
        ip_address: req.headers?.["ip-address"],
        userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
        location: req.headers?.["location"],
        address: req.headers?.["address"],
        organization_id: getUserDetail.organization_id
          ? getUserDetail.organization_id
          : null,
        created_by: getUserDetail.id,
        updated_by: getUserDetail.id,
      } as any);
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_LOGOUT"),
      });
    } else {
      await Activity.create({
        activity_table: "User",
        reference_id: getUserDetail.id,
        activity_type: activity_type.FAILED,
        activity_action: activity_action.LOGOUT,
        ip_address: req.headers?.["ip-address"],
        userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
        address: req.headers?.["address"],
        location: req.headers?.["location"],
        organization_id: getUserDetail.organization_id
          ? getUserDetail.organization_id
          : null,
        created_by: getUserDetail.id,
        updated_by: getUserDetail.id,
      } as any);
      res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_LOGOUT"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * User is not active for the next 5 min then ask for a PIN API
 * @param req
 * @param res
 * @returns
 */

const reEnterPinAfterSometime = async (req: Request, res: Response) => {
  try {
    await generateRefreshToken(req, res);
    await generateSession(req, res);
  } catch (error) {
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  This API for switch user  current Role
 * @param req
 * @param res
 * @returns
 */

const switchUserRole = async (req: Request, res: Response) => {
  try {
    const { role_id } = req.body;
    const findRoleExist = await UserRole.findOne({
      where: { user_id: req.user.id, role_id: role_id },
      raw: true,
      nest: true,
    });
    if (findRoleExist) {
      const getCurrentRole: any = await getRoleName(role_id);
      const normalUser = [...NORMAL_USER];
      const adminSideUser = [...ADMIN_SIDE_USER];
      if (
        !adminSideUser.includes(getCurrentRole[0].role_name) &&
        req.headers["platform-type"] == "web"
      ) {
        return res
          .status(StatusCodes.FORBIDDEN)
          .json({ status: false, message: res.__("PERMISSION_DENIED") });
      }

      if (
        !normalUser.includes(getCurrentRole[0].role_name) &&
        (req.headers["platform-type"] == "ios" ||
          req.headers["platform-type"] == "android")
      ) {
        return res
          .status(StatusCodes.FORBIDDEN)
          .json({ status: false, message: res.__("PERMISSION_DENIED") });
      }

      const updateUserDetail = await User.update(
        req.headers["platform-type"] == "web"
          ? { web_user_active_role_id: role_id }
          : { user_active_role_id: role_id },
        { where: { id: req.user.id } },
      );
      if (updateUserDetail.length > 0) {
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("ROLE_SWITCHED_SUCCESSFULLY"),
        });
      } else {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("ROLE_NOT_SWITCHED"),
        });
      }
    } else {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("ROLE_NOT_FOUND"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getRoleList = async (req: Request, res: Response) => {
  try {
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserParentRole: any = await Role.findOne({
      where: {
        id:
          req.headers["platform-type"] == "web"
            ? getUserDetail.web_user_active_role_id
            : getUserDetail.user_active_role_id,
      },
      raw: true
    });
    const whereObj: any = {
      role_status: role_status.ACTIVE,
      id: {
        [Op.not]:
          req.headers["platform-type"] == "web"
            ? getUserDetail.web_user_active_role_id
            : getUserDetail.user_active_role_id, // Exclude req.user.active role id
      },
    };

    // Add condition when current role id is not 1
    if (
      (req.headers["platform-type"] == "web"
        ? getUserDetail.web_user_active_role_id
        : getUserDetail.user_active_role_id) !== 1
    ) {
      whereObj.parent_role_id = {
        [Op.gt]: getUserParentRole.parent_role_id,
      };
    }

    const roleList = await Role.findAll({
      where: whereObj,
      attributes: ["role_name", "id"], raw: true
    });
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: roleList.length > 0 ? roleList : [],
    });
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getActivityLog = async (req: Request, res: Response) => {
  try {
    const { page, size, search }: any = req.query;

    const { limit, offset } = getPagination(Number(page), Number(size));

    const whereObj: any = {
      organization_id: req.user.organization_id,
    };

    // Search
    if (search) {
      whereObj[Op.or] = [
        Sequelize.where(
          Sequelize.fn(
            "concat",
            Sequelize.col("user_first_name"),
            " ",
            Sequelize.col("user_last_name"),
          ),
          {
            [Op.like]: `%${search}%`,
          },
        ),
      ];
    }

    const activityObj: any = {
      include: [
        {
          model: User,
          as: "users",
          where: { organization_id: req.user.organization_id },
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("user_first_name"),
                " ",
                sequelize.col("user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            [
              sequelize.literal(
                `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = users.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', users.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = users.user_avatar))
          END`
              ),
              "user_avatar_link",
            ],
            "user_avatar",
            [sequelize.literal(`(users.user_avatar)`), "user_avatar_id"],
            "employment_number",
          ],
          include: [
            {
              model: Branch,
              as: "branch",
              attributes: ["id", "branch_name"],
              where: { organization_id: req.user.organization_id }
            },
            {
              model: Department,
              as: "department",
              attributes: ["id", "department_name"],
              where: { organization_id: req.user.organization_id }
            }
          ]
        },
      ],
      attributes: [
        "createdAt",
        "activity_action",
        "previous_data",
        "new_data",
        "location",
        "address",
        "ip_address",
        "activity_type",
        "activity_table",
        "userAgent",
      ],
      where: whereObj,
      order: [["createdAt", "DESC"]],
    };

    if (page && size) {
      activityObj.limit = Number(limit);
      activityObj.offset = Number(offset);
    }
    const { rows: activityLogData, count } =
      await Activity.findAndCountAll(activityObj);
    const activityLog = JSON.parse(JSON.stringify(activityLogData));
    // const count = await Activity.count({where:whereObj});
    if (activityLog.length > 0) {
      activityLog.forEach((log: any) => {
        log.activity_table = addSpacesBeforeCapitals(log.activity_table);
      });

      const { total_pages } = getPaginatedItems(size, page, count || 0);

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: activityLog,
        page: parseInt(page),
        size: parseInt(size),
        count: count,
        total_pages,
      });
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("FAIL_DATA_FETCHED"),
        data: [],
        page: 0,
        size: 0,
        count: 0,
        total_pages: 0,
      });
    }
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getPermissionList = async (req: Request, res: Response) => {
  try {
    const { role_id }: any = req.query;
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const user_role_id = !role_id
      ? getUserDetail.web_user_active_role_id
      : role_id;
    const getPermissionList: any = await Permission.findOne({
      attributes: [
        [
          Sequelize.literal(`
          CONCAT(
            '{',
            GROUP_CONCAT(
              CONCAT('"', module, '":', 
              CASE
                WHEN (permission & 2 = 2) THEN 2
                WHEN (permission & 1 = 1) THEN 1
                ELSE 0
              END)
            ),
            '}'
          )
        `),
          "modules_permissions",
        ],
      ],
      where: { role_id: user_role_id },
      raw: true,
    });

    const modulesPermissions = getPermissionList?.modules_permissions
      ? JSON.parse(getPermissionList.modules_permissions)
      : {};

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: modulesPermissions,
    });
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  User Login With pin
 * @param req
 * @param res
 * @returns
 */

const loginWithPin = async (req: Request, res: Response) => {
  try {
    const { user_pin = "", client_ip } = req.body;
    const getUserDetail: any = await User.findOne({
      where: {
        id: req.user.id,
        user_status: {
          [Op.not]: [user_status.CANCELLED, user_status.DELETED],
        },
      },
      raw: true,
      nest: true,
    });

    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    } else {
      const userRoles = await UserRole.findAll({
        where: { user_id: getUserDetail.id },
        include: [
          {
            model: Role,
            as: "role",
            attributes: ["id", "role_name"],
          },
        ],
        nest: true,
        raw: true,
      });
      const loginUserRoles =
        userRoles.length > 0
          ? _.map(userRoles, (userRole: any) => userRole.role.role_name)
          : [];

      const getCurrentRole: any = await getRoleName(
        getUserDetail.web_user_active_role_id,
      );
      const normalUser = [...NORMAL_USER];

      const getAppCurrentRole: any = await getRoleName(
        getUserDetail.user_active_role_id,
      );
      const adminSideUser = [...ADMIN_SIDE_USER];

      if (
        !loginUserRoles.some((item: any) => adminSideUser.includes(item)) &&
        req.headers["platform-type"] == "web"
      ) {
        return res
          .status(StatusCodes.FORBIDDEN)
          .json({ status: false, message: res.__("PERMISSION_DENIED") });
      }

      if (
        !loginUserRoles.some((item: any) => normalUser.includes(item)) &&
        (req.headers["platform-type"] == "ios" ||
          req.headers["platform-type"] == "android")
      ) {
        return res
          .status(StatusCodes.FORBIDDEN)
          .json({ status: false, message: res.__("PERMISSION_DENIED") });
      }
      const getUserRole = await UserRole.findAll({
        attributes: ["role_id"],
        where: { user_id: getUserDetail.id },
        raw: true,
        nest: true,
      });
      const loginUserDetail: any = {
        user_id: getUserDetail.id,
        email: getUserDetail.user_email,
        is_login_pin: getUserDetail.is_login_pin,
        user_roles:
          _.map(getUserRole, (userRole: any) => userRole.role_id) || [],
        user_status: getUserDetail.user_status,
        user_active_role:
          req.headers["platform-type"] == "web"
            ? getCurrentRole
            : getAppCurrentRole,
        updatedBy: getUserDetail.updated_by,
        profile_status: await checkUserProfileComplete(getUserDetail.id),
      };

      const isMatch = await comparePassword(
        user_pin,
        getUserDetail.user_login_pin,
      );
      if (!isMatch) {
        await Activity.create({
          activity_table: "User",
          reference_id: getUserDetail.id,
          activity_type: activity_type.FAILED,
          activity_action: activity_action.LOGIN,
          ip_address: client_ip,
          userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
          location: req.headers?.["location"],
          organization_id: getUserDetail.organization_id
            ? getUserDetail.organization_id
            : null,
          created_by: getUserDetail.id,
          updated_by: getUserDetail.id,
        } as any);
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("ERROR_INCORRECT_PIN") });
      }

      await Activity.create({
        activity_table: "User",
        reference_id: getUserDetail.id,
        activity_type: activity_type.SUCCESS,
        activity_action: activity_action.LOGIN,
        ip_address: client_ip,
        userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
        location: req.headers?.["location"],
        organization_id: getUserDetail.organization_id
          ? getUserDetail.organization_id
          : null,
        created_by: getUserDetail.id,
        updated_by: getUserDetail.id,
      } as any);

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_LOGIN"),
        user_data: loginUserDetail,
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const updateUserToken = async (req: Request, res: Response) => {
  try {
    const { webAppToken, appToken } = req.body

    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'webAppToken', 'appToken'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("ERROR_USER_NOT_FOUND"),
      });
    }

    let isUpdated: any
    if (webAppToken || appToken) {
      webAppToken
        ? (isUpdated = await User.setHeaders(req).update(
          { webAppToken: webAppToken },
          { where: { id: req.user.id } },
        ))
        : null;
      appToken
        ? (isUpdated = await User.setHeaders(req).update(
          { appToken: appToken },
          { where: { id: req.user.id } },
        ))
        : null;
      const updateWebAppToken: any = null;
      webAppToken
        ? (isUpdated = await User.setHeaders(req).update(
          { webAppToken: updateWebAppToken },
          {
            where: {
              webAppToken: webAppToken,
              id: { [Op.not]: req.user.id },
            },
          },
        ))
        : false;
      appToken
        ? (isUpdated = await User.setHeaders(req).update(
          { appToken: updateWebAppToken },
          {
            where: {
              appToken: appToken,
              id: { [Op.not]: req.user.id },
            },
          },
        ))
        : false;

      if (isUpdated.length > 0) {
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("SUCCESS_NOTIFICATION_TOKEN_UPDATED"),
        });
      } else {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("FAIL_TOKEN_UPDATATION"),
        });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getUserActivityLog = async (req: Request, res: Response) => {
  try {
    const { page, size, search, tab }: any = req.query;
    const { user_id }: any = req.params;
    const { limit, offset } = getPagination(Number(page), Number(size));

    const whereObj: any = {};
    if (tab == "own") {
      whereObj.reference_id = user_id;
      whereObj.activity_action = {
        [Op.in]: [activity_action.CREATED, activity_action.UPDATED],
      };
      whereObj.activity_table = "User";
    } else {
      whereObj.created_by = user_id;
    }
    // Search
    if (search) {
      whereObj[Op.or] = [
        Sequelize.where(
          Sequelize.fn(
            "concat",
            Sequelize.col("user_first_name"),
            " ",
            Sequelize.col("user_last_name"),
          ),
          {
            [Op.like]: `%${search}%`,
          },
        ),
        // { request_reason: { [Op.like]: `%${search}%` } },
      ];
    }

    const activityObj: any = {
      include: [
        {
          model: User,
          as: "users",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("user_first_name"),
                " ",
                sequelize.col("user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            "employment_number",
          ],
          where: { organization_id: req.user.organization_id }
        },
      ],
      attributes: [
        "createdAt",
        "activity_action",
        "activity_table",
        "previous_data",
        "new_data",
        "ip_address",
        "activity_type",
        "location",
        "address",
        "userAgent",
      ],
      where: whereObj,
      order: [["createdAt", "DESC"]],
    };

    if (page && size) {
      activityObj.limit = Number(limit);
      activityObj.offset = Number(offset);
    }

    const { rows: activityLogData, count } =
      await Activity.findAndCountAll(activityObj);
    const activityLog = JSON.parse(JSON.stringify(activityLogData));
    // const count = await Activity.count({where:whereObj});
    if (activityLog.length > 0) {
      activityLog.forEach((log: any) => {
        log.activity_table = addSpacesBeforeCapitals(log.activity_table);
      });

      const { total_pages } = getPaginatedItems(size, page, count || 0);

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: activityLog,
        page: parseInt(page),
        size: parseInt(size),
        count: count,
        total_pages,
      });
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("FAIL_DATA_FETCHED"),
        data: [],
        page: 0,
        size: 0,
        count: 0,
        total_pages: 0,
      });
    }
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const changeUserPassword = async (req: Request, res: Response) => {
  try {
    const {
      user_password,
      user_id,
      isNotify,
      reset_type,
      user_login_pin,
    }: any = req.body;
    const findUserRole: any = await Role.findOne({
      where: {
        id: req.user.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
      raw: true,
    });
    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(findUserRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const findUserDetail: any = await User.findOne({
      attributes: [
        "id",
        "user_first_name",
        "user_last_name",
        "user_email",
        "keycloak_auth_id",
        "is_login_pin",
      ],
      where: { id: user_id, organization_id: req.user.organization_id },
      raw: true,
    });
    const findTokenUser: any = await User.findOne({
      attributes: ["id", "user_first_name", "user_last_name"],
      where: { id: req.user.id, organization_id: req.user.organization_id },
      raw: true,
    });
    /** Check if user exist or not, else throw error */
    if (!findTokenUser || !findUserDetail) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ERROR_USER_NOT_FOUND"),
      });
    }
    if (reset_type == "password") {
      await User.setHeaders(req).update(
        {
          user_password: await encrypt(user_password),
          token_version: (findUserDetail.token_version + 1) % 256,
        },
        { where: { id: user_id } },
      );
      /** Publish queue for change user password */
      if (findUserDetail.keycloak_auth_id) {
        const message = {
          user_id: findUserDetail.keycloak_auth_id,
          user_password: user_password,
          type: "generate_password",
        };
        /** Publish a message to the "staff generate password" queue */
        const queue: any = RABBITMQ_QUEUE.STAFF_PASSWORD_PIN_GENERATE;
        await rabbitmqPublisher.publishMessage(queue, message);
      }
    } else if (reset_type == "pin") {
      if (findUserDetail.is_login_pin) {
        await User.setHeaders(req).update(
          {
            user_login_pin: await encrypt(user_login_pin),
            pin_token_version: (findUserDetail.pin_token_version + 1) % 256,
          },
          { where: { id: user_id } },
        );
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .send({ status: false, message: res.__("SET_PIN_FIRST") });
      }
    }
    if (isNotify == true || isNotify == "true") {
      const templateData: any = {
        name: `${findUserDetail.user_first_name} ${findUserDetail.user_last_name}`,
        admin_name: `${findTokenUser.user_first_name} ${findTokenUser.user_last_name}`,
        email: findUserDetail.user_email,
        ORGANIZATION_LOGO:
          global.config.API_UPLOAD_URL + "/email_logo/logo.png",
        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
        ADDRESS: EMAIL_ADDRESS.ADDRESS,
        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
        EMAIL: EMAIL_ADDRESS.EMAIL,
        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
        smtpConfig: "INFO",
      };
      if (reset_type == "pin") {
        templateData.pin = user_login_pin;
        templateData.mail_type = "reset_user_pin";
        await sendEmailNotification(templateData);
      }
      if (reset_type == "password") {
        templateData.password = user_password;
        templateData.mail_type = "reset_password_mail";
        await sendEmailNotification(templateData);
      }
    }
    if (reset_type == "pin") {
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_USER_PIN_UPDATED") });
    }
    if (reset_type == "password") {
      return res
        .status(StatusCodes.OK)
        .json({
          status: true,
          message: res.__("SUCCESS_USER_PASSWORD_UPDATED"),
        });
    }

    return res
      .status(StatusCodes.OK)
      .json({ status: true, message: res.__("SUCCESS_USER_PASSWORD_UPDATED") });
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const resetUserProfile = async (req: Request, res: Response) => {
  try {
    const findUserRole: any = await Role.findOne({ where: { id: req.user.web_user_active_role_id, role_status: role_status.ACTIVE }, raw: true })
    if (![ROLE_CONSTANT.SUPER_ADMIN,
    ROLE_CONSTANT.ADMIN,
    ROLE_CONSTANT.DIRECTOR,
    ROLE_CONSTANT.HR,
    ].includes(findUserRole?.role_name)) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }
    const { user_id }: any = req.body;
    const userReset = await User.setHeaders(req).update(
      {
        user_middle_name: null,
        date_of_birth: null,
        user_phone_number: null,
        user_signature: null,
        emergency_contact: null,
        pin_code: null,
        country: null,
        address_line1: null,
        address_line2: null,
        user_gender: null,
        user_gender_other: null,
        marital_status: null,
        marital_status_other: null,
        geo_country: null,
        geo_state: null,
        geo_city: null,
      } as any,
      { where: { id: user_id } },
    );
    if (userReset.length > 0) {
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_USER_RESET") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("FAIL_USER_RESET") });
    }
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getUserUpdateHistory = async (req: Request, res: Response) => {
  try {
    const { user_id }: any = req.params;
    const { page, size }: any = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));

    const activityObj: any = {
      include: [
        {
          model: User,
          as: "users",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("user_first_name"),
                " ",
                sequelize.col("user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            "employment_number",
          ],
        },
      ],
      order: [["createdAt", "DESC"]],
      where: {
        reference_id: user_id,
        activity_action: activity_action.UPDATED,
        activity_table: "User",
      },
    };

    if (page && size) {
      activityObj.limit = Number(limit);
      activityObj.offset = Number(offset);
    }
    const { count, rows: getDsrDetail } =
      await Activity.findAndCountAll(activityObj);
    // const count = await Activity.count({where:whereObj});
    if (getDsrDetail.length > 0) {
      const { total_pages } = getPaginatedItems(size, page, count || 0);
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: getDsrDetail,
        page: parseInt(page),
        size: parseInt(size),
        count: count,
        total_pages,
      });
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("FAIL_DATA_FETCHED"),
        data: [],
        page: 0,
        size: 0,
        count: 0,
        total_pages: 0,
      });
    }
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getGeoList = async (req: Request, res: Response) => {
  try {
    /* Get country */
    const { geo_type, parent_place, place_code, place_name } = req.query; // Use query parameters to determine type and parent place

    // Validate geo_type
    if (!place_code && !place_name) {
      if (!geo_type || ![1, 2, 3].includes(Number(geo_type))) {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .send({
            status: false,
            message: res.__("ERR_INVALID_TYPE"),
            type: "Geo Type",
          });
      }
    }

    const filter: any = {};
    // Prepare the filter object
    if (place_code) {
      // Fetch the geographical data
      filter.place_code = place_code;
    } else if (place_name) {
      filter.place_name = place_name;
    } else {
      // For region1 or region2, parent_place should be provided
      if (Number(geo_type) > 1) {
        if (!parent_place) {
          return res
            .status(StatusCodes.BAD_REQUEST)
            .send({
              status: false,
              message: res.__("ERR_MISSING_PARENT_PLACE"),
            });
        } else {
          filter.parent_place = parent_place;
        }
      }
      filter.geo_type = Number(geo_type);
    }

    // Fetch the geographical data
    const geoData = await Geo.findAll({ where: filter });

    // Check if data is found
    if (!geoData || geoData.length === 0) {
      return res
        .status(StatusCodes.OK)
        .send({
          status: false,
          message: res.__("FAIL_DATA_FETCHED"),
          type: `Geo Type ${geo_type}`,
        });
    }

    // Return the response
    return res
      .status(StatusCodes.OK)
      .send({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: geoData,
      });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const upateWorkSchedule = async (req: Request, res: Response) => {
  try {
    const { user_week_day = {}, user_id } = req.body; // Use query parameters to determine type and parent place

    const checkUserExist: any = await User.findOne({ attributes: ['id'], where: { id: user_id, organization_id: req.user.organization_id }, raw: true });

    if (!checkUserExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .send({ status: false, message: res.__("USER_NOT_EXIST") });
    }

    if (user_week_day) {
      const findWorkSchedule = await UserWeekDay.findOne({ where: { user_id: checkUserExist.id }, raw: true });
      if (!findWorkSchedule) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .send({
            status: false,
            message: res.__("USER_WORK_SCHEDULE_NOT_FOUND"),
          });
      }
      const updateWeekDays = await UserWeekDay.update(
        {
          monday: user_week_day.monday,
          tuesday: user_week_day.tuesday,
          wednesday: user_week_day.wednesday,
          thursday: user_week_day.thursday,
          friday: user_week_day.friday,
          saturday: user_week_day.saturday,
          sunday: user_week_day.sunday,
          user_weekday_status: user_weekday_status.ACTIVE,
          updated_by: req.user.id,
        },
        { where: { user_id: checkUserExist.id } },
      );
      if (updateWeekDays.length > 0) {
        return res
          .status(StatusCodes.OK)
          .send({
            status: true,
            message: res.__("USER_WORK_SCHEDULE_UPDATED_SUCCESSFULLY"),
          });
      } else {
        // Return the response
        return res
          .status(StatusCodes.BAD_REQUEST)
          .send({
            status: false,
            message: res.__("FAILED_TO_UPDATE_USER_WORK_SCHEDULE"),
          });
      }
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .send({
          status: false,
          message: res.__("FAILED_TO_UPDATE_USER_WORK_SCHEDULE"),
        });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const assignPolicy = async (req: any, res: Response) => {
  try {
    const {
      branch_ids = [],
      department_ids = [],
      user_ids = [],
      role_ids = [],
      policy_ids = [],
      assign_list_type,
      policy_type,
    }: any = req.body;
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const findUserRole: any = await Role.findOne({
      attributes: ['id', 'role_name'],
      where: { id: req.headers["platform-type"] == "web" ? getUserDetail.web_user_active_role_id : getUserDetail.user_active_role_id }, raw: true
    });

    // Fetch user IDs based on branch IDs, department IDs, role IDs, and user IDs
    const findUserObj: any = {
      user_status: {
        [Op.not]: [
          user_status.CANCELLED, user_status.DELETED
        ],
      },
      organization_id: req.user.organization_id
    }

    if (branch_ids.length > 0) {
      const condition = {
        branch_id: {
          [Op.in]: branch_ids,
        },
      };
      if (findUserObj[Op.or] !== undefined) {
        findUserObj[Op.or].push(condition);
      } else {
        findUserObj[Op.or] = [condition];
      }
    }

    if (department_ids.length > 0) {
      const condition = {
        department_id: {
          [Op.in]: department_ids,
        },
      };
      if (findUserObj[Op.or] !== undefined) {
        findUserObj[Op.or].push(condition);
      } else {
        findUserObj[Op.or] = [condition];
      }
    }
    if (assign_list_type == "all") {
      findUserObj.id = {
        [Op.not]: [req.user.id, 1],
      };
      findUserObj[Op.and] = [
        sequelize.literal(
          `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1,2))) `,
        ),
      ];

      if (
        (findUserRole &&
          findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER) ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
        (findUserRole.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER &&
          getUserDetail.branch_id)
      ) {
        findUserObj.branch_id = getUserDetail.branch_id;
        // Construct recursive query to find child roles
        const getChildRolesQuery = `
      WITH RECURSIVE ChildRoles AS (
        SELECT id, role_name, parent_role_id
        FROM nv_roles
        WHERE id = :activeRoleId
        UNION ALL
        SELECT r.id, r.role_name, r.parent_role_id
        FROM nv_roles r
        INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
      )
      SELECT id
      FROM ChildRoles
      WHERE id != :activeRoleId`;

        // Execute recursive query to find child roles
        const getChildRoles = await sequelize.query(getChildRolesQuery, {
          replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
          type: QueryTypes.SELECT,
        });

        // Build WHERE clause for user roles based on child roles
        let whereStr = "";
        getChildRoles.forEach((child_role: any, index: number) => {
          if (index > 0) {
            whereStr += " OR ";
          }
          whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = User.id)) > 0`;
        });

        if (whereStr) {
          findUserObj[Op.and].push(sequelize.literal(`(${whereStr})`));
        }
      }
    } else {
      if (user_ids.length > 0) {
        const condition = {
          id: {
            [Op.in]: [...new Set(user_ids)],
          },
        };
        if (findUserObj[Op.and] !== undefined) {
          findUserObj[Op.and].push(condition);
        } else {
          findUserObj[Op.and] = [condition];
        }
      }
    }

    if (role_ids.length > 0) {
      const condition = {
        id: {
          [Op.in]: [
            sequelize.literal(
              `select nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id In (${role_ids})`,
            ),
          ],
        },
      };
      if (findUserObj[Op.or] !== undefined) {
        findUserObj[Op.or].push(condition);
      } else {
        findUserObj[Op.or] = [condition];
      }
    }

    const getToUser: any = await User.findAll({
      attributes: ["id", "webAppToken", "appToken", "user_joining_date"],
      where: findUserObj,
      raw: true,
      nest: true,
    });

    if (getToUser.length > 0 && user_ids.length > 0) {
      if (policy_type == "holiday") {
        if (policy_ids.length > 0) {
          const removeUserIds = [];
          for (const policy_id of policy_ids) {
            const checkPolicyExist = await HolidayPolicy.findOne({ where: { id: policy_id, holiday_policy_status: holiday_policy_status.ACTIVE }, raw: true });
            if (checkPolicyExist) {
              for (const user of getToUser) {
                const findExistingAssigned = await UserHolidayPolicy.findOne({ where: { holiday_policy_id: checkPolicyExist?.id, user_id: user?.id }, raw: true });
                if (findExistingAssigned) {
                  removeUserIds.push(user?.id);
                  await UserHolidayPolicy.update(
                    {
                      user_holiday_policy_status:
                        user_holiday_policy_status.ACTIVE,
                    },
                    {
                      where: {
                        holiday_policy_id: policy_id,
                        user_id: user?.id,
                      },
                    },
                  );
                } else {
                  removeUserIds.push(user?.id);
                  await UserHolidayPolicy.create({
                    user_id: user?.id,
                    holiday_policy_id: policy_id,
                    created_by: req.user.id,
                    updated_by: req.user.id,
                  } as any);
                }
              }
            }
          }
          await UserHolidayPolicy.update(
            { user_holiday_policy_status: user_holiday_policy_status.INACTIVE },
            {
              where: {
                holiday_policy_id: { [Op.in]: policy_ids },
                user_id: { [Op.notIn]: removeUserIds },
              },
            },
          );
        }
      }
      if (policy_ids.length > 0) {
        if (policy_type == "leave") {
          for (const policy_id of policy_ids) {
            let policyIds: any = [];
            for (const user of getToUser) {
              const checkPolicyExist = await LeaveAccuralPolicy.findOne({
                attributes: ['id', 'leave_calender_year_start_from', 'stop_policy_accural_timewise_type', 'stop_policy_accural_timewise_value', 'leave_policy_end_date', 'leave_balance_based_on_emp_contract', 'leave_policy_accural', 'leave_type_id', 'effective_from_type', 'effective_after_type', 'effective_after_count'],
                where: { id: policy_id, status: status.ACTIVE }, raw: true
              });
              if (checkPolicyExist) {
                const findLeaveYear =
                  checkPolicyExist.leave_calender_year_start_from;
                const findLeaveType =
                  checkPolicyExist.stop_policy_accural_timewise_type ||
                  "yearly";
                const findLeaveObj =
                  checkPolicyExist.stop_policy_accural_timewise_value
                    ? JSON.parse(
                      checkPolicyExist.stop_policy_accural_timewise_value,
                    )
                    : [];
                const findLeaveEnd = checkPolicyExist.leave_policy_end_date;

                const currentYear = moment().year();
                const leaveBalanceBasedOnEmpContract: any =
                  checkPolicyExist.leave_balance_based_on_emp_contract;
                const findUserMeta: any = await UserMeta.findOne({
                  where: { user_id: user.id },
                });

                // Calculate the probation end date
                const probationEndDate = moment(user?.user_joining_date)
                  .clone()
                  .add(findUserMeta?.probation_length, "days");
                const isUserOnProbation = moment().isSameOrBefore(
                  moment(probationEndDate),
                  "day",
                );

                let baseDate;

                // Determine base date based on effective_from_type
                if (checkPolicyExist.effective_from_type == "date_of_joining") {
                  baseDate = moment(user?.user_joining_date);
                } else if (
                  checkPolicyExist.effective_from_type ==
                  "after_probation_end" &&
                  isUserOnProbation
                ) {
                  baseDate = moment(probationEndDate);
                }

                // Calculate the effective date
                const effectiveDate = moment(baseDate);
                if (checkPolicyExist.effective_after_type === "days") {
                  effectiveDate.add(
                    checkPolicyExist.effective_after_count,
                    "days",
                  );
                } else if (checkPolicyExist.effective_after_type === "months") {
                  effectiveDate.add(
                    checkPolicyExist.effective_after_count,
                    "months",
                  );
                }

                // Compare with the current date

                const findExistingAssigned = await UserLeavePolicy.findOne({
                  where: { leave_accural_policy_id: checkPolicyExist.id, user_id: user?.id }, raw: true
                });
                const findAllLeavePolicy = await LeaveAccuralPolicy.findAll({ attributes: ['id'], where: { leave_type_id: checkPolicyExist.leave_type_id, id: { [Op.not]: policy_id }, status: status.ACTIVE }, raw: true });

                const generalSettings = await getGeneralSettingObj(
                  req.user.organization_id,
                );
                if (findExistingAssigned) {
                  // Update user leave policy status
                  await UserLeavePolicy.update(
                    {
                      user_leave_policy_status: user_leave_policy_status.ACTIVE,
                    },
                    {
                      where: {
                        id: findExistingAssigned.id,
                      },
                    },
                  );
                  // Handle different accrual types
                  if (checkPolicyExist.leave_policy_accural) {
                    await handleLeaveAccrual(
                      findLeaveType,
                      findLeaveObj,
                      findExistingAssigned,
                      findUserMeta,
                      generalSettings,
                      currentYear,
                      req,
                      user,
                      leaveBalanceBasedOnEmpContract,
                      findLeaveYear,
                      findLeaveEnd,
                    );
                  }
                } else {
                  // Create new user leave policy
                  const userLeavePolicyCreated = await UserLeavePolicy.create({
                    user_id: user?.id,
                    leave_accural_policy_id: policy_id,
                    user_leave_policy_status: user_leave_policy_status.ACTIVE,
                    created_by: req.user.id,
                    updated_by: req.user.id,
                  } as any);

                  if (userLeavePolicyCreated) {
                    if (checkPolicyExist.leave_policy_accural) {
                      await handleLeaveAccrual(
                        findLeaveType,
                        findLeaveObj,
                        userLeavePolicyCreated,
                        findUserMeta,
                        generalSettings,
                        currentYear,
                        req,
                        user,
                        leaveBalanceBasedOnEmpContract,
                        findLeaveYear,
                        findLeaveEnd,
                      );
                    }
                  }
                }
                policyIds =
                  findAllLeavePolicy.length > 0
                    ? findAllLeavePolicy.map((policy) => policy.id)
                    : [];
              }
            }
            if (policyIds.length > 0) {
              await UserLeavePolicy.update(
                { user_leave_policy_status: user_leave_policy_status.INACTIVE },
                {
                  where: {
                    leave_accural_policy_id: { [Op.in]: policyIds }, // Exclude policy_id
                    user_id: {
                      [Op.in]: getToUser.map((user: any) => {
                        return user.id;
                      }),
                    },
                    user_leave_policy_status: user_leave_policy_status.ACTIVE,
                  },
                },
              );
            }
            await UserLeavePolicy.update(
              { user_leave_policy_status: user_leave_policy_status.INACTIVE },
              {
                where: {
                  leave_accural_policy_id: policy_id, // Exclude policy_id
                  user_id: {
                    [Op.notIn]: getToUser.map((user: any) => {
                      return user.id;
                    }),
                  },
                  user_leave_policy_status: user_leave_policy_status.ACTIVE,
                },
              },
            );
          }
        }
      }
    } else {
      if (policy_type == "holiday") {
        await UserHolidayPolicy.update(
          { user_holiday_policy_status: user_holiday_policy_status.INACTIVE },
          { where: { holiday_policy_id: { [Op.in]: policy_ids } } },
        );
      }
      if (policy_type == "leave") {
        await UserLeavePolicy.update(
          { user_leave_policy_status: user_leave_policy_status.INACTIVE },
          {
            where: {
              leave_accural_policy_id: { [Op.in]: policy_ids }, // Exclude policy_id
            },
          },
        );
      }
    }
    if (policy_type == "leave") {
      return res
        .status(StatusCodes.OK)
        .json({
          status: true,
          message: res.__("LEAVE_POLICY_UPDATED_SUCCESSFULLY"),
        });
    }
    if (policy_type == "holiday") {
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("HOLIDAY_POLICY_ASSIGN_UPDATED_SUCCESSFULLY") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Calculate storage usage for an organization
 * @param req
 * @param res
 * @returns
 */
const calculateStorageUsage = async (req: Request, res: Response) => {
  try {
    let { organization_id } = req.params;
    organization_id = organization_id
      ? organization_id
      : req.user.organization_id;

    // Get total storage usage in bytes
    const storageResult = await sequelize.query(
      `SELECT 
        SUM(item_size) as total_bytes,
        COUNT(*) as total_files
      FROM nv_items 
      WHERE item_organization_id = :organization_id 
      AND item_status = :item_status`,
      {
        replacements: {
          organization_id: organization_id || req.user.organization_id,
          item_status: item_status.ACTIVE,
        },
        type: QueryTypes.SELECT,
      },
    );

    const usage = storageResult[0] as any;
    const totalBytes = usage.total_bytes || 0;

    // Convert bytes to appropriate unit
    let size: number = totalBytes;
    let unit: string = "bytes";

    if (size > 1024 * 1024 * 1024) {
      size = parseFloat((size / (1024 * 1024 * 1024)).toFixed(2));
      unit = "GB";
    } else if (size > 1024 * 1024) {
      size = parseFloat((size / (1024 * 1024)).toFixed(2));
      unit = "MB";
    } else if (size > 1024) {
      size = parseFloat((size / 1024).toFixed(2));
      unit = "KB";
    }

    // Get file type distribution
    const fileTypeDistribution = await sequelize.query(
      `SELECT 
        item_type,
        COUNT(*) as count,
        SUM(item_size) as size
      FROM nv_items 
      WHERE item_organization_id = :organization_id 
      AND item_status = :item_status
      GROUP BY item_type`,
      {
        replacements: {
          organization_id: organization_id || req.user.organization_id,
          item_status: item_status.ACTIVE,
        },
        type: QueryTypes.SELECT,
      },
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: {
        total_size: size,
        unit: unit,
        raw_bytes: totalBytes,
        total_files: usage.total_files || 0,
        file_type_distribution: fileTypeDistribution,
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Get file from S3 by location
 * @param req
 * @param res
 * @returns
 */
const getFileFromS3 = async (req: Request, res: Response) => {
  try {
    let { location } = req.query;

    if (!location) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("MISSING_LOCATION_PARAMETER"),
      });
    }

    if (`${location}`.startsWith("/")) {
      location = `${location}`.substring(1);
    }

    // Get file information from database
    const fileItem = await Item.findOne({
      where: {
        item_location: location as string,
        item_status: item_status.ACTIVE,
      },
    });

    const fileSize = fileItem?.item_size ?? 0;
    const mimeType = fileItem?.item_mime_type;
    const isVideo = mimeType?.startsWith("video/");

    let logos_location = ``;

    if ([`email_logo`, 'admin'].includes(`${location}`?.split('/')[0])) {
      logos_location = `${location}`
    }

    if (!fileItem) {
      if (logos_location) {
        const response: any = await s3.send(
          new GetObjectCommand({
            Bucket: process.env.NODE_ENV! || "development",
            Key: logos_location
          }),
        );

        // Read the stream
        const chunks: Uint8Array[] = [];
        for await (const chunk of response.Body) {
          chunks.push(chunk);
        }
        const fileBuffer = Buffer.concat(chunks);

        // Determine if this is a media type that should be displayed inline
        const isMedia = isMediaType('image/png');

        // Set appropriate headers
        res.setHeader("Content-Type", 'image/png');

        // For media files, use inline disposition to display in browser
        // For other files, use attachment to download
        const lastIndex = `${logos_location}`?.split('/')[`${logos_location}`?.split('/').length - 1]
        if (isMedia) {
          res.setHeader(
            "Content-Disposition",
            `inline; filename="${lastIndex}"`,
          );
          // Add caching for media files to improve performance
          res.setHeader("Cache-Control", "public, max-age=31536000"); // Cache for 1 year
        } else {
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${lastIndex}"`,
          );
        }

        res.setHeader("Content-Length", fileBuffer.length);

        // Send the file
        return res.send(fileBuffer);
      } else {

        const fileCallbackPath = path.resolve(
          __dirname,
          "..",
          "uploads",
          ...`${location}`.split('/')
        );
        if (fs.existsSync(fileCallbackPath)) {
          const buffer = fs.readFileSync(fileCallbackPath);
          // Set content type based on file extension for local files
          const ext = path.extname(location as string).toLowerCase();
          const mimeType = getMimeType(ext);
          res.setHeader("Content-Type", mimeType || "application/octet-stream");
          res.setHeader("Cache-Control", "public, max-age=31536000"); // Cache for 1 year
          return res.send(buffer);
        } else {
          return res.status(StatusCodes.NOT_FOUND).json({
            status: false,
            message: res.__("FILE_NOT_FOUND"),
          });
        }
      }

    } else {
      // Get file from S3
      const response: any = await s3.send(
        new GetObjectCommand({
          Bucket: process.env.NODE_ENV! || "development",
          Key: logos_location != '' ? logos_location : fileItem?.item_location,
        }),
      );

      // Set appropriate headers
      res.setHeader("Content-Type", fileItem.item_mime_type);
      res.setHeader("Content-Length", fileSize);

      if (isVideo) {
        res.setHeader(
          "Content-Disposition",
          `inline; filename="${fileItem.item_name}"`,
        );
        res.setHeader("Cache-Control", "public, max-age=31536000");
        
        // Stream the video directly from S3
        response.Body.pipe(res);
      } else {
        // For non-video files, handle as before
        const chunks: Uint8Array[] = [];
        for await (const chunk of response.Body) {
          chunks.push(chunk);
        }
        const fileBuffer = Buffer.concat(chunks);

        const isMedia = isMediaType(fileItem.item_mime_type);

        if (isMedia) {
          res.setHeader(
            "Content-Disposition",
            `inline; filename="${fileItem.item_name}"`,
          );
          res.setHeader("Cache-Control", "public, max-age=31536000");
        } else {
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${fileItem.item_name}"`,
          );
        }

        return res.send(fileBuffer);
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Helper function to check if mime type represents media that should be displayed inline
const isMediaType = (mimeType: string): boolean => {
  const mediaTypes = ["image/", "video/", "audio/", "application/pdf"];

  return mediaTypes.some((type) => mimeType.startsWith(type));
};

// Helper function to determine mime type from file extension
const getMimeType = (extension: string): string => {
  const mimeTypes: { [key: string]: string } = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".gif": "image/gif",
    ".svg": "image/svg+xml",
    ".webp": "image/webp",
    ".mp4": "video/mp4",
    ".webm": "video/webm",
    ".mp3": "audio/mpeg",
    ".wav": "audio/wav",
    ".pdf": "application/pdf",
  };

  return mimeTypes[extension] || "";
};

const migrateLocaltoS3 = async (req: Request, res: Response) => {
  try {
    await migrateFilesToS3()

    return res.send({
      status: true,
      message: "migration completed"
    })
  } catch (error) {
    console.log(error)
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
}


/** Get User fields */
const getUserFields = async (req: Request, res: Response) => {
  try {
    const includeFields = [
      'employment_number', 'username', 'user_first_name', 'user_middle_name', 'user_last_name', 'user_email', 'date_of_birth', 'user_phone_number', 'emergency_contact', 'user_joining_date', 'country', 'user_gender', 'user_gender_other', 'marital_status', 'marital_status_other', 'branch_name',
      'department_name', 'contract_name', 'duration_type', 'working_hours', 'wage_type', 'wages_hours', 'fixed_types', 'contract_remark', 'has_holiday_entitlement', 'leave_days', 'place_of_work', 'tips_grade', 'expire_date', 'probation_length', 'general_template', 'department_template', 'additional_template', 'other', 'has_right_to_work_in_uk', 'is_uk_citizen', 'has_student_or_pg_loan', 'passport_no', 'permit_type', 'validity', 'issued_date', 'insurance_number', 'bank_account_name', 'bank_account_number', 'bank_sort_code', 'bank_society_name', 'bank_address', 'kin1_name', 'kin1_relation', 'kin2_mobile_number', 'kin1_address', 'kin2_name', 'kin2_relation', 'kin2_address', 'medical_disability', 'medical_disability_detail', 'professional1_name_contact', 'professional1_role_description', 'professional1_start_date', 'professional1_end_date', 'professional2_name_contact', 'professional2_role_description', 'professional2_start_date', 'another_job', 'private_pension', 'statementA', 'statementB', 'statementC', 'postgraduate_loan', 'statement_apply', 'is_current_information'
    ]; // Add more as needed

    const getFilteredAttributes = (Model: any) => {
      const modelAttrs = Object.keys(Model.getAttributes());
      const modelAttrSet = new Set(modelAttrs);

      return includeFields
        .filter(field => {
          if (Model === UserMeta && field === 'contract_name') {
            return false;
          }
          return modelAttrSet.has(field);
        })
        .map(key => ({
          key,
          label: key
            .replace(/_/g, " ")
            .replace(/\b\w/g, char => char.toUpperCase())
        }));
    }

    const filteredUserAttributes = getFilteredAttributes(User);
    const filteredBranchAttributes = getFilteredAttributes(Branch);
    const filteredDepartmentAttributes = getFilteredAttributes(Department);
    const filteredContractAttributes = getFilteredAttributes(ContractNameModel);
    const filteredUserMetaAttributes = getFilteredAttributes(UserMeta);
    const filterRightToWorkAttributes = getFilteredAttributes(RightToWorkCheckList);
    const filteredStarterFormAttributes = getFilteredAttributes(StarterForm);
    const filteredHrmcFormAttributes = getFilteredAttributes(HrmcForm);

    // Merge all three arrays into one
    const mergedAttributes = [
      ...filteredUserAttributes,
      { key: 'address', label: 'Address' },
      ...filteredBranchAttributes,
      ...filteredDepartmentAttributes,
      { key: 'system_access', label: 'System Access' }
    ];

    const merged2Attributes = [
      ...filteredContractAttributes,
      ...filteredUserMetaAttributes,
    ];

    const merged3Attributes = [
      ...filteredStarterFormAttributes,
      ...filteredHrmcFormAttributes,
    ];

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: {
        personal_details: mergedAttributes,
        employee_contract: merged2Attributes,
        right_to_work: filterRightToWorkAttributes,
        starter_form: merged3Attributes
      }
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/** store configuration of user field sequence */
const storeUserFieldSequence = async (req: Request, res: Response) => {
  try {
    const { user_field_order } = req.body;

    /** check if user field order already exists */
    const userFieldOrder = await UserFieldOrder.findOne({
      where: { organization_id: req.user.organization_id },
      order: [['createdAt', 'DESC']]
    });

    let userFieldSequence: any

    if (userFieldOrder) {
      userFieldSequence = await userFieldOrder.update({
        user_field_order: JSON.stringify(user_field_order),
        updated_by: req.user.id,
      });
    } else {
      userFieldSequence = await UserFieldOrder.create({
        user_field_order: JSON.stringify(user_field_order),
        organization_id: req.user.organization_id,
        created_by: req.user.id,
        updated_by: req.user.id,
      } as any);
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: userFieldSequence
    });

  } catch (error) {
    console.log(error)
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
}
const exportUsers = async (req: Request, res: Response) => {
  try {
    // Get the filters from request query params
    const {
      size,
      page,
      search,
      branch_id,
      role_id,
      department_id,
      isAdmin,
      status,
      contract_status,
      user_track_status,
      isRotaList,
      file_type
    }: any = req.query;

    const filterLabels: string[] = [];
    // Get includeFields from UserFieldOrder instead of req.body
    const userFieldOrder = await UserFieldOrder.findOne({
      where: { organization_id: req.user.organization_id },
      order: [['createdAt', 'DESC']]
    });

    const includeFields = userFieldOrder ? JSON.parse(userFieldOrder.user_field_order) : [];

    const { limit, offset } = getPagination(Number(page), Number(size));

    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'branch_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const findUserRole: any = await Role.findOne({
      where: { id: getUserDetail.web_user_active_role_id },
      raw: true,
    });
    /** Get all super admin user id's */

    const getUserId = await getSuperAdminUserId(req.user?.organization_id)

    const excludedIds = [...getUserId]; // Combine req.user.id with fetched IDs
    const whereObj: any = {
      user_status: {
        [Op.not]: [
          user_status.CANCELLED,
          user_status.DELETED,
        ],
      },
      id: {
        [Op.not]: excludedIds,
      },
      organization_id: req.user.organization_id,
    };

    /** Hide 1 developer user on production server */
    if (process.env.NEXT_NODE_ENV == "production") {
      whereObj.id = { [Op.not]: [1, 2] };
    }

    let contractWhere: any = {
      contract_status: contractStatus.ACTIVE,
    };
    const contractQuery = `  (
    IF (
      EXISTS (
        SELECT 1
        FROM nv_user_meta 
        WHERE user_id = User.id
        AND nv_user_meta.probation_length IS NOT NULL 
        AND nv_user_meta.probation_length != 0
        AND DATE_ADD(user_joining_date, INTERVAL nv_user_meta.probation_length DAY) > CURDATE()
      ), true, false
    )
  )`;

    if (contract_status && contract_status != "") {
      const currentDate = moment().toDate();
      const futureDate = moment().add(15, "days").toDate();

      switch (contract_status) {
        case "expired":
          contractWhere.expire_date = { [Op.lte]: currentDate };
          break;
        case "expiry-soon":
          contractWhere.expire_date = {
            [Op.gt]: currentDate,
            [Op.lte]: futureDate,
          };
          break;
        case "active":
          contractWhere.contract_status = contractStatus.ACTIVE;
          contractWhere.expire_date = {
            [Op.or]: [{ [Op.gte]: futureDate }, null],
          };
          contractWhere.is_confirm_sign = true;
          contractWhere = {
            ...contractWhere,
            [Op.and]: Sequelize.where(sequelize.literal(contractQuery), {
              [Op.eq]: 0,
            }),
          };
          break;
        case "probation":
          contractWhere.contract_status = contractStatus.ACTIVE;
          contractWhere.expire_date = {
            [Op.or]: [{ [Op.gte]: futureDate }, null],
          };
          contractWhere.is_confirm_sign = true;
          // Remove the contractQuery and instead use this condition to check for probation:
          contractWhere = {
            ...contractWhere,
            [Op.and]: Sequelize.where(
              Sequelize.literal(`
          (
            EXISTS (
              SELECT 1
              FROM nv_user_meta 
              WHERE user_id = User.id
              AND probation_length IS NOT NULL 
              AND probation_length != 0
              AND DATE_ADD(user_joining_date, INTERVAL probation_length DAY) > CURDATE()
            )
          )
        `),
              { [Op.eq]: true }, // Match true for users in probation
            ),
          };
          break;
        case "awaiting-signature":
          contractWhere.contract_status = contractStatus.ACTIVE;
          contractWhere.expire_date = {
            [Op.or]: [{ [Op.gte]: futureDate }, null],
          };
          contractWhere.is_confirm_sign = false;
          break;
        default:
          contractWhere.contract_status = contract_status;
          break;
      }
    }
    if (branch_id) {
      whereObj.branch_id = { [Op.in]: branch_id.split(",") };
      const branch = await Branch.findByPk(branch_id);
      if (branch) filterLabels.push(`Branch: ${branch.branch_name}`);
    }
    if (department_id) {
      whereObj.department_id = { [Op.in]: department_id.split(",") };
      const department = await Department.findByPk(department_id);
      if (department) filterLabels.push(`Department: ${department.department_name}`);
    }
    if (status) {
      whereObj.user_status = status;
      if (status) filterLabels.push(`User Status: ${status}`);
    }

    if (isAdmin == "true") {
      // whereObj.user_active_role_id = 2;
      whereObj[Op.and] = [
        sequelize.literal(
          `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id IN (2)))`,
        ),
      ];
    } else if (isAdmin == "false") {
      // whereObj.user_active_role_id = { [Op.not]: [1, 2] };
      whereObj[Op.and] = [
        sequelize.literal(
          `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1,2))) `,
        ),
      ];
    } else {
      whereObj[Op.and] = [
        sequelize.literal(
          `(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1))) `,
        ),
      ];
    }

    const userTrackStatusQuery = ` (
          CASE
              WHEN EXISTS (select status from nv_user_checklist where to_user_id = User.id AND from_user_id = User.id AND checklist_id = 3)
              THEN (select status from nv_user_checklist where to_user_id = User.id AND from_user_id = User.id AND checklist_id = 3)
              ELSE 'pending'
          END)`;

    if (user_track_status && user_track_status != "") {
      whereObj[Op.and].push(
        Sequelize.where(Sequelize.literal(userTrackStatusQuery), {
          [Op.eq]: `${user_track_status}`,
        })
      );
      filterLabels.push(`User Tracking Status: ${user_track_status}`);
    }

    if (
      findUserRole &&
      (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.SIGNATURE) &&
      getUserDetail.branch_id
    ) {
      whereObj.branch_id = getUserDetail.branch_id;
      // Construct recursive query to find child roles
      const getChildRolesQuery = `
        WITH RECURSIVE ChildRoles AS (
          SELECT id, role_name, parent_role_id
          FROM nv_roles
          WHERE id = :activeRoleId
          UNION ALL
          SELECT r.id, r.role_name, r.parent_role_id
          FROM nv_roles r
          INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
        )
        SELECT id
        FROM ChildRoles
        WHERE id != :activeRoleId`;

      // Execute recursive query to find child roles
      const getChildRoles = await sequelize.query(getChildRolesQuery, {
        replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
        type: QueryTypes.SELECT,
      });

      // Build WHERE clause for user roles based on child roles
      let whereStr = "";
      getChildRoles.forEach((child_role: any, index: number) => {
        if (index > 0) {
          whereStr += " OR ";
        }
        whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = User.id)) > 0`;
      });
      // Include only the logged-in user with their role
      if (whereStr) {
        whereStr += ` OR User.id=${req.user.id}`;
        whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
      }
    }
    // Search
    if (search) {
      whereObj[Op.or] = [
        Sequelize.where(
          Sequelize.fn(
            "concat",
            Sequelize.col("user_first_name"),
            " ",
            Sequelize.col("user_last_name"),
          ),
          {
            [Op.like]: `%${search}%`,
          },
        ),
        Sequelize.where(Sequelize.col("user_email"), {
          [Op.like]: `%${search}%`,
        }),
      ];
      filterLabels.push(`Search Term: ${search}`);
    }
    const getUserListQuery: any = {
      attributes: [
        "id",
        'user_first_name',
        'user_last_name',
        'user_middle_name',
        'username',
        "user_joining_date",
        "user_email",
        "employment_number",
        'geo_city',
        'geo_country',
        'geo_state',
        'date_of_birth',
        'marital_status_other',
        'marital_status',
        'user_gender_other',
        'user_gender',
        'emergency_contact',
        'user_phone_number',
        'pin_code',
        'address_line1',
        'address_line2',
        'country',
        [sequelize.literal(`(SELECT branch_name FROM nv_branches AS branch_name WHERE id = User.branch_id AND organization_id = '${req.user.organization_id}')`), 'branch_name'],
        [sequelize.literal(`(SELECT department_name FROM nv_departments AS department_name WHERE id = User.department_id AND organization_id = '${req.user.organization_id}')`), 'department_name'],
      ],
      where: whereObj,
      nest: true,
      raw: true,
      distinct: true, // Ensures unique users are counted correctly
      subQuery: false, // Prevents subquery issues that lead to incorrect count
      include: [
        {
          model: UserMeta,
          as: "user_meta",
          attributes: [
            [sequelize.literal(`(SELECT name FROM nv_contract_template AS contract_name WHERE id = user_meta.general_template AND organization_id = '${req.user.organization_id}')`), 'general_template'],
            [sequelize.literal(`(SELECT name FROM nv_contract_template AS contract_name WHERE id = user_meta.department_template AND organization_id = '${req.user.organization_id}')`), 'department_template'],
            [
              sequelize.literal(`(
                SELECT GROUP_CONCAT(name)
                FROM nv_contract_template
                WHERE FIND_IN_SET(id, user_meta.additional_template)
                AND organization_id = '${req.user.organization_id}'
              )`), 'additional_template'
            ],
            'other',
            'expire_date',
            'probation_length',
            'wages_hours',
            'tips_grade',
            'fixed_types',
            'duration_type',
            'wage_type',
            'contract_remark',
            'working_hours',
            'has_holiday_entitlement',
            'contract_remark',
            'leave_days',
            'place_of_work',
            'tips_grade',
            [sequelize.literal(`(SELECT contract_name FROM nv_contract_name AS contract_name WHERE id = user_meta.contract_name_id AND organization_id = '${req.user.organization_id}')`), 'contract_name'],
          ],
        },
        {
          model: UserEmploymentContract,
          as: "user_contract",
          attributes: [
            "id",
            "contract_status",
            "expire_date",
            "is_confirm_sign",
          ],
          where: contractWhere,
          order: [["createdAt", "desc"]],
          required: contract_status && contract_status != "" ? true : false,
        },
      ],
    };
    if (page && size) {
      getUserListQuery.limit = Number(limit);
      getUserListQuery.offset = Number(offset);
    }
    if (role_id) {
      getUserListQuery.include.push({
        model: UserRole,
        as: "user_roles",
        attributes: [
          "role_id",
          [
            sequelize.literal(
              `(SELECT role_name FROM nv_roles WHERE id=user_roles.role_id)`,
            ),
            "role_name",
          ],
        ],
        where: { role_id: { [Op.in]: role_id.split(",") } },
        required: true,
      });

      const role = await Role.findByPk(role_id);
      if (role) filterLabels.push(`Role: ${role.role_name}`);
    }
    if (isRotaList && isRotaList == "true") {
      getUserListQuery.order = [["list_order", "asc"]];
    } else {
      getUserListQuery.order = [["createdAt", "desc"]];
    }

    const getUserList: any = await User.findAll(getUserListQuery);

    const findUserRoles = await UserRole.findAll({ attributes: ['role_id', 'user_id'], where: { user_id: { [Op.in]: getUserList?.map((user: any) => user.id) } }, raw: true })
    const findRoles = await Role.findAll({ attributes: ['id', 'role_name'], where: { id: { [Op.in]: findUserRoles?.map(r => r.role_id) } }, raw: true })

    const roleMap = new Map(findRoles.map(role => [role.id, role]));

    const userRoleMap: Record<number, any[]> = {};

    for (const ur of findUserRoles) {
      const role = roleMap.get(ur.role_id);
      if (role) {
        if (!userRoleMap[ur.user_id]) {
          userRoleMap[ur.user_id] = [];
        }
        userRoleMap[ur.user_id].push(role);
      }
    }

    if (getUserList.length > 0) {
      for (const user of getUserList) {
        user.user_roles = userRoleMap[user.id] || [];

        /** Right to work check list */
        const getRightToWork: any = await RightToWorkCheckList.findOne({
          where: { user_id: user.id, checklist_id: 1 },
        });

        user.has_right_to_work_in_uk = getRightToWork?.has_right_to_work_in_uk == 0 ? 'No' : 'Yes';
        user.is_uk_citizen = getRightToWork?.is_uk_citizen == 0 ? 'No' : 'Yes';

        const getStarterForm: any = await StarterForm.findOne({
          where: { user_id: user.id, checklist_id: 2 },
        });

        const getHrmcForm: any = await HrmcForm.findOne({
          where: { user_id: user.id, checklist_id: 2 },
        });

        user.has_student_or_pg_loan = getStarterForm?.has_student_or_pg_loan == 0 ? 'No' : 'Yes';
        user.has_p45_form = getStarterForm?.has_p45_form == 0 ? 'No' : 'Yes';
        user.passport_no = getStarterForm?.passport_no || '';
        user.permit_type = getStarterForm?.permit_type || '';
        user.permit_type_other = getStarterForm?.permit_type_other || '';
        user.validity = getStarterForm?.validity || '';
        user.issued_date = getStarterForm?.issued_date || '';
        user.insurance_number = getHrmcForm?.insurance_number || '';
        user.bank_account_name = getStarterForm?.bank_account_name || '';
        user.bank_account_number = getStarterForm?.bank_account_number || '';
        user.bank_sort_code = getStarterForm?.bank_sort_code || '';
        user.bank_society_name = getStarterForm?.bank_society_name || '';
        user.bank_address = getStarterForm?.bank_address || '';
        user.kin1_name = getStarterForm?.kin1_name || '';
        user.kin1_relation = getStarterForm?.kin1_relation || '';
        user.kin2_mobile_number = getStarterForm?.kin2_mobile_number || '';
        user.kin1_address = getStarterForm?.kin1_address || '';
        user.kin2_name = getStarterForm?.kin2_name || '';
        user.kin2_relation = getStarterForm?.kin2_relation || '';
        user.kin2_address = getStarterForm?.kin2_address || '';
        user.kin2_mobile_number = getStarterForm?.kin2_mobile_number || '';
        user.medical_disability = getStarterForm?.medical_disability == 0 ? 'No' : 'Yes';
        user.medical_disability_detail = getStarterForm?.medical_disability_detail || '';
        user.professional1_name_contact = getStarterForm?.professional1_name_contact || '';
        user.professional1_role_description = getStarterForm?.professional1_role_description || '';
        user.professional1_start_date = getStarterForm?.professional1_start_date || '';
        user.professional1_end_date = getStarterForm?.professional1_end_date || '';
        user.professional2_name_contact = getStarterForm?.professional2_name_contact || '';
        user.professional2_role_description = getStarterForm?.professional2_role_description || '';
        user.professional2_start_date = getStarterForm?.professional2_start_date || '';
        user.another_job = getHrmcForm?.another_job == 0 ? 'No' : 'Yes';
        user.private_pension = getHrmcForm?.private_pension == 0 ? 'No' : 'Yes';
        user.postgraduate_loan = getHrmcForm?.postgraduate_loan == 0 ? 'No' : 'Yes';
        user.statementA = getHrmcForm?.statementA == 0 ? 'No' : 'Yes';
        user.statementB = getHrmcForm?.statementB == 0 ? 'No' : 'Yes';
        user.statementC = getHrmcForm?.statementC == 0 ? 'No' : 'Yes';
        user.is_current_information = getHrmcForm?.is_current_information == 0 ? 'No' : 'Yes';
        user.statement_apply = getHrmcForm?.statement_apply == 0 ? 'No' : 'Yes';
        user.payment_from = getHrmcForm?.payment_from == 0 ? 'No' : 'Yes';
      }



      const flattenedUserList = getUserList.map((user: any) => {
        return {
          ...user,
          ...(user.user_meta || {}) // Flatten user_meta if exists
        };

      });
      if (file_type == 'excel') {
        const workbook: any = await generateFile('excel');
        const worksheet: any = workbook.addWorksheet('User Report');

        worksheet.addRow(['User Report']).font = { bold: true, size: 14 };
        worksheet.mergeCells(`A1:${String.fromCharCode(65 + includeFields.length - 1)}1`);
        worksheet.addRow(['Generated At:', moment().format('DD MMMM YYYY, h:mm A')]);
        worksheet.addRow(['Generated By:', req.user.user_first_name + ' ' + req.user.user_last_name]);


        /** Show all applied filter */
        if (filterLabels.length > 0) {
          worksheet.addRow([]);
          worksheet.addRow(['Filters Applied:']).font = { bold: true };
          filterLabels.forEach((label) => worksheet.addRow([label]));
        }

        worksheet.addRow([]);

        /** Add headers **/
        const headerRow = worksheet.addRow([
          'Sequence No.',
          ...includeFields.map(
            (field: string) =>
              field
                .replace(/_/g, ' ')
                .replace(/\b\w/g, (char: string) => char.toUpperCase())
          )
        ]);
        headerRow.eachCell((cell: any) => {
          cell.alignment = { horizontal: 'center' };
          cell.font = { bold: true };
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '0070C0' } }; // Blue background
          cell.font.color = { argb: 'FFFFFF' };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        });


        /**Add user rows with index - based 'id' and handle 'system_access' separately **/
        flattenedUserList.forEach((user: any, index: number) => {
          const row = [index + 1, ...includeFields.map((field: any) => {
            if (field === 'system_access') {
              return user.user_roles?.map((r: any) => r.role_name).join(', ') || '';
            } else if (field === 'address') {
              if (user.address_line1 || user.address_line2 || user.pin_code || user.geo_city || user.geo_country || user.geo_state) {
                return user.address_line1 + ',' + user.address_line2 + ',' + user.pin_code + ',' + user.geo_city + ',' + user.geo_country + ',' + user.geo_state;
              } else {
                return '';
              }
            } else if (field === 'has_holiday_entitlement' && user.has_holiday_entitlement == 1) {
              return 'Yes';
            } else if (field === 'has_holiday_entitlement' && user.has_holiday_entitlement == 0) {
              return 'No';
            } else if (field === 'other') {
              if (user[field]) {
                return user[field].replace(/<[^>]*>/g, '').trim();
              } else {
                return '';
              }
            }
            else {
              return user[field] || '';
            }
          })];

          const dataRow = worksheet.addRow(row);

          dataRow.eachCell((cell: any) => {
            cell.alignment = {
              horizontal: 'center',
              vertical: 'middle',
              wrapText: true
            };
          });

          worksheet.getRow(dataRow.number).height = 20; // Optional: adjust for readability
        });

        /** Auto-adjust column widths **/
        worksheet.columns.forEach((column: any) => {
          let maxLength = 10;
          column.eachCell({ includeEmpty: false }, (cell: any) => {
            const columnLength = (cell.value || '').toString().length;
            maxLength = Math.max(maxLength, columnLength + 3); // Add minimal padding
          });
          column.width = Math.min(maxLength, 40); // Set a max width
        });
        const buffer = await workbook.xlsx.writeBuffer();
        res.setHeader('Content-Disposition', 'attachment; filename=report.xlsx');
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.send(buffer);
      } else if (file_type == 'csv') {
        const formattedData = flattenedUserList.map((user: any, index: number) => {
          const formattedUser: any = {
            sequence_no: index + 1 // ✅ Add Sequence/ID as first field
          };

          includeFields.forEach((field: any) => {
            if (field === 'system_access') {
              formattedUser[field] = user.user_roles?.map((r: any) => r.role_name).join(', ') || '';
            } else if (field === 'address') {
              if (user.address_line1 || user.address_line2 || user.pin_code || user.geo_city || user.geo_country || user.geo_state) {
                formattedUser[field] = user.address_line1 + ',' + user.address_line2 + ',' + user.pin_code + ',' + user.geo_city + ',' + user.geo_country + ',' + user.geo_state;
              } else {
                formattedUser[field] = '';
              }
            } else if (field === 'has_holiday_entitlement' && user.has_holiday_entitlement == 1) {
              formattedUser[field] = 'Yes';
            } else if (field === 'has_holiday_entitlement' && user.has_holiday_entitlement == 0) {
              formattedUser[field] = 'No';
            } else if (field === 'other') {
              if (user[field]) {
                formattedUser[field] = user[field].replace(/<[^>]*>/g, '').trim();
              } else {
                formattedUser[field] = '';
              }
            } else {
              formattedUser[field] = user[field] || '';
            }
          });

          return formattedUser;
        });
        const csvData = json2csv(formattedData, {
          escapeHeaderNestedDots: false,
          keys: [
            { field: 'sequence_no', title: 'Sequence No.' }, // ✅ Custom title
            ...includeFields.map((field: any) => ({
              field,
              title: field
                .replace(/_/g, ' ')
                .replace(/\b\w/g, (char: any) => char.toUpperCase())
            }))
          ]
        });
        // Prepare filter header
        const filterHeaderLines = [
          `"User Report"`,
          `"Generated At:","${moment().format('DD MMMM YYYY, h:mm A')}"`,
          `"Generated By:","${req.user.user_first_name} ${req.user.user_last_name}"`
        ];

        if (filterLabels.length > 0) {
          filterHeaderLines.push('', `"Filters Applied:"`);
          filterLabels.forEach(label => filterHeaderLines.push(`"${label}"`));
        }

        // Join header and CSV data
        const finalCSV = '\ufeff' + [...filterHeaderLines, '', csvData].join('\n');
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', 'attachment; filename="user_report.csv"');
        res.send(finalCSV);
      }
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("FAIL_DATA_NOT_FOUND"),
        data: getUserList,
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
}

/** Get stored user field order */
const getStoredUserFieldOrder = async (req: Request, res: Response) => {
  try {
    const getUserFieldOrder: any = await UserFieldOrder.findOne({ where: { organization_id: req.user.organization_id } });

    if (getUserFieldOrder) {
      // Parse the user_field_order JSON string
      getUserFieldOrder.user_field_order = JSON.parse(getUserFieldOrder.user_field_order);
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: getUserFieldOrder,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
}
export default {
  refreshToken,
  createUser,
  deleteUser,
  updateUser,
  resetPassword,
  setLoginPin,
  resetLoginPin,
  updateUserProfile,
  viewProfile,
  getUserById,
  getUserList,
  logOut,
  getRoleList,
  switchUserRole,
  reEnterPinAfterSometime,
  getActivityLog,
  getPermissionList,
  deleteUserAccount,
  loginWithPin,
  updateUserToken,
  getUserActivityLog,
  changeUserPassword,
  resetUserProfile,
  getUserUpdateHistory,
  getGeoList,
  upateWorkSchedule,
  assignPolicy,
  calculateStorageUsage,
  getFileFromS3,
  migrateLocaltoS3,
  getUserFields,
  exportUsers,
  storeUserFieldSequence,
  getStoredUserFieldOrder
};
