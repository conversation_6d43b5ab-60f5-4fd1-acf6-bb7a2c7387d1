"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { Branch } from "./Branch";

interface cardAttributes {
  id: number;
  card_name: string;
  card_number: string;
  card_status: string;
  branch_id: number;
  card_order: number;
  created_by: number;
  updated_by: number;
}

export enum card_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class Card
  extends Model<cardAttributes, never>
  implements cardAttributes {
  id!: number;
  card_name!: string;
  card_number!: string;
  card_status!: string;
  branch_id!: number;
  card_order!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Card.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    card_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    card_number: {
      type: DataTypes.STRING,
      defaultValue: false
    },
    branch_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    card_status: {
      type: DataTypes.ENUM,
      values: Object.values(card_status),
      defaultValue: card_status.ACTIVE,
    },
    card_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_cards",
    modelName: "Card",
  },
);



Card.belongsTo(Branch, { foreignKey: "branch_id", as: "branch" });
Branch.hasMany(Card, { foreignKey: "branch_id", as: "card_branch" });


// Define hooks for Card model
Card.addHook("afterUpdate", async (card: any) => {
  await addActivity("Card", "updated", card);
});

Card.addHook("afterCreate", async (card: Card) => {
  await addActivity("Card", "created", card);
});

Card.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

