"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface holidayTypeAttributes {
  id: number;
  holiday_type_name: string;
  holiday_type_status: string;
  holiday_type_description: string;
  has_holiday_type_default: string;
  organization_id: string;
  created_by: number;
  updated_by: number;
}

export enum holiday_type_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = "deleted"
}

export class HolidayType
  extends Model<holidayTypeAttributes, never>
  implements holidayTypeAttributes {
  id!: number;
  holiday_type_name!: string;
  holiday_type_status!: string;
  holiday_type_description!: string;
  has_holiday_type_default!: string;
  organization_id!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

HolidayType.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    holiday_type_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    holiday_type_status: {
      type: DataTypes.ENUM,
      values: Object.values(holiday_type_status),
      defaultValue: holiday_type_status.ACTIVE,
    },
    holiday_type_description: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    has_holiday_type_default: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: false
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_holiday_type",
    modelName: "HolidayType",
  },
);



// Define hooks for Card model
HolidayType.addHook("afterUpdate", async (holidayType: any) => {
  await addActivity("holidayType", "updated", holidayType);
});

HolidayType.addHook("afterCreate", async (holidayType: HolidayType) => {
  await addActivity("holidayType", "created", holidayType);
});

HolidayType.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
