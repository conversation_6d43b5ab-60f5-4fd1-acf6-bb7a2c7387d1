"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface healthSafetyAttributes {
  id: number;
  type: string;
  field: number;
  order: number;
  health_safety_category_id: number;
  status: string;
  created_by: number;
  updated_by: number;
}

export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum type {
  FIRE = "fire",
  FIRSTAID = "firstaid",
  SITEISSUE = "siteissue",
  INTRO = "intro",
}

export class HealthSafety
  extends Model<healthSafetyAttributes, never>
  implements healthSafetyAttributes {
  id!: number;
  type!: string;
  field!: number;
  order!: number;
  health_safety_category_id!: number;
  status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

HealthSafety.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    type: {
      type: DataTypes.ENUM,
      values: Object.values(type),
    },
    field: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(status),
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    health_safety_category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_health_safety_list",
    modelName: "HealthSafety",
  },
);

// HealthSafety.belongsTo(HealthSafetyCategory, {
//   foreignKey: "health_safety_category_id",
//   as: "health_safety_categorys",
// });
// HealthSafetyCategory.hasMany(HealthSafety, {
//   foreignKey: "health_safety_category_id",
//   as: "health_safety_category_list",
// });


// Define hooks for HealthSafety model
HealthSafety.addHook("afterUpdate", async (healthSafety: any) => {
  await addActivity("HealthSafety", "updated", healthSafety);
});

HealthSafety.addHook("afterCreate", async (healthSafety: HealthSafety) => {
  await addActivity("HealthSafety", "created", healthSafety);
});

HealthSafety.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

