import { Activity } from "../models/Activity";

const createActivityLog = async (
  user_id: any,
  created_by: any,
  actionType: any,
  ip_address?: any,
) => {
  const logParams: any = {
    user_id,
    created_by,
    updated_by: created_by,
    action: actionType,
  };

  if (ip_address) {
    logParams.ip_address = ip_address;
  }
  await Activity.create(logParams);
};

export { createActivityLog };
