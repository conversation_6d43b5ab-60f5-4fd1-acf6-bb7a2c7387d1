/* Body Background */
body {
    background-color: #ffffff;
    font-family: 'Arial', sans-serif;
}

/* Scheme Container */
.swagger-ui .scheme-container {
    background-color: #39596e;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px;
    color: #ffffff;
    /* White text */
}

/* Topbar Styling */
.swagger-ui .topbar {
    background-color: #39596e;
    border-bottom: 2px solid #2c4353;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    padding: 10px 20px;
    color: #ffffff;
    /* White text */
    font-weight: bold;
    /* Bold header */
}

/* Tag Styling */
.swagger-ui .opblock-tag.no-desc span.tag {
    color: #ffffff;
    /* White text */
    font-weight: bold;
    /* Bold header */
    text-transform: uppercase;
}

/* Tag Container */
.swagger-ui .opblock-tag.no-desc {
    background-color: #39596e !important;
    border-radius: 3px;
    padding: 5px 10px;
    color: #ffffff;
    /* White text */
    font-size: 12px;
    text-align: center;
}

/* Hide Default Swagger UI Logo */
.swagger-ui .topbar .swagger-ui-wrap .topbar-wrapper .topbar img {
    display: none;
}

/* Custom Logo Styling */
.topbar-wrapper img[alt="Swagger UI"],
.swagger-ui .topbar .link:after {
    content: "";
    background-image: url(https://namastevillage.teamtrainhub.com/images/logo_NV.png);
    background-size: contain;
    background-repeat: no-repeat;
    display: block;
    height: 50px;
    width: 150px;
    margin: 0 auto;
}

/* Hide Default Swagger UI SVG */
.swagger-ui .topbar svg:not(:root) {
    display: none;
}

/* Button Hover Effects */
.swagger-ui .btn {
    background-color: #39596e;
    color: #ffffff;
    /* White text */
    font-weight: bold;
    /* Bold text */
    border-radius: 5px;
    padding: 8px 15px;
    transition: background-color 0.3s ease;
}

.swagger-ui .btn:hover {
    background-color: #2c4353;
}

/* Input Fields */
.swagger-ui .input {
    border: 1px solid #39596e;
    padding: 8px 12px;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.swagger-ui .input:focus {
    border-color: #2c4353;
    outline: none;
}

/* Request/Response Block */
.swagger-ui .opblock {
    background: #f4f4f4;
    border-radius: 5px;
    padding: 15px;
    margin-top: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.swagger-ui .opblock:hover {
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

/* Section Headings */
.swagger-ui .section-heading {
    font-size: 20px;
    font-weight: bold;
    /* Bold header */
    color: #39596e;
    border-bottom: 2px solid #39596e;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

/* Response Example Box */
.swagger-ui .response_example {
    background-color: #e7f2f8;
    border-radius: 4px;
    padding: 15px;
    font-family: monospace;
    font-size: 14px;
}

/* OpBlock Summary */
.swagger-ui .opblock-summary {
    font-weight: bold;
    /* Bold text */
    color: #39596e;
}

/* OpBlock Method */
.swagger-ui .opblock-summary-method {
    color: #ffffff;
    /* White text */
    background-color: #39596e;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
}

/* OpBlock Title */
.swagger-ui .opblock-summary-path {
    color: #333333;
    font-size: 16px;
    font-weight: bold;
    /* Bold text */
}

/* Footer */
.swagger-ui .swagger-ui-footer {
    background-color: #39596e;
    color: #ffffff;
    /* White text */
    padding: 15px 0;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    /* Bold footer text */
}

.swagger-ui .swagger-ui-footer a {
    color: #ffffff;
    /* White text */
    text-decoration: none;
}

.swagger-ui .swagger-ui-footer a:hover {
    text-decoration: underline;
}

/* Footer Links */
.swagger-ui .swagger-ui-footer .footer-links {
    margin-top: 10px;
}

.swagger-ui .swagger-ui-footer .footer-links a {
    margin: 0 15px;
    font-size: 14px;
}

/* Custom Scrollbars */
.swagger-ui .swagger-ui-wrap {
    scrollbar-color: #39596e #ffffff;
}

.swagger-ui .swagger-ui-wrap::-webkit-scrollbar {
    width: 10px;
}

.swagger-ui .swagger-ui-wrap::-webkit-scrollbar-thumb {
    background-color: #39596e;
    border-radius: 10px;
}

.swagger-ui .swagger-ui-wrap::-webkit-scrollbar-track {
    background-color: #ffffff;
}

.swagger-ui .info {
    background-color: #f5faff;
    /* Light background */
    border: 2px solid #003764;
    /* Border color */
    border-radius: 8px;
    /* Rounded corners */
    padding: 20px;
    /* Spacing inside the info box */
    margin-bottom: 20px;
    /* Space below the info box */
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    /* Subtle shadow */
}

.swagger-ui .info h1 {
    color: #003764;
    /* Title color */
    font-family: 'Arial', sans-serif;
    /* Custom font */
    font-size: 32px;
    /* Larger font size */
    font-weight: bold;
    /* Bold text */
    margin-bottom: 10px;
    /* Space below the title */
}

.swagger-ui .info .description {
    color: #666666;
    /* Text color for description */
    font-size: 16px;
    /* Standard font size */
    line-height: 1.5;
    /* Line height for better readability */
    margin-top: 10px;
    /* Space above the description */
}

.swagger-ui .info .version {
    background-color: #003764;
    /* Background color for version */
    color: #ffffff;
    /* Text color for version */
    padding: 5px 10px;
    /* Padding for the version badge */
    border-radius: 5px;
    /* Rounded corners for version badge */
    font-size: 14px;
    /* Font size for version */
    display: inline-block;
    /* Display inline like a badge */
    margin-top: 10px;
    /* Space above the version badge */
}