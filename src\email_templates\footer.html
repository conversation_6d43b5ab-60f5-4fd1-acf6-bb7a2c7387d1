
<head>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 10px 20px; /* Adjust padding as needed for the footer area */
            box-sizing: border-box;
            width: 100%;
            font-size: 9px; /* Standard small font size for footer */
            color: #666666; /* Consistent with your detail text color */
        }
        .footer-container {
            width: 100%;
            border-top: 1px solid #dddddd; /* Consistent border style */
            padding-top: 5px; /* Space above first footer content */
        }
        .footer-row {
            width: 100%;
            margin-bottom: 4px; /* Space between footer rows */
        }
        .footer-row:last-child {
            margin-bottom: 0; /* No margin for the very last row */
        }
        .footer-row-confidentiality {
            text-align: center; /* Or 'left' if you prefer */
        }
        .footer-row-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .footer-generated-by {
            text-align: left;
        }
        .footer-page-info {
            text-align: right;
        }
        .footer-row-powered-by {
            text-align: center; /* Center the "Powered by" content */
            margin-top: 5px; /* A little extra space above this line */
            font-size: 8px; /* Even smaller font for this branding line */
            color: #888888; /* Slightly lighter color for subtlety */
        }
        .powered-by-logo {
            max-height: 12px; /* Make the logo tiny */
            max-width: 60px;  /* Prevent overly wide tiny logos */
            vertical-align: middle; /* Align logo nicely with the text */
            margin-left: 4px; /* Space between "Microffice" text and logo */
        }
        /* Classes for page numbers, often injected by the PDF generation tool */
        .pageNumber::after {
            content: counter(page);
        }
        .totalPages::after {
            content: counter(pages);
        }
    </style>
</head>
<body>
    <div class="footer-container">
        <div class="footer-row footer-row-confidentiality">
            {{#if CONFIDENTIALITY_STATEMENT}}
            <span>{{CONFIDENTIALITY_STATEMENT}}</span>
            {{else}}
            <span>Internal Use Only</span> <!-- Default statement -->
            {{/if}}
        </div>

        <div class="footer-row footer-row-details">
            <div class="footer-generated-by">
                {{#if GENERATED_BY_USER}}
                <span>Generated by: {{GENERATED_BY_USER}}</span>
                {{/if}}
            </div>
        </div>

        <div class="footer-row footer-row-powered-by">
            <span>Powered by Microffice</span>
            <img src="{{MICROFFICE_LOGO_URL}}" alt="Microffice" class="powered-by-logo">
        </div>
    </div>
</body>
</html>