"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface leaveApprovalPolicyAttributes {
    leave_accural_policy_id: number;
    leave_request_require_approval: boolean;
    leave_applicant_able_modify_chain: boolean;
    add_person_to_inform_about_leave: boolean;
}

export class LeaveApprovalPolicy
    extends Model<leaveApprovalPolicyAttributes, never>
    implements leaveApprovalPolicyAttributes {
    leave_accural_policy_id!: number;
    leave_request_require_approval!: boolean;
    leave_applicant_able_modify_chain!: boolean;
    add_person_to_inform_about_leave!: boolean;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

LeaveApprovalPolicy.init(
    {
        leave_accural_policy_id: {
            type: DataTypes.INTEGER
        },
        leave_request_require_approval: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        leave_applicant_able_modify_chain: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        add_person_to_inform_about_leave: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_leave_approval_policy",
        modelName: "LeaveApprovalPolicy",
    },
);

LeaveApprovalPolicy.removeAttribute("id");


LeaveApprovalPolicy.addHook("afterUpdate", async (LeaveApprovalPolicy: any) => {
    await addActivity("LeaveApprovalPolicy", "updated", LeaveApprovalPolicy);
});

LeaveApprovalPolicy.addHook("afterCreate", async (LeaveApprovalPolicy: LeaveApprovalPolicy) => {
    await addActivity("LeaveApprovalPolicy", "created", LeaveApprovalPolicy);
});

LeaveApprovalPolicy.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

