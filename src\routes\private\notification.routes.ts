import { Router } from "express";
import notificationController from "../../controller/notification.controller";
import { multerS3 } from "../../helper/upload.service";
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";
// import branchValidator from "../../validators/branch.validator";
const router: Router = Router();

// Initialize multerS3 uploader with proper error handling
const multerS3Upload = multerS3(
  process.env.NODE_ENV || "development",
  FILE_UPLOAD_CONSTANT.NOTIFICATION_FILES.folder,
);

router.post(
  "/send-notification",
  multerS3Upload.upload("notification_image"),
  notificationController.sendNotification,
);

router.get(
  "/get-staff-notification",
  notificationController.getNotificationList,
);

router.get(
  "/get-own-notification",
  notificationController.getOwnNotificationList,
);

router.get("/mark-as-read/:id", notificationController.markAsRead);

router.get(
  "/get-all-notification/:id",
  notificationController.getUserAllNotification,
);

export default router;
