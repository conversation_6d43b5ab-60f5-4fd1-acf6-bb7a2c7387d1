import { Segments, Joi, celebrate } from "celebrate";
export default {
  addDsr: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        dsr_date: Joi.date().required(),
        branch_id: Joi.number().required(),
        current_datetime: Joi.date().allow(null, ''),
        data: Joi.array(),
        dsr_amount_total: Joi.object(),
      }),
    }),
  updateDsr: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        data: Joi.array(),
        current_datetime: Joi.date().allow(null, ''),
        dsr_amount_total: Joi.object(),
        dsr_date: Joi.date().allow(null, ''),
        old_dsr_amount_total: Joi.object().allow(null, ''),
      }),
    }),
  addPaymentType: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        payment_type_title: Joi.string().required(),
        payment_type_status: Joi.string().required(),
        payment_type_usage: Joi.string().required(),
        has_weekly_use: Joi.boolean().valid(true, false).strict().required(),
        has_include_amount: Joi.boolean().valid(true, false).strict().required(),
        has_field_currency: Joi.boolean().valid(true, false).strict().required(),
      }),
    }),
  updatePaymentType: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        payment_type_title: Joi.string().required(),
        payment_type_status: Joi.string().required(),
        payment_type_usage: Joi.string().required(),
        has_weekly_use: Joi.boolean().valid(true, false).strict().required(),
        has_include_amount: Joi.boolean().valid(true, false).strict().required(),
        has_field_currency: Joi.boolean().valid(true, false).strict().required(),
      }),
    }),
  updatePaymentTypeOrder: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        payment_type_id: Joi.number().required(),
        order: Joi.number().required(),
      }),
    }),
  addPaymentTypeCategory: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        payment_type_category_title: Joi.string().required(),
        payment_type_category_status: Joi.string().required(),
        payment_type_category_pattern: Joi.string().valid('single', 'multiple').required(),
        payment_type_category_data_type: Joi.when('payment_type_category_pattern', {
          is: 'single',
          then: Joi.string().valid('string', 'integer').required(),
        }), // No forbidden, it just won't be validated when pattern is 'multiple'
        payment_type_category_fields: Joi.when('payment_type_category_pattern', {
          is: 'multiple',
          then: Joi.array()
            .items(
              Joi.object({
                field_name: Joi.string().required(),
                field_type: Joi.string().valid('string', 'integer').required(),
                field_limit: Joi.number().allow(null),
              })
            )
            .min(1).required(),
        }), // No forbidden, it just won't be validated when pattern is 'single'
        payment_type_category_remarks: Joi.string().allow(null, ''),
      }),
    }),
  updatePaymentTypeCategory: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        payment_type_category_title: Joi.string().required(),
        payment_type_category_status: Joi.string().required(),
        payment_type_category_pattern: Joi.string().valid('single', 'multiple').required(),
        payment_type_category_data_type: Joi.when('payment_type_category_pattern', {
          is: 'single',
          then: Joi.string().valid('string', 'integer').required(),
        }), // No forbidden, it just won't be validated when pattern is 'multiple'
        payment_type_category_fields: Joi.when('payment_type_category_pattern', {
          is: 'multiple',
          then: Joi.array()
            .items(
              Joi.object({
                field_id: Joi.number().allow(null),
                field_name: Joi.string().required(),
                field_type: Joi.string().valid('string', 'integer').required(),
                field_limit: Joi.number().allow(null),
              })
            )
            .min(1).required(),
        }), // No forbidden, it just won't be validated when pattern is 'single'
        payment_type_category_remarks: Joi.string().allow(null, ''),
      }),
    }),
  updatePaymentTypeCategoryOrder: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        payment_type_category_id: Joi.number().required(),
        order: Joi.number().required(),
      }),
    }),
  addPaymentTypeCategoryFieldValue: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        payment_type_category_id: Joi.number().required(),
        payment_type_category_value: Joi.array()
          .items(
            Joi.object({
              payment_type_category_field_id: Joi.number().required(),
              field_value: Joi.alternatives().try(Joi.string(), Joi.number()).required(),
            })
          )
          .min(1)
          .required(),
      }),
    }),
  updatePaymentTypeCategoryFieldValue: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        payment_type_category_value: Joi.array()
          .items(
            Joi.object({
              category_value_id: Joi.number().allow(null),
              field_value: Joi.alternatives().try(Joi.string(), Joi.number()).required(),
              payment_type_category_field_id: Joi.number().required(),
              payment_type_category_branch_id: Joi.number().required(),
            })
          )
          .min(1)
          .required(),
      }),
    }),
  makeDafalutActive: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        checked_category_ids: Joi.array().min(1).required(),
        branch_id: Joi.number().required()
      })
    }),
  updatePaymentTypeValueOrder: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        payment_type_category_branch_id: Joi.number().required(),
        order: Joi.number().required(),
        payment_type_category_id: Joi.number().required()
      }),
    }),
  addWsr: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        wsr_start_date: Joi.date().required(),
        wsr_end_date: Joi.date().required(),
        branch_id: Joi.number().required(),
        current_datetime: Joi.date().allow(null, ''),
        data: Joi.array(),
        wsr_amount_total: Joi.object(),
      }),
    }),
  updateWsr: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        data: Joi.array(),
        current_datetime: Joi.date().allow(null, ''),
        wsr_amount_total: Joi.object(),
        wsr_start_date: Joi.date().allow(null, ''),
        wsr_end_date: Joi.date().allow(null, ''),
        old_wsr_amount_total: Joi.object().allow(null, ''),
      }),
    })
}
