import { Router } from "express";
import {
    copyPermissions,
    createPermission,
    createRole,
    deletePermission,
    deleteRole,
    getPermissions,
    getRoles,
    updatePermission,
    updateRole,
    createModule,
    getModules,
    updateModule,
    deleteModule
} from "../../controller/roles.controller";
import rolesValidator from "../../validators/roles.validator";
import moduleValidator from "../../validators/module.validator";
const router: Router = Router();

// Role routes
router.post('/create', rolesValidator.createRole(), createRole);
router.get('/list', getRoles);
router.put('/update/:id', rolesValidator.updateRole(), updateRole);
router.delete('/delete/:id', deleteRole);

// Permission routes
router.post('/permissions', rolesValidator.createPermission(), createPermission);
router.put('/permissions', rolesValidator.updatePermission(), updatePermission);
router.delete('/permissions/:id', deletePermission);
router.get('/permissions/:id?', getPermissions);
router.post('/permissions/copy', rolesValidator.copyPermission(), copyPermissions);

// Module routes
router.post('/modules', moduleValidator.createModule(), createModule);
router.get('/modules', getModules);
router.put('/modules/:id', moduleValidator.updateModule(), updateModule);
router.delete('/modules/:id', deleteModule);

export default router;
