import { Segments, Joi, celebrate } from "celebrate";
export default {
  login: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        user_email: Joi.string().email().required(),
        user_password: Joi.string().required(),
        client_ip: Joi.string().allow(null, ""),
        login_type : Joi.string().valid('pin','password').allow(null,''),
        webAppToken :Joi.string().allow(null, ""), 
        appToken : Joi.string().allow(null, ""),
      }),
    }),
  forgotPassword: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        user_email: Joi.string().email().required(),
        forgot_type: Joi.string().allow(null, ""),
      }),
    }),
  forgotPasswordVerify: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        user_email: Joi.string().email().required(),
        new_password: Joi.string().required(),
        type: Joi.string().allow(null, ""),
      }),
    }),
  verifyOtp: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        user_email: Joi.string().email().required(),
        user_otp: Joi.number().required(),
      }),
    }),
  resendOtp: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        user_email: Joi.string().email().required(),
      }),
    }),
  forgotPin: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        user_email: Joi.string().email().required(),
        user_password: Joi.string().required(),
        new_pin: Joi.string().required(),
      }),
    }),
};
