"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { ExpenseRequest } from "./ExpenseRequest";
import { PaymentTypeCategory } from "./PaymentTypeCategory";

interface expenseItemRequestAttributes {
  id: number;
  expense_request_detail_id: number;
  payment_type_category_id: number
  expense_amount: number;
  reference_id: number;
  expense_request_item_status: string;
  old_expense_amount: number;
  created_by: number;
  updated_by: number;
}

export enum expense_request_item_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}


export class ExpenseItemRequest
  extends Model<expenseItemRequestAttributes, never>
  implements expenseItemRequestAttributes {
  id!: number;
  expense_request_detail_id!: number;
  payment_type_category_id!: number
  expense_amount!: number;
  reference_id!: number;
  expense_request_item_status!: string;
  old_expense_amount!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

ExpenseItemRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    expense_request_detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    expense_amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    reference_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    expense_request_item_status: {
      type: DataTypes.ENUM,
      values: Object.values(expense_request_item_status),
      defaultValue: expense_request_item_status.ACTIVE,
    },
    old_expense_amount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_expense_item_requests",
    modelName: "expenseItemRequest",
  },
);

ExpenseItemRequest.belongsTo(ExpenseRequest, { foreignKey: "expense_request_detail_id", as: "expense_item_request" });
ExpenseRequest.hasMany(ExpenseItemRequest, { foreignKey: "expense_request_detail_id", as: "expense_request_detail" });

ExpenseItemRequest.belongsTo(PaymentTypeCategory, { foreignKey: "payment_type_category_id", as: "expense_request_item_type" });
PaymentTypeCategory.hasMany(ExpenseItemRequest, { foreignKey: "payment_type_category_id", as: "expense_request_detail_type" });


// Define hooks for Card model
ExpenseItemRequest.addHook("afterUpdate", async (expenseItemRequest: any) => {
  await addActivity("expenseItem", "updated", expenseItemRequest);
});

ExpenseItemRequest.addHook("afterCreate", async (expenseItemRequest: ExpenseItemRequest) => {
  await addActivity("expenseItem", "created", expenseItemRequest);
});

ExpenseItemRequest.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

