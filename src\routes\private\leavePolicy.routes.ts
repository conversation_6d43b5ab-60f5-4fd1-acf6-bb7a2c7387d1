import { Router } from "express";
import leavePolicyController from "../../controller/LeavePolicy.controller";

const router: Router = Router();

// add leave policy 
router.post("/add-leave-policy", leavePolicyController.createLeavePolicy);

// update leave policy 
router.put("/update-leave-policy/:leave_accural_policy_id", leavePolicyController.updateLeavePolicy);

// get leave policy 
router.get("/get-leave-policy-by-id/:leave_accural_policy_id", leavePolicyController.getLeavePolicyById);

// remove leave policy 
router.delete("/remove-leave-policy/:leave_accural_policy_id", leavePolicyController.removeLeavePolicyFromLeaveType);

export default router;
