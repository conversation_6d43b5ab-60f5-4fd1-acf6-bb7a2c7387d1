"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface checkListAttributes {
  id: number;
  checkList_name: string;
  order: number;
  checklist_type: string;
  checklist_status: string;
  type: number;
  prefix: string;
  created_by: number;
  updated_by: number;
}

export enum checklist_type {
  JOINING = "joining",
  LEAVING = "leaving",
}

export enum checklist_status {
  ACTIVE = "active",
  INACTIVE = "inactive"
}

export class CheckList
  extends Model<checkListAttributes, never>
  implements checkListAttributes {
  id!: number;
  checkList_name!: string;
  order!: number;
  checklist_type!: string;
  type!: number;
  checklist_status!: string;
  prefix!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

CheckList.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    checkList_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    checklist_type: {
      type: DataTypes.ENUM,
      values: Object.values(checklist_type),
      allowNull: true,
    },
    checklist_status: {
      type: DataTypes.ENUM,
      values: Object.values(checklist_status),
      allowNull: true,
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    prefix: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_checklist",
    modelName: "CheckList",
  },
);

// Define hooks for checklist model
CheckList.addHook("afterUpdate", async (checkList: any) => {
  await addActivity("CheckList", "updated", checkList);
});

CheckList.addHook("afterCreate", async (checkList: CheckList) => {
  await addActivity("CheckList", "created", checkList);
});

CheckList.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
