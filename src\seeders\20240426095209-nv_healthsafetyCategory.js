"use strict";
const { QueryTypes } = require("sequelize");

module.exports = {
  async up(queryInterface, Sequelize) {
    const getHealthSafetyCategoryData = await queryInterface.sequelize.query(
      `SELECT * FROM nv_health_safety_category`,
      { type: QueryTypes.SELECT },
    );

    if (getHealthSafetyCategoryData.length === 0) {
      const insertData = [{
        category_name : 'Introduction to health & safety',
        status : 'active',
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        category_name :'Fire' ,
        status : 'active',
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),

      },
      {
        category_name : 'First AID',
        status : 'active',
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),

      },
      {
        category_name :'Site specific issues' ,
        status : 'active',
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      ,]


      await queryInterface.bulkInsert("nv_health_safety_category", insertData, {});
    }
  },

  async down(queryInterface, Sequelize) {
    // Add commands to revert seed here.
    // Example: await queryInterface.bulkDelete('People', null, {});
  },
};


