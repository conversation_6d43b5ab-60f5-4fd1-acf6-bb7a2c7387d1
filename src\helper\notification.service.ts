import { Client, HTTPError } from "onesignal-node";

//send push notification
const sendPushNotification = async (
  deviceId: any,
  content: any,
  data: any,
  type: any,
  header = "header",
) => {
  const client = new Client(
    global.config.SIGNAL_APP_ID,
    global.config.SIGNAL_API_KEY,
  );
  if (deviceId && deviceId != "") {
    let NotificationDetails: any;
    if (type && type == "Verify") {
      NotificationDetails = {
        name: global.config.SIGNAL_APP_NAME,
        sms_from: global.config.SIGNAL_SMS_FROM,
        contents: {
          en: content,
        },
        headings: { en: header },
        include_phone_numbers: [deviceId],
        data: { data: data, type: type },
      };
    } else {
      NotificationDetails = {
        contents: {
          en: content,
        },
        headings: { en: header },
        include_player_ids: typeof deviceId != "string" ? deviceId : [deviceId],
        data: { data: data, type: type },
      };
    }
    try {
      const response = await client.createNotification(NotificationDetails);
      console.log("response", response);
    } catch (e) {
      console.log("error", e);
      if (e instanceof HTTPError) {
        // When status code of HTTP response is not 2xx, HTTPError is thrown.
        console.log(e.statusCode);
        console.log(e.body);
      }
      return false;
    }
  }
  return true;
};

export { sendPushNotification };
