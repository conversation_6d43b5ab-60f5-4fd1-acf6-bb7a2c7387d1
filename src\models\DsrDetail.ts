"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface dsrDetailAttributes {
  id: number;
  user_id: number;
  branch_id: number
  dsr_date: string;
  dsr_detail_status: string;
  dsr_amount_total: string;
  created_by: number;
  updated_by: number;
}

export enum dsr_detail_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}

export class DsrDetail
  extends Model<dsrDetailAttributes, never>
  implements dsrDetailAttributes {
  id!: number;
  user_id!: number;
  branch_id!: number
  dsr_date!: string;
  dsr_detail_status!: string;
  dsr_amount_total!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

DsrDetail.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    branch_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    dsr_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    dsr_detail_status: {
      type: DataTypes.ENUM,
      values: Object.values(dsr_detail_status),
      defaultValue: dsr_detail_status.ACTIVE,
    },
    dsr_amount_total: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_dsr_details",
    modelName: "DsrDetail",
  },
);

// DsrDetail.belongsTo(Branch, { foreignKey: "branch_id", as: "dsr_branch" });
// Branch.hasMany(DsrDetail, { foreignKey: "branch_id", as: "branch" });

// DsrDetail.belongsTo(User, { foreignKey: "user_id", as: "dsr_user" });
// User.hasMany(DsrDetail, { foreignKey: "user_id", as: "user" });


// Define hooks for Card model
DsrDetail.addHook("afterUpdate", async (dsrDetail: any) => {
  await addActivity("DsrDetail", "updated", dsrDetail);
});

DsrDetail.addHook("afterCreate", async (dsrDetail: DsrDetail) => {
  await addActivity("DsrDetail", "created", dsrDetail);
});

DsrDetail.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
