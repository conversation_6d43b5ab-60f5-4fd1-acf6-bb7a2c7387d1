import { Router } from "express";
import sideLetterConfirmationController from "../../controller/sideLetterConfirmation.controller";
import { multerS3 } from "../../helper/upload.service";
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";
const router: Router = Router();

// Initialize multerS3 uploader with proper error handling
const multerS3Upload = multerS3(
  process.env.NODE_ENV || "development",
  FILE_UPLOAD_CONSTANT.SIDE_LETTER_FILES.folder,
);

// Route for uploading a side letter confirmation
router.post(
  "/add-side-letter",
  multerS3Upload.array("side_letter_file", 1),
  sideLetterConfirmationController.uploadSideLetterConfirmation,
);

// Route for getting side letter confirmations for a DSR
router.get(
  "/get-side-letter-list",
  sideLetterConfirmationController.getSideLetterConfirmations,
);

router.get(
  "/get-single-side-letter/:id",
  sideLetterConfirmationController.getSideLetterConfirmationById,
);

// Route for updating confirmation status (approve/reject)
router.put(
  "/update-status/:id",
  sideLetterConfirmationController.updateConfirmationStatus,
);

// Route for deleting a side letter confirmation
router.delete(
  "/delete-side-letter",
  sideLetterConfirmationController.deleteSideLetterConfirmation,
);

export default router;
